# iQraa - Système de Gestion de Bibliothèque

<p align="center">
  <img src="./attached_assets/iqraa.png" alt="iQraa Logo" width="200"/>
</p>

iQraa est un système complet de gestion de bibliothèque conçu pour fonctionner dans tous les environnements - du 100% hors ligne au totalement connecté. Avec une approche "offline-first", iQraa permet de gérer efficacement les collections de livres, les lecteurs, les prêts et les activités, tout en garantissant la synchronisation de vos données lorsqu'une connexion est disponible.

## 🌟 Caractéristiques

- **Gestion complète de bibliothèque**
  - Catalogage de livres avec métadonnées détaillées
  - Gestion des lecteurs et des prêts
  - Suivi des emplacements et de la disponibilité des livres
  - Historique des activités et journal d'événements

- **Architecture Offline-First**
  - Fonctionnalité complète même sans connexion internet
  - Synchronisation bidirectionnelle automatique des données
  - Résolution intelligente des conflits

- **Multi-environnement**
  - Application web (CapRover)
  - Application desktop (Electron)
  - Application mobile (Capacitor)

- **Multi-langue**
  - Support complet pour le français, l'arabe et l'anglais
  - Interface utilisateur entièrement traduite
  - Documentation multilingue

- **Sécurité intégrée**
  - Authentification sécurisée
  - Gestion des rôles (administrateur, bibliothécaire)
  - Protection des données personnelles

## 🚀 Démarrage rapide

### Prérequis

- Node.js (version 16+)
- NPM (version 8+)
- SQLite (installation locale)
- PostgreSQL (pour le déploiement en ligne, optionnel)

### Installation locale (développement)

```bash
# Clonez ce dépôt
git clone https://github.com/votre-organisation/iqraa.git
cd iqraa

# Installez les dépendances
npm install

# Démarrez l'application en mode développement
# Sous Windows
./new-start-local.bat

# Sous Linux/Mac
./new-start-local.sh
```

### Accès à l'application

Une fois démarrée, l'application est accessible à l'adresse :
- Frontend : http://localhost:5173
- API Backend : http://localhost:5000/api

### Comptes par défaut

- **Administrateur**: 
  - Identifiant: `admin`
  - Mot de passe: `admin123`

- **Bibliothécaire**:
  - Identifiant: `user`
  - Mot de passe: `user123`

## 🛠️ Options de déploiement

iQraa offre plusieurs options de déploiement pour s'adapter à vos besoins :

### 1. Application Desktop (Electron)

```bash
# Test de l'application Electron (Windows)
./new-test-electron.bat

# Test de l'application Electron (Linux/Mac)
./new-test-electron.sh

# Construction de l'application Electron pour distribution
./build-electron.sh
```

### 2. Application Web (CapRover)

```bash
# Déploiement sur une instance CapRover
./deploy-caprover.sh

# Options disponibles
./deploy-caprover.sh --help
```

Pour plus de détails, consultez [la documentation de déploiement CapRover](./docs/CAPROVER_DEPLOYMENT.md).

### 3. Application Mobile (Capacitor)

```bash
# Préparation du projet pour Capacitor
./build-capacitor.sh

# Test sur Android/iOS
npx cap run android
npx cap run ios
```

## 🗄️ Architecture de base de données

iQraa utilise une double architecture de base de données :

- **SQLite** pour le stockage local (offline-first)
- **PostgreSQL** pour le stockage en ligne (synchronisation)

La synchronisation bidirectionnelle est gérée automatiquement par le service `sync-service` qui s'occupe de la résolution de conflits et de la conversion des types de données.

## 🌐 Synchronisation des données

Le système de synchronisation fonctionne de manière bidirectionnelle :

1. **SQLite → PostgreSQL** : Les modifications locales sont envoyées au serveur
2. **PostgreSQL → SQLite** : Les modifications du serveur sont téléchargées localement

La synchronisation est déclenchée :
- Automatiquement à intervalles réguliers lorsqu'une connexion internet est disponible
- Manuellement via l'interface utilisateur
- À la connexion/déconnexion de l'utilisateur

## 📚 Structure de l'application

```
iQraa/
├── client/               # Frontend React
├── server/               # Backend Express
├── shared/               # Code partagé et schémas
├── electron/             # Configuration Electron
├── docs/                 # Documentation
├── scripts/              # Scripts utilitaires
└── *-deployment.sh       # Scripts de déploiement
```

## 🔄 Flux de travail de développement

1. Utilisez `new-start-local.bat` ou `new-start-local.sh` pour le développement local
2. Les modifications au code sont automatiquement rechargées
3. La base de données SQLite est créée et initialisée automatiquement

## 🤝 Contribution

Les contributions sont les bienvenues ! Voici comment vous pouvez contribuer :

1. Forkez le projet
2. Créez une branche pour votre fonctionnalité (`git checkout -b feature/amazing-feature`)
3. Committez vos changements (`git commit -m 'Add some amazing feature'`)
4. Poussez vers la branche (`git push origin feature/amazing-feature`)
5. Ouvrez une Pull Request

## 📜 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🙏 Remerciements

- Logo "iQraa" créé par l'équipe de conception
- Inspiré par les systèmes de gestion de bibliothèque traditionnels
- Construit avec React, Express, SQLite, PostgreSQL et bien d'autres technologies fabuleuses