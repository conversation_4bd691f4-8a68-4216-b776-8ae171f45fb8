#!/bin/bash

# Couleurs pour la sortie
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================================${NC}"
echo -e "${GREEN}🚀 Démarrage de l'application iQraa avec serveur local${NC}"
echo -e "${BLUE}========================================================${NC}"

# Vérifier les prérequis
echo -e "${YELLOW}🔍 Vérification des prérequis...${NC}"
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js n'est pas installé ou n'est pas dans le PATH.${NC}"
    echo "Veuillez installer Node.js depuis https://nodejs.org/"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ NPM n'est pas installé ou n'est pas dans le PATH.${NC}"
    exit 1
fi

# Vérifier si les dépendances sont installées
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}📦 Installation des dépendances...${NC}"
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Erreur lors de l'installation des dépendances.${NC}"
        exit 1
    fi
fi

# Nettoyer les processus existants
echo -e "${YELLOW}🔄 Nettoyage des processus existants...${NC}"
pkill -f "tsx server/index-sqlite.ts" 2>/dev/null || true
pkill -f "vite --config vite-local.config.ts" 2>/dev/null || true

# Attendre un peu pour que les processus se terminent
sleep 2

# Créer le fichier de base de données SQLite s'il n'existe pas
if [ ! -f "sqlite.db" ]; then
    echo -e "${YELLOW}🗄️ Initialisation de la base de données SQLite...${NC}"
    touch sqlite.db
fi

# Fonction pour nettoyer les processus à la sortie
cleanup() {
    echo -e "\n${YELLOW}🛑 Arrêt des processus...${NC}"
    pkill -f "tsx server/index-sqlite.ts" 2>/dev/null || true
    pkill -f "vite --config vite-local.config.ts" 2>/dev/null || true
    exit 0
}

# Capturer les signaux pour nettoyer proprement
trap cleanup SIGINT SIGTERM

# Démarrer le serveur backend
echo -e "${GREEN}🌐 Démarrage du serveur backend sur http://localhost:5000${NC}"
NODE_ENV=development DB_TYPE=sqlite PG_DISABLED=true SQLITE_PATH=./sqlite.db PORT=5000 npx tsx server/index-sqlite.ts &
BACKEND_PID=$!

# Attendre que le backend se lance
echo -e "${YELLOW}⏳ Attente du démarrage du backend (5 secondes)...${NC}"
sleep 5

# Vérifier si le serveur backend est démarré
if ! lsof -i:5000 &> /dev/null; then
    echo -e "${RED}⚠️ Le serveur backend ne semble pas démarré sur le port 5000${NC}"
    echo -e "${YELLOW}⏳ Attente supplémentaire (5 secondes)...${NC}"
    sleep 5
fi

# Démarrer le serveur frontend
echo -e "${GREEN}🖥️ Démarrage du serveur frontend sur http://localhost:5173${NC}"
NODE_ENV=development npx vite --config vite-local.config.ts &
FRONTEND_PID=$!

echo ""
echo -e "${GREEN}✅ Application démarrée avec succès!${NC}"
echo -e "${GREEN}👉 Frontend: http://localhost:5173${NC}"
echo -e "${GREEN}👉 Backend API: http://localhost:5000/api${NC}"
echo ""
echo -e "${BLUE}========================================================${NC}"
echo -e "${YELLOW}Comptes par défaut:${NC}"
echo -e "${YELLOW}  Admin: admin / admin123${NC}"
echo -e "${YELLOW}  Bibliothécaire: librarian / librarian123${NC}"
echo -e "${BLUE}========================================================${NC}"
echo -e "${YELLOW}⛔ Appuyez sur Ctrl+C pour arrêter l'application.${NC}"
echo -e "${BLUE}========================================================${NC}"

# Attendre que les processus se terminent
wait $BACKEND_PID $FRONTEND_PID
