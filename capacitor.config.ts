import { CapacitorConfig } from '@capacitor/cli';

// Définition de la configuration pour différents environnements
const isDev = process.env.NODE_ENV === 'development';
const isProd = process.env.NODE_ENV === 'production';

// URL de l'API centrale
const apiUrl = process.env.API_URL || 'https://api.iqraa-library.com';

// Configuration Capacitor améliorée
const config: CapacitorConfig = {
  appId: 'com.iqraa.app',
  appName: 'iQraa',
  webDir: 'dist',
  server: {
    androidScheme: 'https',
    // En mode développement, ajouter le serveur local pour tester
    url: isDev ? process.env.DEV_SERVER_URL : undefined,
    cleartext: isDev, // Autoriser le trafic non sécurisé en développement
  },
  plugins: {
    SplashScreen: {
      launchShowDuration: 3000,
      backgroundColor: '#166534', // Couleur de fond verte (vert feuille)
      showSpinner: true,
      androidSpinnerStyle: 'large',
      iosSpinnerStyle: 'large',
      spinnerColor: '#ffffff',
      launchAutoHide: true,
    },
    LocalNotifications: {
      smallIcon: "ic_stat_icon_config_sample",
      iconColor: "#166534",
    },
    CapacitorSQLite: {
      iosDatabaseLocation: 'Library/iqraa-db',
      androidDatabaseLocation: 'iqraa-db',
    }
  },
  android: {
    buildOptions: {
      keystorePath: process.env.ANDROID_KEYSTORE_PATH || 'my-release-key.keystore',
      keystorePassword: process.env.ANDROID_KEYSTORE_PASSWORD || 'votre-mot-de-passe',
      keystoreAlias: process.env.ANDROID_KEYSTORE_ALIAS || 'alias',
      keystoreAliasPassword: process.env.ANDROID_KEYSTORE_ALIAS_PASSWORD || 'votre-mot-de-passe',
    },
  },
  // Variables d'environnement personnalisées pour l'application
  cordova: {
    preferences: {
      // Variables d'environnement pour l'application mobile
      SYNC_API_ENDPOINT: apiUrl,
      SYNC_ENABLED: "true",
      PG_DISABLED: "true", // Toujours utiliser SQLite sur mobile
      APP_MODE: "mobile",
      DEFAULT_LANGUAGE: "fr",
    }
  },
};

export default config;