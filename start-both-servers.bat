@echo off
echo ========================================================
echo 🌐 IQRAA Manager - Starting Both Servers
echo ========================================================

echo 📋 Configuration:
echo   Server IP: ***********
echo   Backend Port: 5000
echo   Frontend Port: 5173

echo.
echo 🚀 Starting servers...

REM Start backend in a new window
echo 🔧 Starting backend server...
start "IQRAA Backend Server" cmd /k "echo Starting backend server... && npx cross-env NODE_ENV=development DB_TYPE=sqlite PG_DISABLED=true HOST=0.0.0.0 CORS_ORIGIN=* SERVER_IP=*********** npx tsx server/index-sqlite.ts"

REM Wait a bit for backend to start
echo ⏳ Waiting 8 seconds for backend to initialize...
timeout /t 8 /nobreak > nul

REM Start frontend in a new window
echo 🖥️ Starting frontend server...
start "IQRAA Frontend Server" cmd /k "echo Starting frontend server... && npx cross-env VITE_API_URL=http://***********:5000 VITE_SERVER_IP=*********** VITE_FRONTEND_PORT=5173 VITE_BACKEND_PORT=5000 npx vite --config vite.config.network.ts"

echo.
echo ✅ Both servers are starting in separate windows!
echo.
echo 🌐 Access URLs:
echo   👉 Frontend: http://***********:5173
echo   👉 Backend API: http://***********:5000/api
echo   👉 Health Check: http://***********:5000/health
echo.
echo 👤 Default Login:
echo   Admin: admin / admin123
echo   Librarian: librarian / librarian123
echo.
echo 📱 Mobile Access:
echo   Connect to same WiFi and open: http://***********:5173
echo.
echo ========================================================
echo ℹ️ Two separate command windows will open:
echo   - One for the backend server (port 5000)
echo   - One for the frontend server (port 5173)
echo.
echo 🛑 To stop the servers, close both command windows
echo ========================================================

pause
