@echo off
title IqraaManager - Installing Dependencies

echo.
echo ========================================
echo    IqraaManager - Dependency Installer
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo.
    echo Please install Node.js from: https://nodejs.org
    echo Download the LTS version and restart this script.
    echo.
    pause
    exit /b 1
)

echo Node.js version:
node --version
echo.

echo Installing dependencies...
echo This may take a few minutes...
echo.

npm install

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to install dependencies
    echo Please check your internet connection and try again.
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Dependencies installed successfully!
echo ========================================
echo.
echo You can now run start-iqraa.bat to start the server.
echo.
pause
