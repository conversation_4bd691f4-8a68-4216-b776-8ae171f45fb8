// IqraaManager Frontend Application
class IqraaManager {
    constructor() {
        this.API_BASE = window.location.origin;
        this.currentUser = null;
        this.sessionId = null;
        this.books = [];
        this.rentals = [];
        this.requests = [];
        
        this.init();
    }

    init() {
        console.log('🚀 IqraaManager initializing...');
        this.setupEventListeners();
        this.checkAuthStatus();
    }

    setupEventListeners() {
        // Login form
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                this.login(username, password);
            });
        }

        // Settings forms
        const addUserForm = document.getElementById('addUserForm');
        if (addUserForm) {
            addUserForm.addEventListener('submit', (e) => this.addUser(e));
        }

        const addBookForm = document.getElementById('addBookForm');
        if (addBookForm) {
            addBookForm.addEventListener('submit', (e) => this.addBook(e));
        }

        // Modal close on outside click
        window.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                e.target.style.display = 'none';
            }
        });
    }

    // API call helper with session management
    async apiCall(endpoint, options = {}) {
        try {
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };
            
            // Add session ID if available
            if (this.sessionId) {
                headers['X-Session-ID'] = this.sessionId;
            }
            
            const response = await fetch(`${this.API_BASE}${endpoint}`, {
                headers: headers,
                ...options
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }

    // Authentication methods
    async login(username, password) {
        try {
            this.showAlert('Logging in...', 'info', 'loginAlert');
            
            const result = await this.apiCall('/api/login', {
                method: 'POST',
                body: JSON.stringify({ username, password })
            });
            
            if (result.success) {
                this.currentUser = result.user;
                this.sessionId = result.sessionId || result.token;
                
                // Store session ID for persistence
                if (this.sessionId) {
                    localStorage.setItem('sessionId', this.sessionId);
                }
                
                console.log('✅ Login successful:', this.currentUser);
                this.showMainApp();
                this.loadAllData();
            } else {
                this.showAlert('Invalid credentials', 'error', 'loginAlert');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showAlert('Login failed. Please try again.', 'error', 'loginAlert');
        }
    }

    async logout() {
        try {
            await this.apiCall('/api/logout', { method: 'POST' });
        } catch (error) {
            console.error('Logout error:', error);
        }
        
        this.currentUser = null;
        this.sessionId = null;
        localStorage.removeItem('sessionId');
        this.showLoginForm();
    }

    async checkAuthStatus() {
        try {
            console.log('🔍 Checking authentication status...');
            
            // Try to get session ID from localStorage
            const storedSessionId = localStorage.getItem('sessionId');
            if (storedSessionId) {
                this.sessionId = storedSessionId;
                console.log('📱 Found stored session ID');
            }
            
            const result = await this.apiCall('/api/auth/status');
            
            if (result.success && result.authenticated && result.user) {
                console.log('✅ User already authenticated:', result.user);
                this.currentUser = result.user;
                this.showMainApp();
                this.loadAllData();
                return true;
            } else {
                console.log('❌ User not authenticated');
                // Clear invalid session
                this.sessionId = null;
                localStorage.removeItem('sessionId');
                this.showLoginForm();
                return false;
            }
        } catch (error) {
            console.error('Auth check error:', error);
            // Clear invalid session
            this.sessionId = null;
            localStorage.removeItem('sessionId');
            this.showLoginForm();
            return false;
        }
    }

    // UI Management
    showLoginForm() {
        document.getElementById('loginSection').classList.remove('hidden');
        document.getElementById('mainApp').classList.add('hidden');
    }

    showMainApp() {
        document.getElementById('loginSection').classList.add('hidden');
        document.getElementById('mainApp').classList.remove('hidden');
        
        // Update user info
        document.getElementById('userName').textContent = this.currentUser.name || this.currentUser.username;
        document.getElementById('userRole').textContent = this.currentUser.role.toUpperCase();
        
        // Setup navigation based on user role
        this.setupNavigation();
    }

    setupNavigation() {
        const dashboardTab = document.getElementById('dashboardTab');
        const requestsTab = document.getElementById('requestsTab');
        const activeRentalsTab = document.getElementById('activeRentalsTab');
        const usersTab = document.getElementById('usersTab');
        const systemTab = document.getElementById('systemTab');
        const rentalsTab = document.getElementById('rentalsTab');

        // Show/hide tabs based on user permissions
        if (this.currentUser.permissions.canViewDashboard) {
            dashboardTab.style.display = 'block';
        } else {
            dashboardTab.style.display = 'none';
        }

        if (this.currentUser.permissions.canApproveRentals) {
            requestsTab.style.display = 'block';
            activeRentalsTab.style.display = 'block';
            usersTab.style.display = 'block';
        } else {
            requestsTab.style.display = 'none';
            activeRentalsTab.style.display = 'none';
            usersTab.style.display = 'none';
        }

        // System tab only for admin
        if (this.currentUser.permissions.canManageUsers) {
            systemTab.style.display = 'block';
        } else {
            systemTab.style.display = 'none';
        }

        // Regular users see "My Rentals", admin/librarian don't
        if (this.currentUser.role === 'user') {
            rentalsTab.style.display = 'block';
        } else {
            rentalsTab.style.display = 'none';
        }
    }

    showAlert(message, type = 'info', containerId = 'alert') {
        const container = document.getElementById(containerId);
        if (!container) return;
        
        container.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
        
        // Auto-hide success messages
        if (type === 'success') {
            setTimeout(() => {
                container.innerHTML = '';
            }, 3000);
        }
    }

    // Tab Management
    showTab(tabName) {
        console.log(`🔄 Switching to tab: ${tabName}`);
        
        // Hide all tab contents
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(content => {
            content.classList.remove('active');
        });
        
        // Remove active class from all tabs
        const tabs = document.querySelectorAll('.tab');
        tabs.forEach(tab => {
            tab.classList.remove('active');
        });
        
        // Show selected tab content
        const selectedContent = document.getElementById(`${tabName}Content`);
        if (selectedContent) {
            selectedContent.classList.add('active');
        }
        
        // Add active class to selected tab
        const selectedTab = event?.target || document.querySelector(`[onclick="showTab('${tabName}')"]`);
        if (selectedTab) {
            selectedTab.classList.add('active');
        }
        
        // Load data for the tab with a small delay to ensure DOM is ready
        setTimeout(() => {
            this.loadTabData(tabName);
        }, 100);
    }

    async loadTabData(tabName) {
        console.log(`📊 Loading data for tab: ${tabName}`);
        
        try {
            switch (tabName) {
                case 'books':
                    await this.loadBooks();
                    break;
                case 'dashboard':
                    if (this.currentUser.permissions.canViewDashboard) {
                        await this.loadDashboard();
                    }
                    break;
                case 'requests':
                    if (this.currentUser.permissions.canApproveRentals) {
                        await this.loadRequests();
                    }
                    break;
                case 'activeRentals':
                    if (this.currentUser.permissions.canApproveRentals) {
                        await this.loadActiveRentals();
                    }
                    break;
                case 'users':
                    if (this.currentUser.permissions.canApproveRentals) {
                        await this.loadUsersTab();
                    }
                    break;
                case 'system':
                    if (this.currentUser.permissions.canManageUsers) {
                        await this.loadSystemTab();
                    }
                    break;
                case 'rentals':
                    await this.loadRentals();
                    break;
                case 'settings':
                    await this.loadSettings();
                    break;
            }
        } catch (error) {
            console.error(`Error loading ${tabName} data:`, error);
            this.showAlert(`Failed to load ${tabName} data`, 'error', `${tabName}Alert`);
        }
    }

    async loadAllData() {
        console.log('📊 Loading all initial data...');
        
        // Always load books first
        await this.loadBooks();
        
        // Load other data based on permissions
        if (this.currentUser.permissions.canViewDashboard) {
            await this.loadDashboard();
        }
        
        if (this.currentUser.permissions.canApproveRentals) {
            await this.loadRequests();
            await this.loadActiveRentals();
        }

        if (this.currentUser.role === 'user') {
            await this.loadRentals();
        }
    }

    // Data Loading Methods
    async loadBooks() {
        try {
            console.log('📚 Loading books...');
            const result = await this.apiCall('/api/books');
            
            if (result.success) {
                this.books = result.data || [];
                console.log(`✅ Loaded ${this.books.length} books`);
                this.renderBooks();
            } else {
                throw new Error(result.message || 'Failed to load books');
            }
        } catch (error) {
            console.error('Books loading error:', error);
            this.showAlert('Failed to load books: ' + error.message, 'error', 'booksAlert');
        }
    }

    async loadDashboard() {
        try {
            console.log('📊 Loading dashboard stats...');
            const result = await this.apiCall('/api/dashboard/stats');
            
            if (result.success) {
                console.log('✅ Dashboard stats loaded:', result.data);
                this.renderDashboard(result.data);
            } else {
                throw new Error(result.message || 'Failed to load dashboard');
            }
        } catch (error) {
            console.error('Dashboard loading error:', error);
            this.showAlert('Failed to load dashboard: ' + error.message, 'error', 'dashboardAlert');
        }
    }

    async loadRequests() {
        try {
            console.log('📋 Loading rental requests...');
            const result = await this.apiCall('/api/rental-requests');
            
            if (result.success) {
                this.requests = result.data || [];
                console.log(`✅ Loaded ${this.requests.length} requests`);
                this.renderRequests();
            } else {
                throw new Error(result.message || 'Failed to load requests');
            }
        } catch (error) {
            console.error('Requests loading error:', error);
            this.showAlert('Failed to load requests: ' + error.message, 'error', 'requestsAlert');
        }
    }

    async loadActiveRentals() {
        try {
            console.log('📖 Loading active rentals for management...');
            const result = await this.apiCall('/api/rentals');

            if (result.success) {
                // Filter only active rentals for the management view
                this.activeRentals = (result.data || []).filter(rental => rental.status === 'active');
                console.log(`✅ Loaded ${this.activeRentals.length} active rentals`);
                this.renderActiveRentals();
            } else {
                throw new Error(result.message || 'Failed to load active rentals');
            }
        } catch (error) {
            console.error('Active rentals loading error:', error);
            this.showAlert('Failed to load active rentals: ' + error.message, 'error', 'activeRentalsAlert');
        }
    }

    async loadRentals() {
        try {
            console.log('📖 Loading rentals...');
            const result = await this.apiCall('/api/rentals');

            if (result.success) {
                this.rentals = result.data || [];
                console.log(`✅ Loaded ${this.rentals.length} rentals`);
                this.renderRentals();
            } else {
                throw new Error(result.message || 'Failed to load rentals');
            }
        } catch (error) {
            console.error('Rentals loading error:', error);
            this.showAlert('Failed to load rentals: ' + error.message, 'error', 'rentalsAlert');
        }
    }

    // Rendering Methods
    renderBooks() {
        console.log('🎨 Rendering books...');
        const container = document.getElementById('booksGrid');

        if (!container) {
            console.error('❌ Books container not found!');
            return;
        }

        if (!this.books || this.books.length === 0) {
            container.innerHTML = '<div class="alert alert-info">No books available</div>';
            return;
        }

        container.innerHTML = this.books.map(book => `
            <div class="book-card">
                <h4>${book.title}</h4>
                <p><strong>Author:</strong> ${book.author}</p>
                <p><strong>Category:</strong> ${book.category}</p>
                <p><strong>Year:</strong> ${book.year}</p>
                <p><strong>Status:</strong>
                    <span class="status-badge ${book.status === 'available' ? 'status-approved' : 'status-pending'}">
                        ${book.status}
                    </span>
                </p>
                ${this.currentUser.permissions.canRequestRentals && book.status === 'available' ?
                    `<button onclick="app.requestBook(${book.id})" class="btn">Request Rental</button>` :
                    ''
                }
            </div>
        `).join('');

        console.log('✅ Books rendered successfully');
    }

    renderDashboard(stats) {
        console.log('🎨 Rendering dashboard...', stats);
        const container = document.getElementById('dashboardStats');

        if (!container) {
            console.error('❌ Dashboard container not found!');
            return;
        }

        if (!stats) {
            container.innerHTML = '<div class="alert alert-error">No dashboard data available</div>';
            return;
        }

        container.innerHTML = `
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>📚</h3>
                    <div class="number">${stats.totalBooks || 0}</div>
                    <div class="label">Total Books</div>
                </div>
                <div class="stat-card">
                    <h3>✅</h3>
                    <div class="number">${stats.availableBooks || 0}</div>
                    <div class="label">Available</div>
                </div>
                <div class="stat-card">
                    <h3>📖</h3>
                    <div class="number">${stats.borrowedBooks || 0}</div>
                    <div class="label">Borrowed</div>
                </div>
                <div class="stat-card">
                    <h3>👥</h3>
                    <div class="number">${stats.totalMembers || 0}</div>
                    <div class="label">Total Members</div>
                </div>
                <div class="stat-card">
                    <h3>🟢</h3>
                    <div class="number">${stats.activeMembers || 0}</div>
                    <div class="label">Active Sessions</div>
                </div>
                <div class="stat-card">
                    <h3>⏳</h3>
                    <div class="number">${stats.pendingRequests || 0}</div>
                    <div class="label">Pending Requests</div>
                </div>
                <div class="stat-card">
                    <h3>📋</h3>
                    <div class="number">${stats.activeRentals || 0}</div>
                    <div class="label">Active Rentals</div>
                </div>
                <div class="stat-card">
                    <h3>⚠️</h3>
                    <div class="number">${stats.overdueBooks || 0}</div>
                    <div class="label">Overdue Books</div>
                </div>
            </div>
        `;

        console.log('✅ Dashboard rendered successfully');
    }

    renderRequests() {
        console.log('🎨 Rendering requests...');
        const container = document.getElementById('requestsList');

        if (!container) {
            console.error('❌ Requests container not found!');
            return;
        }

        if (!this.requests || this.requests.length === 0) {
            container.innerHTML = '<div class="alert alert-info">No pending requests</div>';
            return;
        }

        container.innerHTML = this.requests.map(request => {
            // Find the book details
            const book = this.books.find(b => b.id === request.bookId);
            const bookTitle = book ? book.title : `Book ID: ${request.bookId}`;
            const bookAuthor = book ? book.author : 'Unknown Author';

            return `
                <div class="request-item">
                    <div class="request-header">
                        <div>
                            <div class="book-title">${bookTitle}</div>
                            <div class="book-author">by ${bookAuthor}</div>
                        </div>
                        <span class="status-badge status-${request.status}">${request.status}</span>
                    </div>
                    <p><strong>Requested by:</strong> ${request.username} (${request.userEmail})</p>
                    <p><strong>Request Date:</strong> ${new Date(request.requestDate).toLocaleDateString()}</p>
                    ${request.notes ? `<p><strong>Notes:</strong> ${request.notes}</p>` : ''}

                    ${request.status === 'pending' && this.currentUser.permissions.canApproveRentals ? `
                        <div style="margin-top: 15px;">
                            <button onclick="app.approveRequest(${request.id})" class="btn">Approve</button>
                            <button onclick="app.rejectRequest(${request.id})" class="btn btn-secondary">Reject</button>
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');

        console.log('✅ Requests rendered successfully');
    }

    renderActiveRentals() {
        console.log('🎨 Rendering active rentals...');
        const container = document.getElementById('activeRentalsList');

        if (!container) {
            console.error('❌ Active rentals container not found!');
            return;
        }

        if (!this.activeRentals || this.activeRentals.length === 0) {
            container.innerHTML = '<div class="alert alert-info">No active rentals</div>';
            return;
        }

        container.innerHTML = this.activeRentals.map(rental => {
            const dueDate = new Date(rental.dueDate);
            const today = new Date();
            const isOverdue = dueDate < today;
            const daysUntilDue = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));

            return `
                <div class="request-item">
                    <div class="request-header">
                        <div>
                            <div class="book-title">${rental.book.title}</div>
                            <div class="book-author">by ${rental.book.author}</div>
                        </div>
                        <span class="status-badge ${isOverdue ? 'status-rejected' : 'status-approved'}">${rental.status}</span>
                    </div>
                    <p><strong>Borrowed by:</strong> ${rental.username} (${rental.userEmail})</p>
                    <p><strong>Borrowed:</strong> ${new Date(rental.borrowDate).toLocaleDateString()}</p>
                    <p><strong>Due Date:</strong> ${dueDate.toLocaleDateString()}
                        ${isOverdue ?
                            `<span style="color: red; font-weight: bold;">(${Math.abs(daysUntilDue)} days overdue)</span>` :
                            `<span style="color: green;">(${daysUntilDue} days remaining)</span>`
                        }
                    </p>
                    <p><strong>Approved by:</strong> ${rental.approvedBy}</p>
                    ${rental.notes ? `<p><strong>Notes:</strong> ${rental.notes}</p>` : ''}

                    <div style="margin-top: 15px;">
                        <button onclick="app.returnBook(${rental.id})" class="btn">📚 Return Book</button>
                    </div>
                </div>
            `;
        }).join('');

        console.log('✅ Active rentals rendered successfully');
    }

    renderRentals() {
        console.log('🎨 Rendering rentals...');
        const container = document.getElementById('rentalsList');

        if (!container) {
            console.error('❌ Rentals container not found!');
            return;
        }

        if (!this.rentals || this.rentals.length === 0) {
            container.innerHTML = '<div class="alert alert-info">No active rentals</div>';
            return;
        }

        container.innerHTML = this.rentals.map(rental => `
            <div class="request-item">
                <div class="request-header">
                    <div>
                        <div class="book-title">${rental.book.title}</div>
                        <div class="book-author">by ${rental.book.author}</div>
                    </div>
                    <span class="status-badge status-approved">${rental.status}</span>
                </div>
                <p><strong>Borrowed:</strong> ${new Date(rental.borrowDate).toLocaleDateString()}</p>
                <p><strong>Due Date:</strong> ${new Date(rental.dueDate).toLocaleDateString()}</p>
                <p><strong>Approved by:</strong> ${rental.approvedBy}</p>
                ${rental.notes ? `<p><strong>Notes:</strong> ${rental.notes}</p>` : ''}
            </div>
        `).join('');

        console.log('✅ Rentals rendered successfully');
    }

    // Action Methods
    async requestBook(bookId) {
        try {
            const notes = prompt('Add any notes for your rental request (optional):');

            const result = await this.apiCall('/api/rental-requests', {
                method: 'POST',
                body: JSON.stringify({
                    bookId: bookId,
                    notes: notes || ''
                })
            });

            if (result.success) {
                this.showAlert('Rental request submitted successfully!', 'success', 'booksAlert');
                // Reload data to reflect changes
                await this.loadBooks();
                if (this.currentUser.permissions.canApproveRentals) {
                    await this.loadRequests();
                }
            } else {
                throw new Error(result.message || 'Failed to submit request');
            }
        } catch (error) {
            console.error('Request book error:', error);
            this.showAlert('Failed to submit request: ' + error.message, 'error', 'booksAlert');
        }
    }

    async approveRequest(requestId) {
        try {
            const notes = prompt('Add approval notes (optional):');

            const result = await this.apiCall(`/api/rental-requests/${requestId}`, {
                method: 'PATCH',
                body: JSON.stringify({
                    status: 'approved',
                    notes: notes || ''
                })
            });

            if (result.success) {
                this.showAlert('Request approved successfully!', 'success', 'requestsAlert');
                // Reload data
                await this.loadRequests();
                await this.loadBooks();
                if (this.currentUser.permissions.canViewDashboard) {
                    await this.loadDashboard();
                }
            } else {
                throw new Error(result.message || 'Failed to approve request');
            }
        } catch (error) {
            console.error('Approve request error:', error);
            this.showAlert('Failed to approve request: ' + error.message, 'error', 'requestsAlert');
        }
    }

    async rejectRequest(requestId) {
        try {
            const notes = prompt('Add rejection reason (optional):');

            const result = await this.apiCall(`/api/rental-requests/${requestId}`, {
                method: 'PATCH',
                body: JSON.stringify({
                    status: 'rejected',
                    notes: notes || ''
                })
            });

            if (result.success) {
                this.showAlert('Request rejected successfully!', 'success', 'requestsAlert');
                // Reload data
                await this.loadRequests();
                if (this.currentUser.permissions.canViewDashboard) {
                    await this.loadDashboard();
                }
            } else {
                throw new Error(result.message || 'Failed to reject request');
            }
        } catch (error) {
            console.error('Reject request error:', error);
            this.showAlert('Failed to reject request: ' + error.message, 'error', 'requestsAlert');
        }
    }

    async returnBook(rentalId) {
        try {
            const notes = prompt('Add return notes (optional - condition, damages, etc.):');

            const result = await this.apiCall(`/api/rentals/${rentalId}/return`, {
                method: 'PATCH',
                body: JSON.stringify({
                    notes: notes || ''
                })
            });

            if (result.success) {
                this.showAlert('Book returned successfully!', 'success', 'activeRentalsAlert');
                // Reload data to reflect changes
                await this.loadActiveRentals();
                await this.loadBooks();
                if (this.currentUser.permissions.canViewDashboard) {
                    await this.loadDashboard();
                }
            } else {
                throw new Error(result.message || 'Failed to return book');
            }
        } catch (error) {
            console.error('Return book error:', error);
            this.showAlert('Failed to return book: ' + error.message, 'error', 'activeRentalsAlert');
        }
    }

    // Users Tab Management
    async loadUsersTab() {
        try {
            console.log('👥 Loading users tab...');
            await this.loadUsersForTab();
        } catch (error) {
            console.error('Users tab loading error:', error);
            this.showAlert('Failed to load users: ' + error.message, 'error', 'usersAlert');
        }
    }

    async loadUsersForTab() {
        try {
            const result = await this.apiCall('/api/users');
            if (result.success) {
                this.renderUsersGrid(result.data);
            }
        } catch (error) {
            console.error('Users loading error:', error);
            this.showAlert('Failed to load users: ' + error.message, 'error', 'usersAlert');
        }
    }

    renderUsersGrid(users) {
        const container = document.getElementById('usersGrid');
        if (!container) return;

        container.innerHTML = users.map(user => `
            <div class="user-card ${user.status === 'disabled' ? 'disabled' : ''}">
                <div class="user-header">
                    <div class="user-info">
                        <h4>${user.name}</h4>
                        <p><strong>Username:</strong> ${user.username}</p>
                        <p><strong>Email:</strong> ${user.email}</p>
                        <p><strong>Role:</strong> ${user.role}</p>
                    </div>
                    <span class="user-status status-${user.status}">${user.status}</span>
                </div>
                <div class="user-actions">
                    ${user.status === 'active' ?
                        `<button onclick="app.disableUser(${user.id})" class="btn btn-small btn-disable">🚫 Disable</button>` :
                        `<button onclick="app.enableUser(${user.id})" class="btn btn-small btn-enable">✅ Enable</button>`
                    }
                    ${this.currentUser.permissions.canManageUsers && user.username !== 'admin' ?
                        `<button onclick="app.deleteUser(${user.id})" class="btn btn-small btn-danger">🗑️ Delete</button>` :
                        ''
                    }
                    ${!this.currentUser.permissions.canManageUsers && (user.role === 'librarian' || user.role === 'admin') ?
                        `<span class="text-muted" style="font-size: 0.8em;">Protected User</span>` :
                        ''
                    }
                </div>
            </div>
        `).join('');
    }

    // System Tab Management
    async loadSystemTab() {
        try {
            console.log('🖥️ Loading system tab...');
            await this.loadSystemInformation();
            await this.loadBackupHistory();
        } catch (error) {
            console.error('System tab loading error:', error);
            this.showAlert('Failed to load system information: ' + error.message, 'error', 'systemAlert');
        }
    }

    async loadSystemInformation() {
        try {
            const result = await this.apiCall('/api/system/info');
            if (result.success) {
                this.renderSystemInformation(result.data);
            }
        } catch (error) {
            console.error('System info loading error:', error);
        }
    }

    renderSystemInformation(info) {
        // Server Status
        const serverStatus = document.getElementById('serverStatus');
        if (serverStatus) {
            serverStatus.innerHTML = `
                <div class="system-stat">
                    <span class="stat-label">Status:</span>
                    <span class="stat-value">🟢 Online</span>
                </div>
                <div class="system-stat">
                    <span class="stat-label">Uptime:</span>
                    <span class="stat-value">${Math.floor(info.uptime / 3600)}h ${Math.floor((info.uptime % 3600) / 60)}m</span>
                </div>
                <div class="system-stat">
                    <span class="stat-label">Version:</span>
                    <span class="stat-value">${info.version}</span>
                </div>
            `;
        }

        // Performance Metrics
        const performanceMetrics = document.getElementById('performanceMetrics');
        if (performanceMetrics) {
            performanceMetrics.innerHTML = `
                <div class="system-stat">
                    <span class="stat-label">Active Sessions:</span>
                    <span class="stat-value">${info.activeSessions}</span>
                </div>
                <div class="system-stat">
                    <span class="stat-label">Total Books:</span>
                    <span class="stat-value">${info.totalBooks}</span>
                </div>
                <div class="system-stat">
                    <span class="stat-label">Active Rentals:</span>
                    <span class="stat-value">${info.totalRentals}</span>
                </div>
            `;
        }

        // Storage Information
        const storageInfo = document.getElementById('storageInfo');
        if (storageInfo) {
            storageInfo.innerHTML = `
                <div class="system-stat">
                    <span class="stat-label">Memory Used:</span>
                    <span class="stat-value">${info.memory.used}MB</span>
                </div>
                <div class="system-stat">
                    <span class="stat-label">Memory Total:</span>
                    <span class="stat-value">${info.memory.total}MB</span>
                </div>
                <div class="system-stat">
                    <span class="stat-label">Platform:</span>
                    <span class="stat-value">${info.platform}</span>
                </div>
            `;
        }
    }

    async loadBackupHistory() {
        const container = document.getElementById('backupHistory');
        if (container) {
            container.innerHTML = `
                <div class="backup-item">
                    <div>
                        <strong>No previous backups</strong>
                        <p>Create your first backup using the buttons above</p>
                    </div>
                </div>
            `;
        }
    }

    // Settings Management
    async loadSettings() {
        try {
            console.log('⚙️ Loading settings...');

            // Setup settings sections based on user permissions
            this.setupSettingsSections();

            // Load data for each section
            if (this.currentUser.permissions.canApproveRentals) {
                await this.loadRentalSettings();
                await this.loadOverdueStats();
            }

            this.loadUserProfile();

        } catch (error) {
            console.error('Settings loading error:', error);
            this.showAlert('Failed to load settings: ' + error.message, 'error', 'settingsAlert');
        }
    }

    setupSettingsSections() {
        const userMgmtSection = document.getElementById('userManagementSection');
        const bookMgmtSection = document.getElementById('bookManagementSection');
        const rentalMgmtSection = document.getElementById('rentalManagementSection');
        const returnMgmtSection = document.getElementById('returnManagementSection');

        // Hide user management and book management sections in settings
        // These are now handled in dedicated Users tab and Books tab
        userMgmtSection.style.display = 'none';
        bookMgmtSection.style.display = 'none';

        if (this.currentUser.permissions.canApproveRentals) {
            rentalMgmtSection.style.display = 'block';
            returnMgmtSection.style.display = 'block';
        } else {
            rentalMgmtSection.style.display = 'none';
            returnMgmtSection.style.display = 'none';
        }
    }

    async loadUsers() {
        try {
            const result = await this.apiCall('/api/users');
            if (result.success) {
                this.renderUsers(result.data);
            }
        } catch (error) {
            console.error('Users loading error:', error);
        }
    }

    async loadRentalSettings() {
        try {
            const result = await this.apiCall('/api/settings/rental');
            if (result.success) {
                document.getElementById('defaultRentalDays').value = result.data.defaultRentalDays;
                document.getElementById('maxRentalsPerUser').value = result.data.maxRentalsPerUser;
            }
        } catch (error) {
            console.error('Rental settings loading error:', error);
        }
    }

    async loadOverdueStats() {
        try {
            // Calculate overdue statistics from active rentals
            const overdueRentals = this.activeRentals?.filter(rental => {
                const dueDate = new Date(rental.dueDate);
                return dueDate < new Date() && rental.status === 'active';
            }) || [];

            const container = document.getElementById('overdueStats');
            container.innerHTML = `
                <div class="system-stat">
                    <span class="stat-label">Total Overdue Books:</span>
                    <span class="stat-value">${overdueRentals.length}</span>
                </div>
                <div class="system-stat">
                    <span class="stat-label">Most Overdue:</span>
                    <span class="stat-value">${overdueRentals.length > 0 ?
                        Math.max(...overdueRentals.map(r => Math.floor((new Date() - new Date(r.dueDate)) / (1000 * 60 * 60 * 24)))) + ' days' :
                        'None'}</span>
                </div>
            `;
        } catch (error) {
            console.error('Overdue stats loading error:', error);
        }
    }

    async loadSystemInfo() {
        try {
            const result = await this.apiCall('/api/system/info');
            if (result.success) {
                this.renderSystemInfo(result.data);
            }
        } catch (error) {
            console.error('System info loading error:', error);
        }
    }

    loadUserProfile() {
        document.getElementById('userDisplayName').value = this.currentUser.name || '';
        document.getElementById('userEmail').value = this.currentUser.email || '';
        document.getElementById('lastUpdated').textContent = new Date().toLocaleDateString();
    }

    renderUsers(users) {
        const container = document.getElementById('usersList');
        if (!container) return;

        container.innerHTML = users.map(user => `
            <div class="user-item">
                <div class="user-info">
                    <h5>${user.name}</h5>
                    <p><strong>Username:</strong> ${user.username} | <strong>Role:</strong> ${user.role}</p>
                    <p><strong>Email:</strong> ${user.email}</p>
                </div>
                <div class="user-actions">
                    <button onclick="app.editUser(${user.id})" class="btn btn-small">✏️ Edit</button>
                    ${user.username !== 'admin' ?
                        `<button onclick="app.deleteUser(${user.id})" class="btn btn-small btn-danger">🗑️ Delete</button>` :
                        ''
                    }
                </div>
            </div>
        `).join('');
    }

    renderSystemInfo(info) {
        const container = document.getElementById('systemInfo');
        if (!container) return;

        container.innerHTML = `
            <div class="system-stat">
                <span class="stat-label">Server Uptime:</span>
                <span class="stat-value">${Math.floor(info.uptime / 3600)}h ${Math.floor((info.uptime % 3600) / 60)}m</span>
            </div>
            <div class="system-stat">
                <span class="stat-label">Active Sessions:</span>
                <span class="stat-value">${info.activeSessions}</span>
            </div>
            <div class="system-stat">
                <span class="stat-label">Memory Usage:</span>
                <span class="stat-value">${info.memory.used}MB / ${info.memory.total}MB</span>
            </div>
            <div class="system-stat">
                <span class="stat-label">Node.js Version:</span>
                <span class="stat-value">${info.nodeVersion}</span>
            </div>
            <div class="system-stat">
                <span class="stat-label">Platform:</span>
                <span class="stat-value">${info.platform}</span>
            </div>
        `;
    }

    // Settings Action Methods
    showAddUserForm() {
        document.getElementById('addUserModal').style.display = 'block';
    }

    showAddBookForm() {
        document.getElementById('addBookModal').style.display = 'block';
    }

    closeModal(modalId) {
        document.getElementById(modalId).style.display = 'none';
    }

    async addUser(event) {
        event.preventDefault();
        try {
            const formData = {
                username: document.getElementById('newUsername').value,
                email: document.getElementById('newUserEmail').value,
                name: document.getElementById('newUserName').value,
                role: document.getElementById('newUserRole').value || 'user',
                status: document.getElementById('newUserStatus').value || 'active',
                password: document.getElementById('newUserPassword').value
            };

            const result = await this.apiCall('/api/users', {
                method: 'POST',
                body: JSON.stringify(formData)
            });

            if (result.success) {
                this.showAlert('User added successfully!', 'success', 'usersAlert');
                this.closeModal('addUserModal');
                document.getElementById('addUserForm').reset();
                await this.loadUsersForTab();
            } else {
                throw new Error(result.message || 'Failed to add user');
            }
        } catch (error) {
            console.error('Add user error:', error);
            this.showAlert('Failed to add user: ' + error.message, 'error', 'settingsAlert');
        }
    }

    async addBook(event) {
        event.preventDefault();
        try {
            const formData = {
                title: document.getElementById('newBookTitle').value,
                author: document.getElementById('newBookAuthor').value,
                isbn: document.getElementById('newBookISBN').value,
                category: document.getElementById('newBookCategory').value,
                year: document.getElementById('newBookYear').value
            };

            const result = await this.apiCall('/api/books', {
                method: 'POST',
                body: JSON.stringify(formData)
            });

            if (result.success) {
                this.showAlert('Book added successfully!', 'success', 'settingsAlert');
                this.closeModal('addBookModal');
                document.getElementById('addBookForm').reset();
                await this.loadBooks();
            } else {
                throw new Error(result.message || 'Failed to add book');
            }
        } catch (error) {
            console.error('Add book error:', error);
            this.showAlert('Failed to add book: ' + error.message, 'error', 'settingsAlert');
        }
    }

    async updateRentalSettings() {
        try {
            const settings = {
                defaultRentalDays: parseInt(document.getElementById('defaultRentalDays').value),
                maxRentalsPerUser: parseInt(document.getElementById('maxRentalsPerUser').value)
            };

            const result = await this.apiCall('/api/settings/rental', {
                method: 'PATCH',
                body: JSON.stringify(settings)
            });

            if (result.success) {
                this.showAlert('Rental settings updated successfully!', 'success', 'settingsAlert');
            } else {
                throw new Error(result.message || 'Failed to update settings');
            }
        } catch (error) {
            console.error('Update settings error:', error);
            this.showAlert('Failed to update settings: ' + error.message, 'error', 'settingsAlert');
        }
    }

    async bulkApproveRequests() {
        try {
            const result = await this.apiCall('/api/rentals/bulk-approve', {
                method: 'POST'
            });

            if (result.success) {
                this.showAlert(`Successfully approved ${result.data.approvedCount} requests!`, 'success', 'settingsAlert');
                // Reload relevant data
                await this.loadRequests();
                await this.loadActiveRentals();
                if (this.currentUser.permissions.canViewDashboard) {
                    await this.loadDashboard();
                }
            } else {
                throw new Error(result.message || 'Failed to bulk approve');
            }
        } catch (error) {
            console.error('Bulk approve error:', error);
            this.showAlert('Failed to bulk approve: ' + error.message, 'error', 'settingsAlert');
        }
    }

    // Placeholder methods for additional functionality
    toggleBookVisibility() {
        this.showAlert('Book visibility toggle feature coming soon!', 'info', 'settingsAlert');
    }

    exportBooks() {
        this.showAlert('Book export feature coming soon!', 'info', 'settingsAlert');
    }

    generateOverdueReport() {
        this.showAlert('Overdue report generation coming soon!', 'info', 'settingsAlert');
    }

    sendReminders() {
        this.showAlert('Reminder system coming soon!', 'info', 'settingsAlert');
    }

    bulkReturnBooks() {
        this.showAlert('Bulk return feature coming soon!', 'info', 'settingsAlert');
    }

    markLostBooks() {
        this.showAlert('Lost book tracking coming soon!', 'info', 'settingsAlert');
    }

    generateReturnReport() {
        this.showAlert('Return report generation coming soon!', 'info', 'settingsAlert');
    }

    processOverdueBooks() {
        this.showAlert('Overdue processing feature coming soon!', 'info', 'settingsAlert');
    }

    updateProfile() {
        this.showAlert('Profile update feature coming soon!', 'info', 'settingsAlert');
    }

    changePassword() {
        this.showAlert('Password change feature coming soon!', 'info', 'settingsAlert');
    }

    viewLoginHistory() {
        this.showAlert('Login history feature coming soon!', 'info', 'settingsAlert');
    }

    logoutAllSessions() {
        this.showAlert('Session management feature coming soon!', 'info', 'settingsAlert');
    }

    checkForUpdates() {
        this.showAlert('Update check feature coming soon!', 'info', 'settingsAlert');
    }

    editUser(userId) {
        this.showAlert(`Edit user ${userId} feature coming soon!`, 'info', 'settingsAlert');
    }

    async deleteUser(userId) {
        if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
            try {
                const result = await this.apiCall(`/api/users/${userId}`, {
                    method: 'DELETE'
                });

                if (result.success) {
                    this.showAlert('User deleted successfully!', 'success', 'usersAlert');
                    await this.loadUsersForTab();
                } else {
                    throw new Error(result.message || 'Failed to delete user');
                }
            } catch (error) {
                console.error('Delete user error:', error);
                this.showAlert('Failed to delete user: ' + error.message, 'error', 'usersAlert');
            }
        }
    }

    // User Status Management
    async enableUser(userId) {
        try {
            const result = await this.apiCall(`/api/users/${userId}/status`, {
                method: 'PATCH',
                body: JSON.stringify({ status: 'active' })
            });

            if (result.success) {
                this.showAlert('User enabled successfully!', 'success', 'usersAlert');
                await this.loadUsersForTab();
            } else {
                throw new Error(result.message || 'Failed to enable user');
            }
        } catch (error) {
            console.error('Enable user error:', error);
            this.showAlert('Failed to enable user: ' + error.message, 'error', 'usersAlert');
        }
    }

    async disableUser(userId) {
        if (confirm('Are you sure you want to disable this user?')) {
            try {
                const result = await this.apiCall(`/api/users/${userId}/status`, {
                    method: 'PATCH',
                    body: JSON.stringify({ status: 'disabled' })
                });

                if (result.success) {
                    this.showAlert('User disabled successfully!', 'success', 'usersAlert');
                    await this.loadUsersForTab();
                } else {
                    throw new Error(result.message || 'Failed to disable user');
                }
            } catch (error) {
                console.error('Disable user error:', error);
                this.showAlert('Failed to disable user: ' + error.message, 'error', 'usersAlert');
            }
        }
    }

    showAddUserModal() {
        const modal = document.getElementById('addUserModal');
        const roleFieldGroup = document.getElementById('roleFieldGroup');
        const statusFieldGroup = document.getElementById('statusFieldGroup');
        const roleField = document.getElementById('newUserRole');
        const statusField = document.getElementById('newUserStatus');

        // Show/hide fields based on user permissions
        if (this.currentUser.permissions.canManageUsers) {
            // Admin can see and set all fields
            roleFieldGroup.style.display = 'block';
            statusFieldGroup.style.display = 'block';
            roleField.required = true;
            statusField.required = true;
        } else {
            // Librarian cannot set role or status - defaults to 'user' and 'active'
            roleFieldGroup.style.display = 'none';
            statusFieldGroup.style.display = 'none';
            roleField.required = false;
            statusField.required = false;
            roleField.value = 'user'; // Default to user role
            statusField.value = 'active'; // Default to active status
        }

        modal.style.display = 'block';
    }

    async refreshUsers() {
        await this.loadUsersForTab();
        this.showAlert('Users list refreshed!', 'success', 'usersAlert');
    }

    // System Management Methods
    async createBackup() {
        try {
            const result = await this.apiCall('/api/backup/create');

            if (result.success) {
                // Download the backup file
                const dataStr = JSON.stringify(result.data, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                const url = URL.createObjectURL(dataBlob);

                const link = document.createElement('a');
                link.href = url;
                link.download = `iqraa-backup-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);

                this.showAlert('Backup created and downloaded successfully!', 'success', 'systemAlert');
                await this.loadBackupHistory();
            } else {
                throw new Error(result.message || 'Failed to create backup');
            }
        } catch (error) {
            console.error('Create backup error:', error);
            this.showAlert('Failed to create backup: ' + error.message, 'error', 'systemAlert');
        }
    }

    createPartialBackup() {
        this.showAlert('Partial backup feature coming soon!', 'info', 'systemAlert');
    }

    handleRestoreFile(event) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = async (e) => {
            try {
                const backupData = JSON.parse(e.target.result);
                await this.restoreFromBackup(backupData);
            } catch (error) {
                this.showAlert('Invalid backup file format!', 'error', 'systemAlert');
            }
        };
        reader.readAsText(file);
    }

    async restoreFromBackup(backupData) {
        if (confirm('Are you sure you want to restore from this backup? This will overwrite all current data.')) {
            try {
                const result = await this.apiCall('/api/backup/restore', {
                    method: 'POST',
                    body: JSON.stringify({ backupData })
                });

                if (result.success) {
                    this.showAlert('Data restored successfully!', 'success', 'systemAlert');
                    // Reload all data
                    await this.loadAllData();
                } else {
                    throw new Error(result.message || 'Failed to restore data');
                }
            } catch (error) {
                console.error('Restore error:', error);
                this.showAlert('Failed to restore data: ' + error.message, 'error', 'systemAlert');
            }
        }
    }

    async resetToDefaults() {
        if (confirm('Are you sure you want to reset the system to default state? This will delete ALL data and cannot be undone.')) {
            try {
                const result = await this.apiCall('/api/system/reset', {
                    method: 'POST'
                });

                if (result.success) {
                    this.showAlert('System reset to defaults successfully!', 'success', 'systemAlert');
                    // Reload all data
                    await this.loadAllData();
                } else {
                    throw new Error(result.message || 'Failed to reset system');
                }
            } catch (error) {
                console.error('Reset error:', error);
                this.showAlert('Failed to reset system: ' + error.message, 'error', 'systemAlert');
            }
        }
    }

    // System maintenance methods (placeholders)
    cleanupOldSessions() {
        this.showAlert('Session cleanup feature coming soon!', 'info', 'systemAlert');
    }

    cleanupLogs() {
        this.showAlert('Log cleanup feature coming soon!', 'info', 'systemAlert');
    }

    optimizeDatabase() {
        this.showAlert('Database optimization feature coming soon!', 'info', 'systemAlert');
    }

    restartServer() {
        this.showAlert('Server restart feature coming soon!', 'info', 'systemAlert');
    }

    async checkSystemHealth() {
        try {
            const result = await this.apiCall('/health');
            this.showAlert('✅ System health check passed!', 'success', 'systemAlert');
        } catch (error) {
            this.showAlert('❌ System health check failed!', 'error', 'systemAlert');
        }
    }

    generateSystemReport() {
        this.showAlert('System report generation feature coming soon!', 'info', 'systemAlert');
    }
}

// Global functions for HTML onclick handlers
function showTab(tabName) {
    if (window.app) {
        window.app.showTab(tabName);
    }
}

function logout() {
    if (window.app) {
        window.app.logout();
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    console.log('🌟 DOM loaded, initializing IqraaManager...');
    window.app = new IqraaManager();
});
