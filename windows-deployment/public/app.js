// IqraaManager Frontend Application
class IqraaManager {
    constructor() {
        this.API_BASE = window.location.origin;
        this.currentUser = null;
        this.sessionId = null;
        this.books = [];
        this.rentals = [];
        this.requests = [];
        
        this.init();
    }

    init() {
        console.log('🚀 IqraaManager initializing...');
        this.setupEventListeners();
        this.checkAuthStatus();
    }

    setupEventListeners() {
        // Login form
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                this.login(username, password);
            });
        }
    }

    // API call helper with session management
    async apiCall(endpoint, options = {}) {
        try {
            const headers = {
                'Content-Type': 'application/json',
                ...options.headers
            };
            
            // Add session ID if available
            if (this.sessionId) {
                headers['X-Session-ID'] = this.sessionId;
            }
            
            const response = await fetch(`${this.API_BASE}${endpoint}`, {
                headers: headers,
                ...options
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }
            
            return await response.json();
        } catch (error) {
            console.error('API Error:', error);
            throw error;
        }
    }

    // Authentication methods
    async login(username, password) {
        try {
            this.showAlert('Logging in...', 'info', 'loginAlert');
            
            const result = await this.apiCall('/api/login', {
                method: 'POST',
                body: JSON.stringify({ username, password })
            });
            
            if (result.success) {
                this.currentUser = result.user;
                this.sessionId = result.sessionId || result.token;
                
                // Store session ID for persistence
                if (this.sessionId) {
                    localStorage.setItem('sessionId', this.sessionId);
                }
                
                console.log('✅ Login successful:', this.currentUser);
                this.showMainApp();
                this.loadAllData();
            } else {
                this.showAlert('Invalid credentials', 'error', 'loginAlert');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showAlert('Login failed. Please try again.', 'error', 'loginAlert');
        }
    }

    async logout() {
        try {
            await this.apiCall('/api/logout', { method: 'POST' });
        } catch (error) {
            console.error('Logout error:', error);
        }
        
        this.currentUser = null;
        this.sessionId = null;
        localStorage.removeItem('sessionId');
        this.showLoginForm();
    }

    async checkAuthStatus() {
        try {
            console.log('🔍 Checking authentication status...');
            
            // Try to get session ID from localStorage
            const storedSessionId = localStorage.getItem('sessionId');
            if (storedSessionId) {
                this.sessionId = storedSessionId;
                console.log('📱 Found stored session ID');
            }
            
            const result = await this.apiCall('/api/auth/status');
            
            if (result.success && result.authenticated && result.user) {
                console.log('✅ User already authenticated:', result.user);
                this.currentUser = result.user;
                this.showMainApp();
                this.loadAllData();
                return true;
            } else {
                console.log('❌ User not authenticated');
                // Clear invalid session
                this.sessionId = null;
                localStorage.removeItem('sessionId');
                this.showLoginForm();
                return false;
            }
        } catch (error) {
            console.error('Auth check error:', error);
            // Clear invalid session
            this.sessionId = null;
            localStorage.removeItem('sessionId');
            this.showLoginForm();
            return false;
        }
    }

    // UI Management
    showLoginForm() {
        document.getElementById('loginSection').classList.remove('hidden');
        document.getElementById('mainApp').classList.add('hidden');
    }

    showMainApp() {
        document.getElementById('loginSection').classList.add('hidden');
        document.getElementById('mainApp').classList.remove('hidden');
        
        // Update user info
        document.getElementById('userName').textContent = this.currentUser.name || this.currentUser.username;
        document.getElementById('userRole').textContent = this.currentUser.role.toUpperCase();
        
        // Setup navigation based on user role
        this.setupNavigation();
    }

    setupNavigation() {
        const dashboardTab = document.getElementById('dashboardTab');
        const requestsTab = document.getElementById('requestsTab');
        const rentalsTab = document.getElementById('rentalsTab');
        
        // Show/hide tabs based on user permissions
        if (this.currentUser.permissions.canViewDashboard) {
            dashboardTab.style.display = 'block';
        } else {
            dashboardTab.style.display = 'none';
        }
        
        if (this.currentUser.permissions.canApproveRentals) {
            requestsTab.style.display = 'block';
        } else {
            requestsTab.style.display = 'none';
        }
        
        // Regular users see "My Rentals", admin/librarian don't
        if (this.currentUser.role === 'user') {
            rentalsTab.style.display = 'block';
        } else {
            rentalsTab.style.display = 'none';
        }
    }

    showAlert(message, type = 'info', containerId = 'alert') {
        const container = document.getElementById(containerId);
        if (!container) return;
        
        container.innerHTML = `<div class="alert alert-${type}">${message}</div>`;
        
        // Auto-hide success messages
        if (type === 'success') {
            setTimeout(() => {
                container.innerHTML = '';
            }, 3000);
        }
    }

    // Tab Management
    showTab(tabName) {
        console.log(`🔄 Switching to tab: ${tabName}`);
        
        // Hide all tab contents
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(content => {
            content.classList.remove('active');
        });
        
        // Remove active class from all tabs
        const tabs = document.querySelectorAll('.tab');
        tabs.forEach(tab => {
            tab.classList.remove('active');
        });
        
        // Show selected tab content
        const selectedContent = document.getElementById(`${tabName}Content`);
        if (selectedContent) {
            selectedContent.classList.add('active');
        }
        
        // Add active class to selected tab
        const selectedTab = event?.target || document.querySelector(`[onclick="showTab('${tabName}')"]`);
        if (selectedTab) {
            selectedTab.classList.add('active');
        }
        
        // Load data for the tab with a small delay to ensure DOM is ready
        setTimeout(() => {
            this.loadTabData(tabName);
        }, 100);
    }

    async loadTabData(tabName) {
        console.log(`📊 Loading data for tab: ${tabName}`);
        
        try {
            switch (tabName) {
                case 'books':
                    await this.loadBooks();
                    break;
                case 'dashboard':
                    if (this.currentUser.permissions.canViewDashboard) {
                        await this.loadDashboard();
                    }
                    break;
                case 'requests':
                    if (this.currentUser.permissions.canApproveRentals) {
                        await this.loadRequests();
                    }
                    break;
                case 'rentals':
                    await this.loadRentals();
                    break;
                case 'settings':
                    // Settings tab doesn't need data loading
                    break;
            }
        } catch (error) {
            console.error(`Error loading ${tabName} data:`, error);
            this.showAlert(`Failed to load ${tabName} data`, 'error', `${tabName}Alert`);
        }
    }

    async loadAllData() {
        console.log('📊 Loading all initial data...');
        
        // Always load books first
        await this.loadBooks();
        
        // Load other data based on permissions
        if (this.currentUser.permissions.canViewDashboard) {
            await this.loadDashboard();
        }
        
        if (this.currentUser.permissions.canApproveRentals) {
            await this.loadRequests();
        }
        
        if (this.currentUser.role === 'user') {
            await this.loadRentals();
        }
    }

    // Data Loading Methods
    async loadBooks() {
        try {
            console.log('📚 Loading books...');
            const result = await this.apiCall('/api/books');
            
            if (result.success) {
                this.books = result.data || [];
                console.log(`✅ Loaded ${this.books.length} books`);
                this.renderBooks();
            } else {
                throw new Error(result.message || 'Failed to load books');
            }
        } catch (error) {
            console.error('Books loading error:', error);
            this.showAlert('Failed to load books: ' + error.message, 'error', 'booksAlert');
        }
    }

    async loadDashboard() {
        try {
            console.log('📊 Loading dashboard stats...');
            const result = await this.apiCall('/api/dashboard/stats');
            
            if (result.success) {
                console.log('✅ Dashboard stats loaded:', result.data);
                this.renderDashboard(result.data);
            } else {
                throw new Error(result.message || 'Failed to load dashboard');
            }
        } catch (error) {
            console.error('Dashboard loading error:', error);
            this.showAlert('Failed to load dashboard: ' + error.message, 'error', 'dashboardAlert');
        }
    }

    async loadRequests() {
        try {
            console.log('📋 Loading rental requests...');
            const result = await this.apiCall('/api/rental-requests');
            
            if (result.success) {
                this.requests = result.data || [];
                console.log(`✅ Loaded ${this.requests.length} requests`);
                this.renderRequests();
            } else {
                throw new Error(result.message || 'Failed to load requests');
            }
        } catch (error) {
            console.error('Requests loading error:', error);
            this.showAlert('Failed to load requests: ' + error.message, 'error', 'requestsAlert');
        }
    }

    async loadRentals() {
        try {
            console.log('📖 Loading rentals...');
            const result = await this.apiCall('/api/rentals');
            
            if (result.success) {
                this.rentals = result.data || [];
                console.log(`✅ Loaded ${this.rentals.length} rentals`);
                this.renderRentals();
            } else {
                throw new Error(result.message || 'Failed to load rentals');
            }
        } catch (error) {
            console.error('Rentals loading error:', error);
            this.showAlert('Failed to load rentals: ' + error.message, 'error', 'rentalsAlert');
        }
    }

    // Rendering Methods
    renderBooks() {
        console.log('🎨 Rendering books...');
        const container = document.getElementById('booksGrid');

        if (!container) {
            console.error('❌ Books container not found!');
            return;
        }

        if (!this.books || this.books.length === 0) {
            container.innerHTML = '<div class="alert alert-info">No books available</div>';
            return;
        }

        container.innerHTML = this.books.map(book => `
            <div class="book-card">
                <h4>${book.title}</h4>
                <p><strong>Author:</strong> ${book.author}</p>
                <p><strong>Category:</strong> ${book.category}</p>
                <p><strong>Year:</strong> ${book.year}</p>
                <p><strong>Status:</strong>
                    <span class="status-badge ${book.status === 'available' ? 'status-approved' : 'status-pending'}">
                        ${book.status}
                    </span>
                </p>
                ${this.currentUser.permissions.canRequestRentals && book.status === 'available' ?
                    `<button onclick="app.requestBook(${book.id})" class="btn">Request Rental</button>` :
                    ''
                }
            </div>
        `).join('');

        console.log('✅ Books rendered successfully');
    }

    renderDashboard(stats) {
        console.log('🎨 Rendering dashboard...', stats);
        const container = document.getElementById('dashboardStats');

        if (!container) {
            console.error('❌ Dashboard container not found!');
            return;
        }

        if (!stats) {
            container.innerHTML = '<div class="alert alert-error">No dashboard data available</div>';
            return;
        }

        container.innerHTML = `
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>📚</h3>
                    <div class="number">${stats.totalBooks || 0}</div>
                    <div class="label">Total Books</div>
                </div>
                <div class="stat-card">
                    <h3>✅</h3>
                    <div class="number">${stats.availableBooks || 0}</div>
                    <div class="label">Available</div>
                </div>
                <div class="stat-card">
                    <h3>📖</h3>
                    <div class="number">${stats.borrowedBooks || 0}</div>
                    <div class="label">Borrowed</div>
                </div>
                <div class="stat-card">
                    <h3>👥</h3>
                    <div class="number">${stats.totalMembers || 0}</div>
                    <div class="label">Total Members</div>
                </div>
                <div class="stat-card">
                    <h3>🟢</h3>
                    <div class="number">${stats.activeMembers || 0}</div>
                    <div class="label">Active Sessions</div>
                </div>
                <div class="stat-card">
                    <h3>⏳</h3>
                    <div class="number">${stats.pendingRequests || 0}</div>
                    <div class="label">Pending Requests</div>
                </div>
                <div class="stat-card">
                    <h3>📋</h3>
                    <div class="number">${stats.activeRentals || 0}</div>
                    <div class="label">Active Rentals</div>
                </div>
                <div class="stat-card">
                    <h3>⚠️</h3>
                    <div class="number">${stats.overdueBooks || 0}</div>
                    <div class="label">Overdue Books</div>
                </div>
            </div>
        `;

        console.log('✅ Dashboard rendered successfully');
    }

    renderRequests() {
        console.log('🎨 Rendering requests...');
        const container = document.getElementById('requestsList');

        if (!container) {
            console.error('❌ Requests container not found!');
            return;
        }

        if (!this.requests || this.requests.length === 0) {
            container.innerHTML = '<div class="alert alert-info">No pending requests</div>';
            return;
        }

        container.innerHTML = this.requests.map(request => {
            // Find the book details
            const book = this.books.find(b => b.id === request.bookId);
            const bookTitle = book ? book.title : `Book ID: ${request.bookId}`;
            const bookAuthor = book ? book.author : 'Unknown Author';

            return `
                <div class="request-item">
                    <div class="request-header">
                        <div>
                            <div class="book-title">${bookTitle}</div>
                            <div class="book-author">by ${bookAuthor}</div>
                        </div>
                        <span class="status-badge status-${request.status}">${request.status}</span>
                    </div>
                    <p><strong>Requested by:</strong> ${request.username} (${request.userEmail})</p>
                    <p><strong>Request Date:</strong> ${new Date(request.requestDate).toLocaleDateString()}</p>
                    ${request.notes ? `<p><strong>Notes:</strong> ${request.notes}</p>` : ''}

                    ${request.status === 'pending' && this.currentUser.permissions.canApproveRentals ? `
                        <div style="margin-top: 15px;">
                            <button onclick="app.approveRequest(${request.id})" class="btn">Approve</button>
                            <button onclick="app.rejectRequest(${request.id})" class="btn btn-secondary">Reject</button>
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');

        console.log('✅ Requests rendered successfully');
    }

    renderRentals() {
        console.log('🎨 Rendering rentals...');
        const container = document.getElementById('rentalsList');

        if (!container) {
            console.error('❌ Rentals container not found!');
            return;
        }

        if (!this.rentals || this.rentals.length === 0) {
            container.innerHTML = '<div class="alert alert-info">No active rentals</div>';
            return;
        }

        container.innerHTML = this.rentals.map(rental => `
            <div class="request-item">
                <div class="request-header">
                    <div>
                        <div class="book-title">${rental.book.title}</div>
                        <div class="book-author">by ${rental.book.author}</div>
                    </div>
                    <span class="status-badge status-approved">${rental.status}</span>
                </div>
                <p><strong>Borrowed:</strong> ${new Date(rental.borrowDate).toLocaleDateString()}</p>
                <p><strong>Due Date:</strong> ${new Date(rental.dueDate).toLocaleDateString()}</p>
                <p><strong>Approved by:</strong> ${rental.approvedBy}</p>
                ${rental.notes ? `<p><strong>Notes:</strong> ${rental.notes}</p>` : ''}
            </div>
        `).join('');

        console.log('✅ Rentals rendered successfully');
    }

    // Action Methods
    async requestBook(bookId) {
        try {
            const notes = prompt('Add any notes for your rental request (optional):');

            const result = await this.apiCall('/api/rental-requests', {
                method: 'POST',
                body: JSON.stringify({
                    bookId: bookId,
                    notes: notes || ''
                })
            });

            if (result.success) {
                this.showAlert('Rental request submitted successfully!', 'success', 'booksAlert');
                // Reload data to reflect changes
                await this.loadBooks();
                if (this.currentUser.permissions.canApproveRentals) {
                    await this.loadRequests();
                }
            } else {
                throw new Error(result.message || 'Failed to submit request');
            }
        } catch (error) {
            console.error('Request book error:', error);
            this.showAlert('Failed to submit request: ' + error.message, 'error', 'booksAlert');
        }
    }

    async approveRequest(requestId) {
        try {
            const notes = prompt('Add approval notes (optional):');

            const result = await this.apiCall(`/api/rental-requests/${requestId}`, {
                method: 'PATCH',
                body: JSON.stringify({
                    status: 'approved',
                    notes: notes || ''
                })
            });

            if (result.success) {
                this.showAlert('Request approved successfully!', 'success', 'requestsAlert');
                // Reload data
                await this.loadRequests();
                await this.loadBooks();
                if (this.currentUser.permissions.canViewDashboard) {
                    await this.loadDashboard();
                }
            } else {
                throw new Error(result.message || 'Failed to approve request');
            }
        } catch (error) {
            console.error('Approve request error:', error);
            this.showAlert('Failed to approve request: ' + error.message, 'error', 'requestsAlert');
        }
    }

    async rejectRequest(requestId) {
        try {
            const notes = prompt('Add rejection reason (optional):');

            const result = await this.apiCall(`/api/rental-requests/${requestId}`, {
                method: 'PATCH',
                body: JSON.stringify({
                    status: 'rejected',
                    notes: notes || ''
                })
            });

            if (result.success) {
                this.showAlert('Request rejected successfully!', 'success', 'requestsAlert');
                // Reload data
                await this.loadRequests();
                if (this.currentUser.permissions.canViewDashboard) {
                    await this.loadDashboard();
                }
            } else {
                throw new Error(result.message || 'Failed to reject request');
            }
        } catch (error) {
            console.error('Reject request error:', error);
            this.showAlert('Failed to reject request: ' + error.message, 'error', 'requestsAlert');
        }
    }
}

// Global functions for HTML onclick handlers
function showTab(tabName) {
    if (window.app) {
        window.app.showTab(tabName);
    }
}

function logout() {
    if (window.app) {
        window.app.logout();
    }
}

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
    console.log('🌟 DOM loaded, initializing IqraaManager...');
    window.app = new IqraaManager();
});
