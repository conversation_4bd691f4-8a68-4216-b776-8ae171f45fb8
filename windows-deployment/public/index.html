<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IqraaManager - Library Management System</title>
    <link rel="icon" type="image/png" href="favicon.png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 1200px;
            margin: 20px;
            min-height: 80vh;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .login-form {
            max-width: 400px;
            margin: 0 auto;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .btn {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }

        .btn-secondary:hover {
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.3);
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .tabs {
            display: flex;
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            margin-bottom: 20px;
        }

        .tab {
            padding: 15px 25px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 16px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
        }

        .tab:hover {
            background-color: #e9ecef;
            color: #333;
        }

        .tab.active {
            color: #4CAF50;
            border-bottom-color: #4CAF50;
            background-color: white;
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease-in;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .card h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #dee2e6;
        }

        .stat-card h3 {
            color: #4CAF50;
            font-size: 1.5em;
            margin-bottom: 10px;
        }

        .stat-card .number {
            font-size: 2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .stat-card .label {
            color: #666;
            font-size: 0.9em;
        }

        .book-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
        }

        .book-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .book-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .book-card h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.2em;
        }

        .book-card p {
            color: #666;
            margin-bottom: 8px;
        }

        /* Add Book Form Styles */
        .add-book-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .add-book-form {
            margin-top: 15px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 15px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #495057;
        }

        .form-group input,
        .form-group select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }

        /* Search Section Styles */
        .search-section {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .search-row {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .search-row input {
            flex: 1;
            padding: 10px 15px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        .advanced-search {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .search-filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-group label {
            font-weight: bold;
            margin-bottom: 5px;
            color: #495057;
            font-size: 12px;
        }

        .filter-group input,
        .filter-group select {
            padding: 6px 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 13px;
        }

        /* Book Actions Styles */
        .book-actions {
            display: flex;
            gap: 8px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .book-actions .btn {
            padding: 6px 12px;
            font-size: 12px;
        }

        .request-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            transition: transform 0.2s;
        }

        .request-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .request-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .book-title {
            font-weight: bold;
            color: #333;
            font-size: 1.1em;
        }

        .book-author {
            color: #666;
            font-style: italic;
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 500;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background-color: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background-color: #f8d7da;
            color: #721c24;
        }

        .user-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .user-name {
            font-weight: 600;
            color: #333;
        }

        .user-role {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.85em;
            font-weight: 500;
        }

        .hidden {
            display: none !important;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4CAF50;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .credentials {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .credentials h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .credential-item {
            margin-bottom: 10px;
            padding: 8px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }

        .credential-item strong {
            color: #4CAF50;
        }

        .settings-section {
            margin-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 20px;
        }

        .settings-section:last-child {
            border-bottom: none;
        }

        .settings-section h3 {
            color: #4CAF50;
            margin-bottom: 20px;
            font-size: 1.4em;
            border-left: 4px solid #4CAF50;
            padding-left: 15px;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .setting-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
        }

        .setting-card h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .user-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .user-info h5 {
            margin: 0 0 5px 0;
            color: #333;
        }

        .user-info p {
            margin: 0;
            color: #666;
            font-size: 0.9em;
        }

        .user-actions {
            display: flex;
            gap: 10px;
        }

        .btn-small {
            padding: 5px 10px;
            font-size: 0.85em;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        }

        .btn-danger:hover {
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
        }

        .system-stat {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .system-stat:last-child {
            border-bottom: none;
        }

        .stat-label {
            font-weight: 500;
            color: #333;
        }

        .stat-value {
            color: #4CAF50;
            font-weight: bold;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .modal-header h3 {
            margin: 0;
            color: #333;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #aaa;
        }

        .close:hover {
            color: #333;
        }

        .users-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #dee2e6;
        }

        .users-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }

        .user-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .user-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .user-card.disabled {
            opacity: 0.6;
            background: #f1f1f1;
        }

        .user-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .user-info h4 {
            margin: 0 0 5px 0;
            color: #333;
            font-size: 1.2em;
        }

        .user-info p {
            margin: 2px 0;
            color: #666;
            font-size: 0.9em;
        }

        .user-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
        }

        .status-active {
            background-color: #d4edda;
            color: #155724;
        }

        .status-disabled {
            background-color: #f8d7da;
            color: #721c24;
        }

        .user-actions {
            display: flex;
            gap: 8px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .btn-enable {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        .btn-disable {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        }

        .system-section {
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 1px solid #dee2e6;
        }

        .system-section:last-child {
            border-bottom: none;
        }

        .system-section h3 {
            color: #4CAF50;
            margin-bottom: 20px;
            font-size: 1.4em;
            border-left: 4px solid #4CAF50;
            padding-left: 15px;
        }

        .system-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .info-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
        }

        .info-card h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .backup-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .backup-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
        }

        .backup-card h4 {
            color: #333;
            margin-bottom: 10px;
        }

        .backup-card p {
            color: #666;
            margin-bottom: 15px;
            font-size: 0.9em;
        }

        .backup-history {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
        }

        .backup-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .backup-item:last-child {
            border-bottom: none;
        }

        .maintenance-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .maintenance-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
        }

        .maintenance-card h4 {
            color: #333;
            margin-bottom: 15px;
        }

        .maintenance-card .btn {
            margin: 5px 5px 5px 0;
        }

        .rentals-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #dee2e6;
            flex-wrap: wrap;
            gap: 15px;
        }

        .rentals-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .rentals-stats {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .stat-item {
            text-align: center;
            padding: 10px 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #4CAF50;
            display: block;
        }

        .stat-number.overdue {
            color: #dc3545;
        }

        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                min-height: 95vh;
            }

            .header h1 {
                font-size: 2em;
            }

            .content {
                padding: 20px;
            }

            .tabs {
                flex-wrap: wrap;
            }

            .tab {
                flex: 1;
                min-width: 120px;
                padding: 12px 15px;
                font-size: 14px;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
            }

            .book-grid {
                grid-template-columns: 1fr;
            }

            .settings-grid {
                grid-template-columns: 1fr;
            }

            .user-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .user-actions {
                width: 100%;
                justify-content: flex-end;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 IqraaManager</h1>
            <p>Library Management System</p>
        </div>

        <!-- Login Form -->
        <div id="loginSection" class="content">
            <div class="login-form">
                <h2 style="text-align: center; margin-bottom: 30px; color: #333;">Welcome Back</h2>
                
                <div id="loginAlert"></div>
                
                <form id="loginForm">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    
                    <button type="submit" class="btn" style="width: 100%;">Login</button>
                </form>

                <div class="credentials">
                    <h3>🔑 Demo Credentials</h3>
                    <div class="credential-item">
                        <strong>Admin:</strong> admin / admin123
                    </div>
                    <div class="credential-item">
                        <strong>Librarian:</strong> librarian / librarian123
                    </div>
                    <div class="credential-item">
                        <strong>User:</strong> user / user123
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Application -->
        <div id="mainApp" class="hidden">
            <!-- User Info Bar -->
            <div class="user-info">
                <div>
                    <span class="user-name" id="userName">User Name</span>
                    <span class="user-role" id="userRole">Role</span>
                </div>
                <button onclick="logout()" class="btn btn-secondary">Logout</button>
            </div>

            <div class="content">
                <!-- Navigation Tabs -->
                <div class="tabs" id="navigationTabs">
                    <button class="tab" id="dashboardTab" onclick="showTab('dashboard')">📊 Dashboard</button>
                    <button class="tab" onclick="showTab('books')">📚 Books</button>
                    <button class="tab" id="requestsTab" onclick="showTab('requests')">📋 Requests</button>
                    <button class="tab" id="activeRentalsTab" onclick="showTab('activeRentals')">📖 Active Rentals</button>
                    <button class="tab" id="usersTab" onclick="showTab('users')">👥 Users</button>
                    <button class="tab" id="systemTab" onclick="showTab('system')">🖥️ System</button>
                    <button class="tab" id="rentalsTab" onclick="showTab('rentals')">📖 My Rentals</button>
                    <button class="tab" onclick="showTab('settings')">⚙️ Settings</button>
                </div>

                <!-- Books Tab -->
                <div id="booksContent" class="tab-content active">
                    <div class="card">
                        <h2>📚 Book Library</h2>
                        <div id="booksAlert"></div>

                        <!-- Add Book Section (Admin/Librarian only) -->
                        <div id="addBookSection" class="add-book-section" style="display: none;">
                            <h3>➕ Add New Book</h3>
                            <div class="add-book-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="newBookTitle">Book Title:</label>
                                        <input type="text" id="newBookTitle" placeholder="Enter book title" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="newBookAuthor">Author:</label>
                                        <input type="text" id="newBookAuthor" placeholder="Enter author name" required>
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="newBookCategory">Category:</label>
                                        <select id="newBookCategory" required>
                                            <option value="">Select category</option>
                                            <option value="Fiction">Fiction</option>
                                            <option value="Non-Fiction">Non-Fiction</option>
                                            <option value="Science">Science</option>
                                            <option value="Technology">Technology</option>
                                            <option value="History">History</option>
                                            <option value="Literature">Literature</option>
                                            <option value="Biography">Biography</option>
                                            <option value="Education">Education</option>
                                            <option value="Reference">Reference</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label for="newBookEditor">Editor:</label>
                                        <input type="text" id="newBookEditor" placeholder="Enter editor name">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="newBookPublisher">Publisher:</label>
                                        <input type="text" id="newBookPublisher" placeholder="Enter publisher name">
                                    </div>
                                    <div class="form-group">
                                        <label for="newBookYear">Publication Year:</label>
                                        <input type="number" id="newBookYear" placeholder="YYYY" min="1000" max="2030">
                                    </div>
                                </div>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="newBookSerial">Serial Number (ISBN):</label>
                                        <input type="text" id="newBookSerial" placeholder="Enter ISBN or serial number">
                                    </div>
                                    <div class="form-group">
                                        <button onclick="app.addNewBook()" class="btn">📚 Add Book</button>
                                        <button onclick="app.clearAddBookForm()" class="btn btn-secondary">🗑️ Clear</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Search Section -->
                        <div class="search-section">
                            <h3>🔍 Search Books</h3>
                            <div class="search-container">
                                <div class="search-row">
                                    <input type="text" id="bookSearchGeneral" placeholder="🔍 Search by any detail (title, author, category, publisher, etc.)" onkeyup="app.searchBooks()">
                                    <button onclick="app.clearSearch()" class="btn btn-secondary">Clear</button>
                                </div>
                                <div class="advanced-search" id="advancedSearch" style="display: none;">
                                    <div class="search-filters">
                                        <div class="filter-group">
                                            <label>Title:</label>
                                            <input type="text" id="searchTitle" placeholder="Book title" onkeyup="app.searchBooks()">
                                        </div>
                                        <div class="filter-group">
                                            <label>Author:</label>
                                            <input type="text" id="searchAuthor" placeholder="Author name" onkeyup="app.searchBooks()">
                                        </div>
                                        <div class="filter-group">
                                            <label>Category:</label>
                                            <select id="searchCategory" onchange="app.searchBooks()">
                                                <option value="">All categories</option>
                                                <option value="Fiction">Fiction</option>
                                                <option value="Non-Fiction">Non-Fiction</option>
                                                <option value="Science">Science</option>
                                                <option value="Technology">Technology</option>
                                                <option value="History">History</option>
                                                <option value="Literature">Literature</option>
                                                <option value="Biography">Biography</option>
                                                <option value="Education">Education</option>
                                                <option value="Reference">Reference</option>
                                                <option value="Other">Other</option>
                                            </select>
                                        </div>
                                        <div class="filter-group">
                                            <label>Publisher:</label>
                                            <input type="text" id="searchPublisher" placeholder="Publisher name" onkeyup="app.searchBooks()">
                                        </div>
                                        <div class="filter-group">
                                            <label>Year:</label>
                                            <input type="number" id="searchYear" placeholder="Publication year" onkeyup="app.searchBooks()">
                                        </div>
                                        <div class="filter-group">
                                            <label>Status:</label>
                                            <select id="searchStatus" onchange="app.searchBooks()">
                                                <option value="">All statuses</option>
                                                <option value="available">Available</option>
                                                <option value="borrowed">Borrowed</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <button onclick="app.toggleAdvancedSearch()" class="btn btn-secondary">🔧 Advanced Search</button>
                            </div>
                        </div>

                        <div id="booksGrid" class="book-grid">
                            <div class="loading">Loading books...</div>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Tab -->
                <div id="dashboardContent" class="tab-content">
                    <div class="card">
                        <h2>📊 Dashboard</h2>
                        <div id="dashboardAlert"></div>
                        <div id="dashboardStats">
                            <div class="loading">Loading dashboard...</div>
                        </div>
                    </div>
                </div>

                <!-- Requests Tab -->
                <div id="requestsContent" class="tab-content">
                    <div class="card">
                        <h2>📋 Rental Requests</h2>
                        <div id="requestsAlert"></div>
                        <div id="requestsList">
                            <div class="loading">Loading requests...</div>
                        </div>
                    </div>
                </div>

                <!-- Active Rentals Tab (Admin/Librarian) -->
                <div id="activeRentalsContent" class="tab-content">
                    <div class="card">
                        <h2>📖 Active Rentals</h2>
                        <div id="activeRentalsAlert"></div>

                        <div class="rentals-header">
                            <div class="rentals-actions">
                                <button onclick="app.generateOverdueReport()" class="btn btn-secondary">📋 Overdue Report</button>
                                <button onclick="app.sendOverdueReminders()" class="btn btn-secondary">📧 Send Reminders</button>
                                <button onclick="app.refreshActiveRentals()" class="btn">🔄 Refresh</button>
                            </div>
                            <div class="rentals-stats" id="rentalsStats">
                                <div class="loading">Loading statistics...</div>
                            </div>
                        </div>

                        <div id="activeRentalsList">
                            <div class="loading">Loading active rentals...</div>
                        </div>
                    </div>
                </div>

                <!-- Users Tab (Admin/Librarian) -->
                <div id="usersContent" class="tab-content">
                    <div class="card">
                        <h2>👥 User Management</h2>
                        <div id="usersAlert"></div>

                        <div class="users-header">
                            <button onclick="app.showAddUserModal()" class="btn">➕ Add New User</button>
                            <button onclick="app.refreshUsers()" class="btn btn-secondary">🔄 Refresh</button>
                        </div>

                        <div id="usersGrid" class="users-grid">
                            <div class="loading">Loading users...</div>
                        </div>
                    </div>
                </div>

                <!-- System Tab (Admin/Librarian) -->
                <div id="systemContent" class="tab-content">
                    <div class="card">
                        <h2>🖥️ System Management</h2>
                        <div id="systemAlert"></div>

                        <!-- System Information Section -->
                        <div class="system-section">
                            <h3>📊 System Information</h3>
                            <div class="system-info-grid">
                                <div class="info-card">
                                    <h4>🖥️ Server Status</h4>
                                    <div id="serverStatus">
                                        <div class="loading">Loading server status...</div>
                                    </div>
                                </div>
                                <div class="info-card">
                                    <h4>📈 Performance Metrics</h4>
                                    <div id="performanceMetrics">
                                        <div class="loading">Loading performance data...</div>
                                    </div>
                                </div>
                                <div class="info-card">
                                    <h4>💾 Storage Information</h4>
                                    <div id="storageInfo">
                                        <div class="loading">Loading storage data...</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- System Details Section -->
                        <div class="system-section">
                            <h3>🔧 System Details</h3>
                            <div class="system-info-grid">
                                <div class="info-card">
                                    <h4>🌐 Network Information</h4>
                                    <div id="networkInfo">
                                        <div class="loading">Loading network data...</div>
                                    </div>
                                </div>
                                <div class="info-card">
                                    <h4>📊 Database Statistics</h4>
                                    <div id="databaseStats">
                                        <div class="loading">Loading database stats...</div>
                                    </div>
                                </div>
                                <div class="info-card">
                                    <h4>🔒 Security Status</h4>
                                    <div id="securityStatus">
                                        <div class="loading">Loading security status...</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Application Information Section -->
                        <div class="system-section">
                            <h3>📱 Application Information</h3>
                            <div class="system-info-grid">
                                <div class="info-card">
                                    <h4>ℹ️ Version Details</h4>
                                    <div id="versionInfo">
                                        <div class="loading">Loading version info...</div>
                                    </div>
                                </div>
                                <div class="info-card">
                                    <h4>📅 System Logs</h4>
                                    <div id="systemLogs">
                                        <div class="loading">Loading recent logs...</div>
                                    </div>
                                </div>
                                <div class="info-card">
                                    <h4>⚙️ Configuration</h4>
                                    <div id="systemConfig">
                                        <div class="loading">Loading configuration...</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Backup & Restore Section -->
                        <div class="system-section">
                            <h3>💾 Backup & Restore</h3>
                            <div class="backup-grid">
                                <div class="backup-card">
                                    <h4>📤 Create Backup</h4>
                                    <p>Export all application data including books, users, rentals, and settings.</p>
                                    <button onclick="app.createBackup()" class="btn">📤 Create Full Backup</button>
                                    <button onclick="app.createPartialBackup()" class="btn btn-secondary">📋 Create Data Backup</button>
                                </div>
                                <div class="backup-card">
                                    <h4>📥 Restore Data</h4>
                                    <p>Import previously exported application data.</p>
                                    <input type="file" id="restoreFile" accept=".json" style="display: none;" onchange="app.handleRestoreFile(event)">
                                    <button onclick="document.getElementById('restoreFile').click()" class="btn">📥 Select Backup File</button>
                                    <button onclick="app.resetToDefaults()" class="btn btn-danger">🔄 Reset to Defaults</button>
                                </div>
                            </div>

                            <div class="backup-history">
                                <h4>📋 Recent Backups</h4>
                                <div id="backupHistory">
                                    <div class="loading">Loading backup history...</div>
                                </div>
                            </div>
                        </div>

                        <!-- System Maintenance Section -->
                        <div class="system-section">
                            <h3>🔧 System Maintenance</h3>
                            <div class="maintenance-grid">
                                <div class="maintenance-card">
                                    <h4>🧹 Data Cleanup</h4>
                                    <button onclick="app.cleanupOldSessions()" class="btn btn-secondary">🗑️ Clear Old Sessions</button>
                                    <button onclick="app.cleanupLogs()" class="btn btn-secondary">📝 Clear System Logs</button>
                                    <button onclick="app.optimizeDatabase()" class="btn btn-secondary">⚡ Optimize Data</button>
                                </div>
                                <div class="maintenance-card">
                                    <h4>🔄 System Actions</h4>
                                    <button onclick="app.restartServer()" class="btn btn-secondary">🔄 Restart Server</button>
                                    <button onclick="app.checkSystemHealth()" class="btn">🏥 Health Check</button>
                                    <button onclick="app.generateSystemReport()" class="btn">📊 Generate Report</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Rentals Tab -->
                <div id="rentalsContent" class="tab-content">
                    <div class="card">
                        <h2>📖 My Rentals</h2>
                        <div id="rentalsAlert"></div>
                        <div id="rentalsList">
                            <div class="loading">Loading rentals...</div>
                        </div>
                    </div>
                </div>

                <!-- Settings Tab -->
                <div id="settingsContent" class="tab-content">
                    <div class="card">
                        <h2>⚙️ Settings</h2>
                        <div id="settingsAlert"></div>

                        <!-- User Management Section (Admin Only) -->
                        <div id="userManagementSection" class="settings-section">
                            <h3>👥 User Management</h3>
                            <div class="settings-grid">
                                <div class="setting-card">
                                    <h4>📋 User List</h4>
                                    <div id="usersList">
                                        <div class="loading">Loading users...</div>
                                    </div>
                                    <button onclick="app.showAddUserForm()" class="btn">➕ Add New User</button>
                                </div>
                            </div>
                        </div>

                        <!-- Book Management Section (Admin Only) -->
                        <div id="bookManagementSection" class="settings-section">
                            <h3>📚 Book Management</h3>
                            <div class="settings-grid">
                                <div class="setting-card">
                                    <h4>📖 Book Actions</h4>
                                    <button onclick="app.showAddBookForm()" class="btn">➕ Add New Book</button>
                                    <button onclick="app.toggleBookVisibility()" class="btn btn-secondary">👁️ Toggle Book Visibility</button>
                                    <button onclick="app.exportBooks()" class="btn btn-secondary">📤 Export Book List</button>
                                </div>
                            </div>
                        </div>

                        <!-- Rental Management Section (Admin & Librarian) -->
                        <div id="rentalManagementSection" class="settings-section">
                            <h3>📋 Rental Management</h3>
                            <div class="settings-grid">
                                <div class="setting-card">
                                    <h4>⚙️ Rental Settings</h4>
                                    <div class="form-group">
                                        <label for="defaultRentalDays">Default Rental Period (days):</label>
                                        <input type="number" id="defaultRentalDays" value="14" min="1" max="90">
                                    </div>
                                    <div class="form-group">
                                        <label for="maxRentalsPerUser">Max Rentals per User:</label>
                                        <input type="number" id="maxRentalsPerUser" value="3" min="1" max="10">
                                    </div>
                                    <button onclick="app.updateRentalSettings()" class="btn">💾 Save Settings</button>
                                </div>
                                <div class="setting-card">
                                    <h4>📊 Rental Actions</h4>
                                    <button onclick="app.bulkApproveRequests()" class="btn">✅ Bulk Approve Pending</button>
                                    <button onclick="app.generateOverdueReport()" class="btn btn-secondary">📋 Overdue Report</button>
                                    <button onclick="app.sendReminders()" class="btn btn-secondary">📧 Send Reminders</button>
                                    <button onclick="app.viewRentalStatistics()" class="btn btn-secondary">📊 View Statistics</button>
                                </div>
                            </div>
                        </div>

                        <!-- Return Management Section (Admin & Librarian) -->
                        <div id="returnManagementSection" class="settings-section">
                            <h3>📖 Return Management</h3>
                            <div class="settings-grid">
                                <div class="setting-card">
                                    <h4>🔄 Return Actions</h4>
                                    <button onclick="app.bulkReturnBooks()" class="btn">📚 Bulk Return Process</button>
                                    <button onclick="app.markLostBooks()" class="btn btn-secondary">❌ Mark Books as Lost</button>
                                    <button onclick="app.generateReturnReport()" class="btn btn-secondary">📊 Return Statistics</button>
                                </div>
                                <div class="setting-card">
                                    <h4>⚠️ Overdue Management</h4>
                                    <div id="overdueStats">
                                        <div class="loading">Loading overdue statistics...</div>
                                    </div>
                                    <button onclick="app.processOverdueBooks()" class="btn">⏰ Process All Overdue</button>
                                </div>
                            </div>
                        </div>

                        <!-- System Settings Section (All Users) -->
                        <div id="systemSettingsSection" class="settings-section">
                            <h3>🔧 System Settings</h3>
                            <div class="settings-grid">
                                <div class="setting-card">
                                    <h4>👤 Profile Settings</h4>
                                    <div class="form-group">
                                        <label for="userDisplayName">Display Name:</label>
                                        <input type="text" id="userDisplayName" placeholder="Your display name">
                                    </div>
                                    <div class="form-group">
                                        <label for="userEmail">Email:</label>
                                        <input type="email" id="userEmail" placeholder="<EMAIL>">
                                    </div>
                                    <button onclick="app.updateProfile()" class="btn">💾 Update Profile</button>
                                </div>
                                <div class="setting-card">
                                    <h4>🔒 Security</h4>
                                    <button onclick="app.changePassword()" class="btn">🔑 Change Password</button>
                                    <button onclick="app.viewLoginHistory()" class="btn btn-secondary">📋 Login History</button>
                                    <button onclick="app.logoutAllSessions()" class="btn btn-secondary">🚪 Logout All Sessions</button>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Dialogs -->
    <!-- Add User Modal -->
    <div id="addUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>➕ Add New User</h3>
                <span class="close" onclick="app.closeModal('addUserModal')">&times;</span>
            </div>
            <form id="addUserForm">
                <div class="form-group">
                    <label for="newUsername">Username:</label>
                    <input type="text" id="newUsername" required>
                </div>
                <div class="form-group">
                    <label for="newUserEmail">Email:</label>
                    <input type="email" id="newUserEmail" required>
                </div>
                <div class="form-group">
                    <label for="newUserName">Full Name:</label>
                    <input type="text" id="newUserName" required>
                </div>
                <div class="form-group" id="roleFieldGroup">
                    <label for="newUserRole">Role:</label>
                    <select id="newUserRole" required>
                        <option value="user">User</option>
                        <option value="librarian">Librarian</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                <div class="form-group" id="statusFieldGroup">
                    <label for="newUserStatus">Status:</label>
                    <select id="newUserStatus" required>
                        <option value="active">Active</option>
                        <option value="disabled">Disabled</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="newUserPassword">Password:</label>
                    <input type="password" id="newUserPassword" required>
                </div>
                <button type="submit" class="btn">➕ Add User</button>
                <button type="button" class="btn btn-secondary" onclick="app.closeModal('addUserModal')">Cancel</button>
            </form>
        </div>
    </div>

    <!-- Add Book Modal -->
    <div id="addBookModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>📚 Add New Book</h3>
                <span class="close" onclick="app.closeModal('addBookModal')">&times;</span>
            </div>
            <form id="addBookForm">
                <div class="form-group">
                    <label for="newBookTitle">Title:</label>
                    <input type="text" id="newBookTitle" required>
                </div>
                <div class="form-group">
                    <label for="newBookAuthor">Author:</label>
                    <input type="text" id="newBookAuthor" required>
                </div>
                <div class="form-group">
                    <label for="newBookISBN">ISBN:</label>
                    <input type="text" id="newBookISBN" required>
                </div>
                <div class="form-group">
                    <label for="newBookCategory">Category:</label>
                    <input type="text" id="newBookCategory" required>
                </div>
                <div class="form-group">
                    <label for="newBookYear">Year:</label>
                    <input type="number" id="newBookYear" min="1000" max="2030" required>
                </div>
                <button type="submit" class="btn">📚 Add Book</button>
                <button type="button" class="btn btn-secondary" onclick="app.closeModal('addBookModal')">Cancel</button>
            </form>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
