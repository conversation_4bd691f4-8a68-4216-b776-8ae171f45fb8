<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IqraaManager - Library Management System</title>
    <link rel="icon" type="image/png" href="favicon.png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 100%;
            max-width: 1200px;
            margin: 20px;
            min-height: 80vh;
        }

        .header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .login-form {
            max-width: 400px;
            margin: 0 auto;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .btn {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
        }

        .btn-secondary:hover {
            box-shadow: 0 5px 15px rgba(33, 150, 243, 0.3);
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .tabs {
            display: flex;
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            margin-bottom: 20px;
        }

        .tab {
            padding: 15px 25px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 16px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s;
            border-bottom: 3px solid transparent;
        }

        .tab:hover {
            background-color: #e9ecef;
            color: #333;
        }

        .tab.active {
            color: #4CAF50;
            border-bottom-color: #4CAF50;
            background-color: white;
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease-in;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .card h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 1px solid #dee2e6;
        }

        .stat-card h3 {
            color: #4CAF50;
            font-size: 1.5em;
            margin-bottom: 10px;
        }

        .stat-card .number {
            font-size: 2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .stat-card .label {
            color: #666;
            font-size: 0.9em;
        }

        .book-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
        }

        .book-card {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .book-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .book-card h4 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.2em;
        }

        .book-card p {
            color: #666;
            margin-bottom: 8px;
        }

        .request-item {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            transition: transform 0.2s;
        }

        .request-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .request-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .book-title {
            font-weight: bold;
            color: #333;
            font-size: 1.1em;
        }

        .book-author {
            color: #666;
            font-style: italic;
        }

        .status-badge {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 500;
        }

        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background-color: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background-color: #f8d7da;
            color: #721c24;
        }

        .user-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .user-name {
            font-weight: 600;
            color: #333;
        }

        .user-role {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.85em;
            font-weight: 500;
        }

        .hidden {
            display: none !important;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4CAF50;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .credentials {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .credentials h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .credential-item {
            margin-bottom: 10px;
            padding: 8px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }

        .credential-item strong {
            color: #4CAF50;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                min-height: 95vh;
            }

            .header h1 {
                font-size: 2em;
            }

            .content {
                padding: 20px;
            }

            .tabs {
                flex-wrap: wrap;
            }

            .tab {
                flex: 1;
                min-width: 120px;
                padding: 12px 15px;
                font-size: 14px;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
            }

            .book-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 IqraaManager</h1>
            <p>Library Management System</p>
        </div>

        <!-- Login Form -->
        <div id="loginSection" class="content">
            <div class="login-form">
                <h2 style="text-align: center; margin-bottom: 30px; color: #333;">Welcome Back</h2>
                
                <div id="loginAlert"></div>
                
                <form id="loginForm">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">Password</label>
                        <input type="password" id="password" name="password" required>
                    </div>
                    
                    <button type="submit" class="btn" style="width: 100%;">Login</button>
                </form>

                <div class="credentials">
                    <h3>🔑 Demo Credentials</h3>
                    <div class="credential-item">
                        <strong>Admin:</strong> admin / admin123
                    </div>
                    <div class="credential-item">
                        <strong>Librarian:</strong> librarian / librarian123
                    </div>
                    <div class="credential-item">
                        <strong>User:</strong> user / user123
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Application -->
        <div id="mainApp" class="hidden">
            <!-- User Info Bar -->
            <div class="user-info">
                <div>
                    <span class="user-name" id="userName">User Name</span>
                    <span class="user-role" id="userRole">Role</span>
                </div>
                <button onclick="logout()" class="btn btn-secondary">Logout</button>
            </div>

            <div class="content">
                <!-- Navigation Tabs -->
                <div class="tabs" id="navigationTabs">
                    <button class="tab active" onclick="showTab('books')">📚 Books</button>
                    <button class="tab" id="dashboardTab" onclick="showTab('dashboard')">📊 Dashboard</button>
                    <button class="tab" id="requestsTab" onclick="showTab('requests')">📋 Requests</button>
                    <button class="tab" id="rentalsTab" onclick="showTab('rentals')">📖 My Rentals</button>
                    <button class="tab" onclick="showTab('settings')">⚙️ Settings</button>
                </div>

                <!-- Books Tab -->
                <div id="booksContent" class="tab-content active">
                    <div class="card">
                        <h2>📚 Book Library</h2>
                        <div id="booksAlert"></div>
                        <div id="booksGrid" class="book-grid">
                            <div class="loading">Loading books...</div>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Tab -->
                <div id="dashboardContent" class="tab-content">
                    <div class="card">
                        <h2>📊 Dashboard</h2>
                        <div id="dashboardAlert"></div>
                        <div id="dashboardStats">
                            <div class="loading">Loading dashboard...</div>
                        </div>
                    </div>
                </div>

                <!-- Requests Tab -->
                <div id="requestsContent" class="tab-content">
                    <div class="card">
                        <h2>📋 Rental Requests</h2>
                        <div id="requestsAlert"></div>
                        <div id="requestsList">
                            <div class="loading">Loading requests...</div>
                        </div>
                    </div>
                </div>

                <!-- Rentals Tab -->
                <div id="rentalsContent" class="tab-content">
                    <div class="card">
                        <h2>📖 My Rentals</h2>
                        <div id="rentalsAlert"></div>
                        <div id="rentalsList">
                            <div class="loading">Loading rentals...</div>
                        </div>
                    </div>
                </div>

                <!-- Settings Tab -->
                <div id="settingsContent" class="tab-content">
                    <div class="card">
                        <h2>⚙️ Settings</h2>
                        <div id="settingsAlert"></div>
                        <p>Settings functionality coming soon...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
