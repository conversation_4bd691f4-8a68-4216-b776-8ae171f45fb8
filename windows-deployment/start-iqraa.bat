@echo off
title IqraaManager - Library Management System

echo.
echo ========================================
echo    IqraaManager - Starting Server...
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo.
    echo Please install Node.js from: https://nodejs.org
    echo Download the LTS version and restart this script.
    echo.
    pause
    exit /b 1
)

echo Node.js version:
node --version
echo.

REM Check if node_modules exists, if not install dependencies
if not exist "node_modules" (
    echo Installing dependencies...
    echo This may take a few minutes on first run...
    echo.
    npm install
    if %errorlevel% neq 0 (
        echo.
        echo ERROR: Failed to install dependencies
        echo Please check your internet connection and try again.
        echo.
        pause
        exit /b 1
    )
    echo.
    echo Dependencies installed successfully!
    echo.
)

REM Start the server
echo Starting IqraaManager server...
echo.
echo ========================================
echo  Server will start in a few seconds...
echo  Press Ctrl+C to stop the server
echo ========================================
echo.

node server.js

REM If we get here, the server stopped
echo.
echo ========================================
echo    Server stopped
echo ========================================
echo.
pause
