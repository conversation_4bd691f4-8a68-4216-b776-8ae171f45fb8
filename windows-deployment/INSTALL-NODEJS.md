# Installing Node.js on Windows

## 📥 Download Node.js

1. **Visit the official Node.js website**: https://nodejs.org
2. **Download the LTS version** (recommended for most users)
3. **Choose the Windows Installer (.msi)** for your system:
   - **64-bit**: Most modern Windows computers
   - **32-bit**: Older computers (rare)

## 🔧 Installation Steps

1. **Run the downloaded installer** (.msi file)
2. **Follow the installation wizard**:
   - Accept the license agreement
   - Choose installation location (default is fine)
   - **Important**: Make sure "Add to PATH" is checked
   - Install additional tools if prompted (recommended)
3. **Restart your computer** after installation

## ✅ Verify Installation

1. **Open Command Prompt**:
   - Press `Windows + R`
   - Type `cmd` and press Enter
2. **Check Node.js version**:
   ```
   node --version
   ```
   Should show something like: `v18.17.0`
3. **Check npm version**:
   ```
   npm --version
   ```
   Should show something like: `9.6.7`

## 🚀 Run IqraaManager

Once Node.js is installed:
1. **Double-click** `start-iqraa.bat`
2. **Or run manually**:
   - Open Command Prompt in the IqraaManager folder
   - Run: `npm install` (first time only)
   - Run: `node server.js`

## 🐛 Troubleshooting

### "node is not recognized as an internal or external command"
- Node.js is not in your PATH
- Restart your computer
- Reinstall Node.js and ensure "Add to PATH" is checked

### Permission errors
- Run Command Prompt as Administrator
- Right-click on `start-iqraa.bat` and select "Run as administrator"

### Port already in use
- Another application is using port 5000
- Close other applications or restart your computer
- The server will show the correct IP address to use

## 🌐 Network Access

After starting the server:
1. **Note the IP address** shown in the startup message
2. **Share this address** with other devices on your network
3. **Example**: If the server shows `http://*************:5000`
   - Other devices can access: `http://*************:5000`

## 🔒 Windows Firewall

If other devices can't access the application:
1. **Windows Security** → **Firewall & network protection**
2. **Allow an app through firewall**
3. **Find Node.js** in the list and check both boxes
4. **Or temporarily disable** Windows Firewall for testing

## 📱 Mobile Access

The application works on mobile devices:
1. **Connect mobile device** to the same WiFi network
2. **Open browser** on mobile device
3. **Enter the IP address** shown by the server
4. **Bookmark** for easy access
