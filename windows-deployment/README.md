# IqraaManager - Windows Deployment Package

## 📋 Overview
Complete deployment package for IqraaManager library management system on Windows.

## 🚀 Quick Start

1. **Extract the package** to your desired location
2. **Run the startup script**: Double-click `start-iqraa.bat`
3. **Access the application**: Open http://localhost:5000 in your browser

## 👤 Default Login Credentials

- **Admin**: admin / admin123 (Full access)
- **Librarian**: librarian / librarian123 (Manage books, approve rentals)
- **User**: user / user123 (Search books, request rentals)

## 🌐 Network Access

The application automatically detects your local IP and makes itself available on the network.
Other devices can access it using: `http://YOUR_IP:5000`

## 📁 Package Contents

```
windows-deployment/
├── start-iqraa.bat          # Main startup script
├── server.js                # Application server
├── public/                  # Web application files
│   ├── index.html          # Main application
│   ├── assets/             # CSS and JS files
│   └── favicon.png         # Application icon
├── node_modules/           # Dependencies (if included)
├── package.json            # Node.js configuration
└── README.md              # This file
```

## 🔧 Requirements

- **Node.js 16+** (Download from https://nodejs.org)
- **Windows 10/11**
- **Network access** for multi-device use

## 🛠️ Manual Setup (if needed)

If the automatic startup doesn't work:

1. Open Command Prompt in the deployment folder
2. Run: `npm install` (if node_modules not included)
3. Run: `node server.js`
4. Open http://localhost:5000

## 🌟 Features

- ✅ **Role-based access control** with granular permissions
- ✅ **Book search and management** with visibility controls
- ✅ **Rental request workflow** with approval system
- ✅ **Book return management** with overdue tracking
- ✅ **User management** with add/disable/enable/delete
- ✅ **System management** with backup/restore functionality
- ✅ **Overdue tracking** with visual alerts
- ✅ **Multi-device network access**
- ✅ **Session persistence**
- ✅ **Real-time updates**

## 🔒 Security

- Session-based authentication
- Role-based permissions
- Secure password handling
- Network access controls

## 📞 Support

For issues or questions, check the troubleshooting section below.

## 🐛 Troubleshooting

### Application won't start
- Ensure Node.js is installed
- Check if port 5000 is available
- Run as Administrator if needed
- Try running `install-dependencies.bat` first

### Can't access from other devices
- Check Windows Firewall settings
- Ensure devices are on same network
- Try the IP address shown in startup message
- Allow Node.js through Windows Firewall

### Login issues
- Use the default credentials above
- Clear browser cache
- Try incognito/private browsing mode
- Check browser console for errors

### Empty dashboard/requests pages
- This issue has been fixed in this deployment
- The new architecture ensures proper rendering
- If still experiencing issues, check browser console

## 🔧 Technical Details

### Architecture
- **Backend**: Node.js with Express
- **Frontend**: Vanilla JavaScript with modern ES6+ features
- **Session Management**: In-memory with proper isolation
- **Database**: In-memory storage (resets on server restart)

### Key Fixes in This Version
- ✅ **Fixed rendering issues** - Complete rewrite of frontend
- ✅ **Proper session management** - Each user gets isolated session
- ✅ **Role-based navigation** - Correct tabs for each user type
- ✅ **Session persistence** - Users stay logged in on page refresh
- ✅ **Network accessibility** - Works across local network
- ✅ **Error handling** - Comprehensive error messages and logging

### API Endpoints
- `POST /api/login` - User authentication
- `GET /api/auth/status` - Check authentication status
- `POST /api/logout` - User logout
- `GET /api/books` - Get all books
- `GET /api/dashboard/stats` - Dashboard statistics (admin/librarian)
- `GET /api/rental-requests` - Get rental requests
- `POST /api/rental-requests` - Create rental request
- `PATCH /api/rental-requests/:id` - Approve/reject request
- `GET /api/rentals` - Get user rentals

### File Structure
```
windows-deployment/
├── server.js              # Main server application
├── package.json           # Node.js dependencies
├── start-iqraa.bat        # Main startup script
├── install-dependencies.bat # Dependency installer
├── public/
│   ├── index.html         # Main application UI
│   ├── app.js            # Frontend JavaScript
│   └── favicon.png       # Application icon
└── README.md             # This documentation
```
