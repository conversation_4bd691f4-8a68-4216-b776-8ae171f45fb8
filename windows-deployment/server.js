const express = require('express');
const cors = require('cors');
const path = require('path');
const os = require('os');

const app = express();
const PORT = process.env.PORT || 5000;

// Enhanced session management
const activeSessions = new Map(); // sessionId -> user data
const userSessions = new Map(); // userId -> sessionId

// Generate session ID
function generateSessionId() {
  return 'session-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
}

// Get user from request (check session)
function getCurrentUser(req) {
  // Try to get session from various sources
  const sessionId = req.headers['x-session-id'] || 
                   req.headers['authorization']?.replace('Bearer ', '') ||
                   req.cookies?.sessionId;
  
  if (sessionId && activeSessions.has(sessionId)) {
    return activeSessions.get(sessionId);
  }
  return null;
}

// In-memory storage for rental requests and active rentals
let rentalRequests = [];
let activeRentals = [];
let requestIdCounter = 1;
let rentalIdCounter = 1;

// Get local IP address
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return 'localhost';
}

// User permissions based on role
function getUserPermissions(role) {
  const permissions = {
    admin: {
      canManageBooks: true,
      canManageUsers: true,
      canApproveRentals: true,
      canViewDashboard: true,
      canSearchBooks: true,
      canRequestRentals: true,
      canViewAllRentals: true
    },
    librarian: {
      canManageBooks: true,
      canManageUsers: false,
      canApproveRentals: true,
      canViewDashboard: true,
      canSearchBooks: true,
      canRequestRentals: true,
      canViewAllRentals: true
    },
    user: {
      canManageBooks: false,
      canManageUsers: false,
      canApproveRentals: false,
      canViewDashboard: false,
      canSearchBooks: true,
      canRequestRentals: true,
      canViewAllRentals: false
    }
  };
  return permissions[role] || permissions.user;
}

// CORS configuration
app.use(cors({
  origin: '*',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin', 'X-Session-ID'],
}));

app.use(express.json());

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Mock books data
const books = [
  { id: 1, title: 'Introduction to Library Science', author: 'Dr. Ahmed Hassan', isbn: '978-1234567890', category: 'Education', status: 'available', year: 2020 },
  { id: 2, title: 'Digital Library Management', author: 'Prof. Sarah Johnson', isbn: '978-1234567891', category: 'Technology', status: 'available', year: 2021 },
  { id: 3, title: 'Islamic History and Culture', author: 'Dr. Omar Al-Rashid', isbn: '978-1234567892', category: 'History', status: 'available', year: 2019 },
  { id: 4, title: 'To Kill a Mockingbird', author: 'Harper Lee', isbn: '978-1234567893', category: 'Literature', status: 'available', year: 1960 },
  { id: 5, title: 'World Literature Anthology', author: 'Prof. Elena Rodriguez', isbn: '978-1234567894', category: 'Literature', status: 'available', year: 2022 }
];

// Authentication endpoints
app.post('/api/login', (req, res) => {
  console.log('Login request:', req.body);
  const { username, password } = req.body;

  // Simple authentication
  const validUsers = {
    admin: { password: 'admin123', role: 'admin', id: 1, email: '<EMAIL>', name: 'Administrator' },
    librarian: { password: 'librarian123', role: 'librarian', id: 2, email: '<EMAIL>', name: 'Librarian' },
    user: { password: 'user123', role: 'user', id: 3, email: '<EMAIL>', name: 'Library User' }
  };

  if (validUsers[username] && validUsers[username].password === password) {
    const userData = validUsers[username];
    const user = {
      id: userData.id,
      username: username,
      role: userData.role,
      email: userData.email,
      name: userData.name,
      permissions: getUserPermissions(userData.role)
    };

    // Create session
    const sessionId = generateSessionId();
    activeSessions.set(sessionId, user);
    userSessions.set(user.id, sessionId);

    console.log('Login successful for:', username, 'Session:', sessionId);
    res.json({
      success: true,
      user: user,
      token: sessionId,
      sessionId: sessionId
    });
  } else {
    console.log('Login failed for:', username);
    res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }
});

// Check authentication status
app.get('/api/auth/status', (req, res) => {
  const currentUser = getCurrentUser(req);
  if (currentUser) {
    res.json({
      success: true,
      authenticated: true,
      user: currentUser
    });
  } else {
    res.json({
      success: true,
      authenticated: false,
      user: null
    });
  }
});

// Logout endpoints
app.post('/api/auth/logout', (req, res) => {
  console.log('Logout request');
  const currentUser = getCurrentUser(req);
  
  if (currentUser) {
    // Remove user's session
    const sessionId = userSessions.get(currentUser.id);
    if (sessionId) {
      activeSessions.delete(sessionId);
      userSessions.delete(currentUser.id);
    }
  }

  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

app.post('/api/logout', (req, res) => {
  console.log('Alternative logout request');
  const currentUser = getCurrentUser(req);
  
  if (currentUser) {
    // Remove user's session
    const sessionId = userSessions.get(currentUser.id);
    if (sessionId) {
      activeSessions.delete(sessionId);
      userSessions.delete(currentUser.id);
    }
  }

  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

// Books endpoint
app.get('/api/books', (req, res) => {
  res.json({
    success: true,
    data: books,
    total: books.length
  });
});

// Rental request endpoints
app.post('/api/rental-requests', (req, res) => {
  console.log('Rental request:', req.body);
  const { bookId, notes } = req.body;
  const currentUser = getCurrentUser(req);

  if (!currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  // Check if user has permission to request rentals
  if (!currentUser.permissions.canRequestRentals) {
    return res.status(403).json({
      success: false,
      message: 'You do not have permission to request book rentals'
    });
  }

  const request = {
    id: requestIdCounter++,
    bookId: parseInt(bookId),
    userId: currentUser.id,
    username: currentUser.username,
    userEmail: currentUser.email,
    status: 'pending',
    notes: notes || '',
    requestDate: new Date().toISOString(),
    approvedBy: null,
    approvedDate: null
  };

  rentalRequests.push(request);
  console.log(`Created rental request ${request.id} for user ${currentUser.username}`);

  res.json({
    success: true,
    message: 'Rental request submitted successfully',
    data: request
  });
});

// Get rental requests (filtered by user role)
app.get('/api/rental-requests', (req, res) => {
  const currentUser = getCurrentUser(req);
  if (!currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  let filteredRequests = rentalRequests;

  // Regular users can only see their own requests
  if (currentUser.role === 'user') {
    filteredRequests = rentalRequests.filter(req => req.userId === currentUser.id);
  }
  // Admins and librarians can see all requests

  res.json({
    success: true,
    data: filteredRequests,
    total: filteredRequests.length
  });
});

// Approve/reject rental request (admin/librarian only)
app.patch('/api/rental-requests/:id', (req, res) => {
  const requestId = parseInt(req.params.id);
  const { status, notes } = req.body;
  const currentUser = getCurrentUser(req);

  if (!currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  if (!currentUser.permissions.canApproveRentals) {
    return res.status(403).json({
      success: false,
      message: 'You do not have permission to approve rental requests'
    });
  }

  const requestIndex = rentalRequests.findIndex(req => req.id === requestId);
  if (requestIndex === -1) {
    return res.status(404).json({
      success: false,
      message: 'Rental request not found'
    });
  }

  const request = rentalRequests[requestIndex];

  if (status === 'approved') {
    // Create active rental
    const rental = {
      id: rentalIdCounter++,
      bookId: request.bookId,
      userId: request.userId,
      username: request.username,
      userEmail: request.userEmail,
      status: 'active',
      borrowDate: new Date().toISOString(),
      dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days from now
      approvedBy: currentUser.username,
      notes: notes || request.notes
    };

    activeRentals.push(rental);

    // Update book status
    const book = books.find(b => b.id === request.bookId);
    if (book) {
      book.status = 'borrowed';
    }

    console.log(`Approved rental request ${requestId}, created rental ${rental.id}`);
  }

  // Update request status
  request.status = status;
  request.approvedBy = currentUser.username;
  request.approvedDate = new Date().toISOString();
  request.adminNotes = notes;

  res.json({
    success: true,
    message: `Rental request ${status} successfully`,
    data: request
  });
});

// Get user's active rentals
app.get('/api/rentals', (req, res) => {
  const currentUser = getCurrentUser(req);
  if (!currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  let userRentals = activeRentals;

  // Regular users can only see their own rentals
  if (currentUser.role === 'user') {
    userRentals = activeRentals.filter(rental => rental.userId === currentUser.id);
  }
  // Admins and librarians can see all rentals

  // Enhance rentals with book information
  const rentalsWithBooks = userRentals.map(rental => {
    const book = books.find(b => b.id === rental.bookId) || {
      id: rental.bookId,
      title: 'Unknown Book',
      author: 'Unknown Author'
    };

    return {
      ...rental,
      book: book
    };
  });

  res.json({
    success: true,
    data: rentalsWithBooks,
    total: rentalsWithBooks.length
  });
});

// Dashboard stats endpoint (admin/librarian only)
app.get('/api/dashboard/stats', (req, res) => {
  const currentUser = getCurrentUser(req);
  if (!currentUser || !currentUser.permissions.canViewDashboard) {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Dashboard access requires admin or librarian privileges.'
    });
  }

  const stats = {
    totalBooks: books.length,
    availableBooks: books.filter(book => book.status === 'available').length,
    borrowedBooks: books.filter(book => book.status === 'borrowed').length,
    totalMembers: 3, // admin, librarian, user
    activeMembers: activeSessions.size,
    newMembersThisMonth: 1,
    pendingRequests: rentalRequests.filter(req => req.status === 'pending').length,
    activeRentals: activeRentals.filter(rental => rental.status === 'active').length,
    overdueBooks: 0
  };

  res.json({
    success: true,
    data: stats
  });
});

// Serve the main application
app.get('/', (req, res) => {
  console.log('Serving main app');
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Start server
const localIP = getLocalIP();
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🌐 Detected local IP: ${localIP}`);
  console.log('');
  console.log('🚀 IqraaManager Network Server Started!');
  console.log('========================================');
  console.log(`🌐 Main App: http://${localIP}:${PORT}`);
  console.log(`📊 Health check: http://${localIP}:${PORT}/health`);
  console.log(`🔗 API status: http://${localIP}:${PORT}/api/auth/status`);
  console.log('');
  console.log('📱 Access from other devices:');
  console.log(`   Connect to same WiFi and open: http://${localIP}:${PORT}`);
  console.log('');
  console.log('👤 Default login credentials:');
  console.log('   👑 Admin: admin / admin123 (Full access)');
  console.log('   📚 Librarian: librarian / librarian123 (Manage books, approve rentals)');
  console.log('   👤 User: user / user123 (Search books, request rentals)');
  console.log('');
  console.log('✨ Features:');
  console.log('   • Role-based access control');
  console.log('   • Book search and browsing');
  console.log('   • Rental request workflow');
  console.log('   • Admin/Librarian approval system');
  console.log('   • Network-accessible from any device');
  console.log('');
  console.log('⛔ Press Ctrl+C to stop the server');
  console.log('========================================');
});
