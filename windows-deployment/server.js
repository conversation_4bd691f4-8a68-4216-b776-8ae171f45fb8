const express = require('express');
const cors = require('cors');
const path = require('path');
const os = require('os');

const app = express();
const PORT = process.env.PORT || 5000;

// Enhanced session management
const activeSessions = new Map(); // sessionId -> user data
const userSessions = new Map(); // userId -> sessionId

// Generate session ID
function generateSessionId() {
  return 'session-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
}

// Get user from request (check session)
function getCurrentUser(req) {
  // Try to get session from various sources
  const sessionId = req.headers['x-session-id'] || 
                   req.headers['authorization']?.replace('Bearer ', '') ||
                   req.cookies?.sessionId;
  
  if (sessionId && activeSessions.has(sessionId)) {
    return activeSessions.get(sessionId);
  }
  return null;
}

// In-memory storage for rental requests and active rentals
let rentalRequests = [];
let activeRentals = [];
let requestIdCounter = 1;
let rentalIdCounter = 1;

// Get local IP address
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  for (const name of Object.keys(interfaces)) {
    for (const interface of interfaces[name]) {
      if (interface.family === 'IPv4' && !interface.internal) {
        return interface.address;
      }
    }
  }
  return 'localhost';
}

// User permissions based on role
function getUserPermissions(role) {
  const permissions = {
    admin: {
      canManageBooks: true,
      canManageUsers: true,
      canApproveRentals: true,
      canViewDashboard: true,
      canSearchBooks: true,
      canRequestRentals: true,
      canViewAllRentals: true
    },
    librarian: {
      canManageBooks: true,
      canManageUsers: false,
      canApproveRentals: true,
      canViewDashboard: true,
      canSearchBooks: true,
      canRequestRentals: true,
      canViewAllRentals: true
    },
    user: {
      canManageBooks: false,
      canManageUsers: false,
      canApproveRentals: false,
      canViewDashboard: false,
      canSearchBooks: true,
      canRequestRentals: true,
      canViewAllRentals: false
    }
  };
  return permissions[role] || permissions.user;
}

// CORS configuration
app.use(cors({
  origin: '*',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin', 'X-Session-ID'],
}));

app.use(express.json());

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Mock books data
const books = [
  { id: 1, title: 'Introduction to Library Science', author: 'Dr. Ahmed Hassan', isbn: '978-1234567890', category: 'Education', status: 'available', year: 2020 },
  { id: 2, title: 'Digital Library Management', author: 'Prof. Sarah Johnson', isbn: '978-1234567891', category: 'Technology', status: 'available', year: 2021 },
  { id: 3, title: 'Islamic History and Culture', author: 'Dr. Omar Al-Rashid', isbn: '978-1234567892', category: 'History', status: 'available', year: 2019 },
  { id: 4, title: 'To Kill a Mockingbird', author: 'Harper Lee', isbn: '978-1234567893', category: 'Literature', status: 'available', year: 1960 },
  { id: 5, title: 'World Literature Anthology', author: 'Prof. Elena Rodriguez', isbn: '978-1234567894', category: 'Literature', status: 'available', year: 2022 }
];

// Authentication endpoints
app.post('/api/login', (req, res) => {
  console.log('Login request:', req.body);
  const { username, password } = req.body;

  // Simple authentication
  const validUsers = {
    admin: { password: 'admin123', role: 'admin', id: 1, email: '<EMAIL>', name: 'Administrator' },
    librarian: { password: 'librarian123', role: 'librarian', id: 2, email: '<EMAIL>', name: 'Librarian' },
    user: { password: 'user123', role: 'user', id: 3, email: '<EMAIL>', name: 'Library User' }
  };

  if (validUsers[username] && validUsers[username].password === password) {
    const userData = validUsers[username];
    const user = {
      id: userData.id,
      username: username,
      role: userData.role,
      email: userData.email,
      name: userData.name,
      permissions: getUserPermissions(userData.role)
    };

    // Create session
    const sessionId = generateSessionId();
    activeSessions.set(sessionId, user);
    userSessions.set(user.id, sessionId);

    console.log('Login successful for:', username, 'Session:', sessionId);
    res.json({
      success: true,
      user: user,
      token: sessionId,
      sessionId: sessionId
    });
  } else {
    console.log('Login failed for:', username);
    res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }
});

// Check authentication status
app.get('/api/auth/status', (req, res) => {
  const currentUser = getCurrentUser(req);
  if (currentUser) {
    res.json({
      success: true,
      authenticated: true,
      user: currentUser
    });
  } else {
    res.json({
      success: true,
      authenticated: false,
      user: null
    });
  }
});

// Logout endpoints
app.post('/api/auth/logout', (req, res) => {
  console.log('Logout request');
  const currentUser = getCurrentUser(req);
  
  if (currentUser) {
    // Remove user's session
    const sessionId = userSessions.get(currentUser.id);
    if (sessionId) {
      activeSessions.delete(sessionId);
      userSessions.delete(currentUser.id);
    }
  }

  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

app.post('/api/logout', (req, res) => {
  console.log('Alternative logout request');
  const currentUser = getCurrentUser(req);
  
  if (currentUser) {
    // Remove user's session
    const sessionId = userSessions.get(currentUser.id);
    if (sessionId) {
      activeSessions.delete(sessionId);
      userSessions.delete(currentUser.id);
    }
  }

  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

// Books endpoint
app.get('/api/books', (req, res) => {
  res.json({
    success: true,
    data: books,
    total: books.length
  });
});

// Add new book endpoint (admin and librarian)
app.post('/api/books', (req, res) => {
  const currentUser = getCurrentUser(req);
  if (!currentUser || (!currentUser.permissions.canManageBooks && !currentUser.permissions.canApproveRentals)) {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Book management requires admin or librarian privileges.'
    });
  }

  const { title, author, isbn, category, year } = req.body;

  if (!title || !author || !isbn || !category || !year) {
    return res.status(400).json({
      success: false,
      message: 'All fields are required: title, author, isbn, category, year'
    });
  }

  const newBook = {
    id: books.length + 1,
    title: title.trim(),
    author: author.trim(),
    isbn: isbn.trim(),
    category: category.trim(),
    year: parseInt(year),
    status: 'available'
  };

  books.push(newBook);
  console.log(`New book added: ${newBook.title} by ${currentUser.username}`);

  res.json({
    success: true,
    message: 'Book added successfully',
    data: newBook
  });
});

// Users management endpoints
app.get('/api/users', (req, res) => {
  const currentUser = getCurrentUser(req);
  if (!currentUser || !currentUser.permissions.canManageUsers) {
    return res.status(403).json({
      success: false,
      message: 'Access denied. User management requires admin privileges.'
    });
  }

  // Return mock user data (in real app, this would come from database)
  const users = [
    { id: 1, username: 'admin', role: 'admin', email: '<EMAIL>', name: 'Administrator', status: 'active' },
    { id: 2, username: 'librarian', role: 'librarian', email: '<EMAIL>', name: 'Librarian', status: 'active' },
    { id: 3, username: 'user', role: 'user', email: '<EMAIL>', name: 'Library User', status: 'active' }
  ];

  res.json({
    success: true,
    data: users,
    total: users.length
  });
});

// Add new user endpoint (admin and librarian)
app.post('/api/users', (req, res) => {
  const currentUser = getCurrentUser(req);
  if (!currentUser || (!currentUser.permissions.canManageUsers && !currentUser.permissions.canApproveRentals)) {
    return res.status(403).json({
      success: false,
      message: 'Access denied. User management requires admin or librarian privileges.'
    });
  }

  const { username, email, name, role, status, password } = req.body;

  if (!username || !email || !name || !role || !password) {
    return res.status(400).json({
      success: false,
      message: 'All fields are required: username, email, name, role, password'
    });
  }

  // In a real app, you would save to database and hash the password
  const newUser = {
    id: Date.now(), // Simple ID generation
    username: username.trim(),
    email: email.trim(),
    name: name.trim(),
    role: role,
    status: status || 'active',
    createdBy: currentUser.username,
    createdAt: new Date().toISOString()
  };

  console.log(`New user created: ${newUser.username} (${newUser.role}) by ${currentUser.username}`);

  res.json({
    success: true,
    message: 'User created successfully',
    data: newUser
  });
});

// Update user status endpoint (admin and librarian)
app.patch('/api/users/:id/status', (req, res) => {
  const userId = parseInt(req.params.id);
  const { status } = req.body;
  const currentUser = getCurrentUser(req);

  if (!currentUser || (!currentUser.permissions.canManageUsers && !currentUser.permissions.canApproveRentals)) {
    return res.status(403).json({
      success: false,
      message: 'Access denied. User status management requires admin or librarian privileges.'
    });
  }

  if (!['active', 'disabled'].includes(status)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid status. Must be "active" or "disabled".'
    });
  }

  // In real app, update database
  console.log(`User ${userId} status changed to ${status} by ${currentUser.username}`);

  res.json({
    success: true,
    message: `User status updated to ${status}`,
    data: { userId, status, updatedBy: currentUser.username }
  });
});

// Delete user endpoint (admin only)
app.delete('/api/users/:id', (req, res) => {
  const userId = parseInt(req.params.id);
  const currentUser = getCurrentUser(req);

  if (!currentUser || !currentUser.permissions.canManageUsers) {
    return res.status(403).json({
      success: false,
      message: 'Access denied. User deletion requires admin privileges.'
    });
  }

  if (userId === 1) { // Prevent deleting admin user
    return res.status(400).json({
      success: false,
      message: 'Cannot delete the main admin user.'
    });
  }

  // In real app, delete from database
  console.log(`User ${userId} deleted by ${currentUser.username}`);

  res.json({
    success: true,
    message: 'User deleted successfully',
    data: { userId, deletedBy: currentUser.username }
  });
});

// Rental request endpoints
app.post('/api/rental-requests', (req, res) => {
  console.log('Rental request:', req.body);
  const { bookId, notes } = req.body;
  const currentUser = getCurrentUser(req);

  if (!currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  // Check if user has permission to request rentals
  if (!currentUser.permissions.canRequestRentals) {
    return res.status(403).json({
      success: false,
      message: 'You do not have permission to request book rentals'
    });
  }

  const request = {
    id: requestIdCounter++,
    bookId: parseInt(bookId),
    userId: currentUser.id,
    username: currentUser.username,
    userEmail: currentUser.email,
    status: 'pending',
    notes: notes || '',
    requestDate: new Date().toISOString(),
    approvedBy: null,
    approvedDate: null
  };

  rentalRequests.push(request);
  console.log(`Created rental request ${request.id} for user ${currentUser.username}`);

  res.json({
    success: true,
    message: 'Rental request submitted successfully',
    data: request
  });
});

// Get rental requests (filtered by user role)
app.get('/api/rental-requests', (req, res) => {
  const currentUser = getCurrentUser(req);
  if (!currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  let filteredRequests = rentalRequests;

  // Regular users can only see their own requests
  if (currentUser.role === 'user') {
    filteredRequests = rentalRequests.filter(req => req.userId === currentUser.id);
  }
  // Admins and librarians can see all requests

  res.json({
    success: true,
    data: filteredRequests,
    total: filteredRequests.length
  });
});

// Approve/reject rental request (admin/librarian only)
app.patch('/api/rental-requests/:id', (req, res) => {
  const requestId = parseInt(req.params.id);
  const { status, notes } = req.body;
  const currentUser = getCurrentUser(req);

  if (!currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  if (!currentUser.permissions.canApproveRentals) {
    return res.status(403).json({
      success: false,
      message: 'You do not have permission to approve rental requests'
    });
  }

  const requestIndex = rentalRequests.findIndex(req => req.id === requestId);
  if (requestIndex === -1) {
    return res.status(404).json({
      success: false,
      message: 'Rental request not found'
    });
  }

  const request = rentalRequests[requestIndex];

  if (status === 'approved') {
    // Create active rental
    const rental = {
      id: rentalIdCounter++,
      bookId: request.bookId,
      userId: request.userId,
      username: request.username,
      userEmail: request.userEmail,
      status: 'active',
      borrowDate: new Date().toISOString(),
      dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days from now
      approvedBy: currentUser.username,
      notes: notes || request.notes
    };

    activeRentals.push(rental);

    // Update book status
    const book = books.find(b => b.id === request.bookId);
    if (book) {
      book.status = 'borrowed';
    }

    console.log(`Approved rental request ${requestId}, created rental ${rental.id}`);
  }

  // Update request status
  request.status = status;
  request.approvedBy = currentUser.username;
  request.approvedDate = new Date().toISOString();
  request.adminNotes = notes;

  res.json({
    success: true,
    message: `Rental request ${status} successfully`,
    data: request
  });
});

// Get user's active rentals
app.get('/api/rentals', (req, res) => {
  const currentUser = getCurrentUser(req);
  if (!currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  let userRentals = activeRentals;

  // Regular users can only see their own rentals
  if (currentUser.role === 'user') {
    userRentals = activeRentals.filter(rental => rental.userId === currentUser.id);
  }
  // Admins and librarians can see all rentals

  // Enhance rentals with book information
  const rentalsWithBooks = userRentals.map(rental => {
    const book = books.find(b => b.id === rental.bookId) || {
      id: rental.bookId,
      title: 'Unknown Book',
      author: 'Unknown Author'
    };

    return {
      ...rental,
      book: book
    };
  });

  res.json({
    success: true,
    data: rentalsWithBooks,
    total: rentalsWithBooks.length
  });
});

// Return book endpoint (admin/librarian only)
app.patch('/api/rentals/:id/return', (req, res) => {
  const rentalId = parseInt(req.params.id);
  const { notes } = req.body;
  const currentUser = getCurrentUser(req);

  if (!currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  if (!currentUser.permissions.canApproveRentals) {
    return res.status(403).json({
      success: false,
      message: 'You do not have permission to process book returns'
    });
  }

  const rentalIndex = activeRentals.findIndex(rental => rental.id === rentalId);
  if (rentalIndex === -1) {
    return res.status(404).json({
      success: false,
      message: 'Rental not found'
    });
  }

  const rental = activeRentals[rentalIndex];

  // Update book status back to available
  const book = books.find(b => b.id === rental.bookId);
  if (book) {
    book.status = 'available';
  }

  // Update rental record
  rental.status = 'returned';
  rental.returnDate = new Date().toISOString();
  rental.returnedBy = currentUser.username;
  rental.returnNotes = notes || '';

  console.log(`Book returned: Rental ${rentalId} for book ${rental.bookId} by ${currentUser.username}`);

  res.json({
    success: true,
    message: 'Book returned successfully',
    data: rental
  });
});

// Dashboard stats endpoint (admin/librarian only)
app.get('/api/dashboard/stats', (req, res) => {
  const currentUser = getCurrentUser(req);
  if (!currentUser || !currentUser.permissions.canViewDashboard) {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Dashboard access requires admin or librarian privileges.'
    });
  }

  const stats = {
    totalBooks: books.length,
    availableBooks: books.filter(book => book.status === 'available').length,
    borrowedBooks: books.filter(book => book.status === 'borrowed').length,
    totalMembers: 3, // admin, librarian, user
    activeMembers: activeSessions.size,
    newMembersThisMonth: 1,
    pendingRequests: rentalRequests.filter(req => req.status === 'pending').length,
    activeRentals: activeRentals.filter(rental => rental.status === 'active').length,
    overdueBooks: 0
  };

  res.json({
    success: true,
    data: stats
  });
});

// Serve the main application
app.get('/', (req, res) => {
  console.log('Serving main app');
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// System information endpoint
app.get('/api/system/info', (req, res) => {
  const currentUser = getCurrentUser(req);
  if (!currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  const systemInfo = {
    version: '1.0.0',
    build: 'Windows Deployment',
    uptime: Math.floor(process.uptime()),
    activeSessions: activeSessions.size,
    totalBooks: books.length,
    totalRequests: rentalRequests.length,
    totalRentals: activeRentals.length,
    serverTime: new Date().toISOString(),
    nodeVersion: process.version,
    platform: process.platform,
    memory: {
      used: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
      total: Math.round(process.memoryUsage().heapTotal / 1024 / 1024)
    }
  };

  res.json({
    success: true,
    data: systemInfo
  });
});

// Rental settings endpoints
app.get('/api/settings/rental', (req, res) => {
  const currentUser = getCurrentUser(req);
  if (!currentUser || !currentUser.permissions.canApproveRentals) {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Rental settings require admin or librarian privileges.'
    });
  }

  // Return current rental settings (in real app, from database)
  const settings = {
    defaultRentalDays: 14,
    maxRentalsPerUser: 3,
    allowRenewals: true,
    overdueGracePeriod: 3,
    autoReminders: true
  };

  res.json({
    success: true,
    data: settings
  });
});

app.patch('/api/settings/rental', (req, res) => {
  const currentUser = getCurrentUser(req);
  if (!currentUser || !currentUser.permissions.canApproveRentals) {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Rental settings require admin or librarian privileges.'
    });
  }

  const { defaultRentalDays, maxRentalsPerUser } = req.body;

  // In real app, save to database
  console.log(`Rental settings updated by ${currentUser.username}:`, { defaultRentalDays, maxRentalsPerUser });

  res.json({
    success: true,
    message: 'Rental settings updated successfully',
    data: { defaultRentalDays, maxRentalsPerUser }
  });
});

// Bulk operations endpoints
app.post('/api/rentals/bulk-approve', (req, res) => {
  const currentUser = getCurrentUser(req);
  if (!currentUser || !currentUser.permissions.canApproveRentals) {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Bulk approval requires admin or librarian privileges.'
    });
  }

  const pendingRequests = rentalRequests.filter(req => req.status === 'pending');
  let approvedCount = 0;

  pendingRequests.forEach(request => {
    // Create active rental
    const rental = {
      id: rentalIdCounter++,
      bookId: request.bookId,
      userId: request.userId,
      username: request.username,
      userEmail: request.userEmail,
      status: 'active',
      borrowDate: new Date().toISOString(),
      dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
      approvedBy: currentUser.username,
      notes: 'Bulk approved'
    };

    activeRentals.push(rental);

    // Update book status
    const book = books.find(b => b.id === request.bookId);
    if (book) {
      book.status = 'borrowed';
    }

    // Update request status
    request.status = 'approved';
    request.approvedBy = currentUser.username;
    request.approvedDate = new Date().toISOString();

    approvedCount++;
  });

  console.log(`Bulk approved ${approvedCount} requests by ${currentUser.username}`);

  res.json({
    success: true,
    message: `Successfully approved ${approvedCount} rental requests`,
    data: { approvedCount }
  });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// Start server
const localIP = getLocalIP();
app.listen(PORT, '0.0.0.0', () => {
  console.log(`🌐 Detected local IP: ${localIP}`);
  console.log('');
  console.log('🚀 IqraaManager Network Server Started!');
  console.log('========================================');
  console.log(`🌐 Main App: http://${localIP}:${PORT}`);
  console.log(`📊 Health check: http://${localIP}:${PORT}/health`);
  console.log(`🔗 API status: http://${localIP}:${PORT}/api/auth/status`);
  console.log('');
  console.log('📱 Access from other devices:');
  console.log(`   Connect to same WiFi and open: http://${localIP}:${PORT}`);
  console.log('');
  console.log('👤 Default login credentials:');
  console.log('   👑 Admin: admin / admin123 (Full access)');
  console.log('   📚 Librarian: librarian / librarian123 (Manage books, approve rentals)');
  console.log('   👤 User: user / user123 (Search books, request rentals)');
  console.log('');
  console.log('✨ Features:');
  console.log('   • Role-based access control');
  console.log('   • Book search and browsing');
  console.log('   • Rental request workflow');
  console.log('   • Admin/Librarian approval system');
  console.log('   • Network-accessible from any device');
  console.log('');
  console.log('⛔ Press Ctrl+C to stop the server');
  console.log('========================================');
});
