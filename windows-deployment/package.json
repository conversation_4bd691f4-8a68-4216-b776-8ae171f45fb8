{"name": "<PERSON><PERSON><PERSON><PERSON>-manager", "version": "1.0.0", "description": "IqraaManager - Library Management System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["library", "management", "books", "rental", "iqraa"], "author": "IqraaManager Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "engines": {"node": ">=16.0.0"}}