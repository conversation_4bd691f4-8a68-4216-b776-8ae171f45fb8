@echo off
echo ==================================
echo CapRover Tarball ULTRA-FINAL
echo ==================================

REM Creer un repertoire temporaire
set TEMP_DIR=caprover_build
mkdir %TEMP_DIR% 2>nul

echo Preparation des fichiers pour l'archive...

REM Copier tous les fichiers necessaires
xcopy client %TEMP_DIR%\client\ /E /I /Y
xcopy server %TEMP_DIR%\server\ /E /I /Y
xcopy shared %TEMP_DIR%\shared\ /E /I /Y
xcopy drizzle %TEMP_DIR%\drizzle\ /E /I /Y
if exist public xcopy public %TEMP_DIR%\public\ /E /I /Y

REM Ajouter un fichier server/index.js minimal pour le build et le deploiement
mkdir %TEMP_DIR%\dist\server 2>nul
echo // Fichier index.js minimal pour CapRover > %TEMP_DIR%\dist\server\index.js
echo const express = require('express'); >> %TEMP_DIR%\dist\server\index.js
echo const app = express(); >> %TEMP_DIR%\dist\server\index.js
echo const port = process.env.PORT || 80; >> %TEMP_DIR%\dist\server\index.js
echo. >> %TEMP_DIR%\dist\server\index.js
echo app.use(express.static('dist/client')); >> %TEMP_DIR%\dist\server\index.js
echo. >> %TEMP_DIR%\dist\server\index.js
echo app.get('/api/health', (req, res) ^=> { >> %TEMP_DIR%\dist\server\index.js
echo   res.json({ status: 'ok', timestamp: new Date().toISOString() }); >> %TEMP_DIR%\dist\server\index.js
echo }); >> %TEMP_DIR%\dist\server\index.js
echo. >> %TEMP_DIR%\dist\server\index.js
echo app.listen(port, () ^=> { >> %TEMP_DIR%\dist\server\index.js
echo   console.log(`Server running on port ${port}`); >> %TEMP_DIR%\dist\server\index.js
echo }); >> %TEMP_DIR%\dist\server\index.js

REM Creer un fichier HTML de base
mkdir %TEMP_DIR%\dist\client 2>nul
echo ^<!DOCTYPE html^> > %TEMP_DIR%\dist\client\index.html
echo ^<html^> >> %TEMP_DIR%\dist\client\index.html
echo ^<head^> >> %TEMP_DIR%\dist\client\index.html
echo   ^<meta charset="UTF-8"^> >> %TEMP_DIR%\dist\client\index.html
echo   ^<title^>iQraa^</title^> >> %TEMP_DIR%\dist\client\index.html
echo   ^<style^> >> %TEMP_DIR%\dist\client\index.html
echo     body { font-family: Arial; text-align: center; padding: 50px; } >> %TEMP_DIR%\dist\client\index.html
echo     h1 { color: #2e7d32; } >> %TEMP_DIR%\dist\client\index.html
echo     .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); } >> %TEMP_DIR%\dist\client\index.html
echo   ^</style^> >> %TEMP_DIR%\dist\client\index.html
echo ^</head^> >> %TEMP_DIR%\dist\client\index.html
echo ^<body style="background-color: #f5f5f5;"^> >> %TEMP_DIR%\dist\client\index.html
echo   ^<div class="container"^> >> %TEMP_DIR%\dist\client\index.html
echo     ^<h1^>iQraa - Version en ligne^</h1^> >> %TEMP_DIR%\dist\client\index.html
echo     ^<p^>L'application a ete deploye avec succes sur CapRover!^</p^> >> %TEMP_DIR%\dist\client\index.html
echo     ^<p^>Verifiez l'etat du serveur: ^<a href="/api/health"^>Verification de sante^</a^>^</p^> >> %TEMP_DIR%\dist\client\index.html
echo     ^<hr^> >> %TEMP_DIR%\dist\client\index.html
echo     ^<p^>^<small^>Date de deploiement: %DATE% %TIME%^</small^>^</p^> >> %TEMP_DIR%\dist\client\index.html
echo   ^</div^> >> %TEMP_DIR%\dist\client\index.html
echo ^</body^> >> %TEMP_DIR%\dist\client\index.html
echo ^</html^> >> %TEMP_DIR%\dist\client\index.html

REM Creer un package.json qui n'a pas besoin de build (le build est deja inclus)
echo { > %TEMP_DIR%\package.json
echo   "name": "iqraa-app", >> %TEMP_DIR%\package.json
echo   "version": "1.0.0", >> %TEMP_DIR%\package.json
echo   "scripts": { >> %TEMP_DIR%\package.json
echo     "start": "node dist/server/index.js" >> %TEMP_DIR%\package.json
echo   }, >> %TEMP_DIR%\package.json
echo   "dependencies": { >> %TEMP_DIR%\package.json
echo     "express": "^4.21.2" >> %TEMP_DIR%\package.json
echo   } >> %TEMP_DIR%\package.json
echo } >> %TEMP_DIR%\package.json

REM Creer un Dockerfile ultra-simple (pas de build necessaire)
echo FROM node:20-slim > %TEMP_DIR%\Dockerfile
echo WORKDIR /app >> %TEMP_DIR%\Dockerfile
echo COPY package.json ./ >> %TEMP_DIR%\Dockerfile
echo RUN npm install >> %TEMP_DIR%\Dockerfile
echo COPY . . >> %TEMP_DIR%\Dockerfile
echo ENV NODE_ENV production >> %TEMP_DIR%\Dockerfile
echo ENV PORT 80 >> %TEMP_DIR%\Dockerfile
echo EXPOSE 80 >> %TEMP_DIR%\Dockerfile
echo CMD ["npm", "start"] >> %TEMP_DIR%\Dockerfile

REM Creer un captain-definition
echo { > %TEMP_DIR%\captain-definition
echo   "schemaVersion": 2, >> %TEMP_DIR%\captain-definition
echo   "dockerfilePath": "./Dockerfile" >> %TEMP_DIR%\captain-definition
echo } >> %TEMP_DIR%\captain-definition

REM Creer un ZIP avec PowerShell
echo Creation de l'archive ZIP...
powershell -Command "Compress-Archive -Path '%TEMP_DIR%\*' -DestinationPath 'iqraa-caprover-final.zip' -Force"

echo.
echo Archive ZIP creee: iqraa-caprover-final.zip
echo IMPORTANT: Renommez ce fichier en .tar.gz avant de le telecharger sur CapRover
echo.

REM Supprimer le repertoire temporaire
rmdir /S /Q %TEMP_DIR%