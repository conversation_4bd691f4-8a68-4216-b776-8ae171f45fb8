@echo off
echo 🔧 Script de correction des dépendances Windows pour iQraa
echo.

:: Suppression du package-lock et node_modules comme recommandé dans l'erreur
echo 🧹 Nettoyage des dépendances actuelles...
echo ⚠️ Suppression du package-lock.json et node_modules...
if exist package-lock.json del /f package-lock.json
if exist node_modules rmdir /s /q node_modules
echo ✅ Nettoyage terminé
echo.

:: Installation des dépendances spécifiques Windows
echo 📦 Installation des dépendances de base...
call npm install --no-package-lock

echo.
echo 📦 Installation de Vite globalement (nécessaire pour server/vite.ts)...
call npm install -g vite

echo.
echo 📦 Installation des dépendances Vite et React localement...
call npm install -D vite @vitejs/plugin-react --no-save
call npm install -D @tailwindcss/vite --no-save

echo.
echo 📦 Installation des modules natifs pour Windows...
call npm install -D @rollup/rollup-win32-x64-msvc --no-save

echo.
echo 📦 Installation de tsx pour le transpilage TypeScript...
call npm install -g tsx
call npm install -D tsx --no-save

echo.
echo 📦 Installation d'autres outils essentiels...
call npm install -D cross-env esbuild --no-save

echo.
echo 🔧 Configuration spécifique pour Windows...
:: Création d'un .npmrc temporaire pour Windows
echo legacy-peer-deps=true> .npmrc.windows
echo platform=win32>> .npmrc.windows
echo architecture=x64>> .npmrc.windows
echo msvc=true>> .npmrc.windows
copy .npmrc.windows .npmrc

echo.
echo 🔧 Création d'un fix temporaire pour résoudre les erreurs de modules...
set "TEMP_FIX_DIR=%TEMP%\iqraa-fix"
mkdir "%TEMP_FIX_DIR%" 2>nul

:: Réparation pour vite
echo module.exports = require('vite');> "%TEMP_FIX_DIR%\vite.js"
echo import vite from 'vite'; export default vite;> "%TEMP_FIX_DIR%\vite.mjs"
echo export * from 'vite';>> "%TEMP_FIX_DIR%\vite.mjs"

:: Réparation pour les plugins Replit
echo export default {};> "%TEMP_FIX_DIR%\runtime-error-modal.js"
echo export const cartographer = () => {};> "%TEMP_FIX_DIR%\cartographer.js"

:: Créer une version locale du fichier de configuration Vite
echo Création d'une configuration Vite locale sans les plugins Replit...
echo import { defineConfig } from "vite";> vite-local.config.ts
echo import react from "@vitejs/plugin-react";>> vite-local.config.ts
echo import path from "path";>> vite-local.config.ts
echo.>> vite-local.config.ts
echo // Version simplifiée de vite.config.ts sans les plugins Replit>> vite-local.config.ts
echo export default defineConfig({>> vite-local.config.ts
echo   plugins: [>> vite-local.config.ts
echo     react()>> vite-local.config.ts
echo   ],>> vite-local.config.ts
echo   resolve: {>> vite-local.config.ts
echo     alias: {>> vite-local.config.ts
echo       "@": path.resolve(__dirname, "client", "src"),>> vite-local.config.ts
echo       "@shared": path.resolve(__dirname, "shared"),>> vite-local.config.ts
echo       "@assets": path.resolve(__dirname, "attached_assets"),>> vite-local.config.ts
echo     },>> vite-local.config.ts
echo   },>> vite-local.config.ts
echo   root: path.resolve(__dirname, "client"),>> vite-local.config.ts
echo   build: {>> vite-local.config.ts
echo     outDir: path.resolve(__dirname, "dist/public"),>> vite-local.config.ts
echo     emptyOutDir: true,>> vite-local.config.ts
echo   },>> vite-local.config.ts
echo });>> vite-local.config.ts

echo.
echo ✅ Toutes les dépendances ont été installées ou mises à jour
echo ✅ Pour démarrer l'application, utilisez l'une des méthodes suivantes:
echo.
echo    1. Méthode simple tout-en-un (recommandée):
echo       start-windows.bat
echo.
echo    2. Méthode manuelle avec correctif:
echo       set NODE_PATH=%TEMP_FIX_DIR%;%NODE_PATH% ^&^& npx vite --config vite-local.config.ts
echo.
echo ⚠️ Si vous avez encore des problèmes, utilisez la méthode manuelle en deux étapes:
echo    - Terminal 1: set NODE_PATH=%TEMP_FIX_DIR% ^&^& npx cross-env NODE_ENV=development tsx server/index.ts
echo    - Terminal 2: set NODE_PATH=%TEMP_FIX_DIR% ^&^& npx vite --config vite-local.config.ts