@echo off
REM Script de nettoyage pour archiver les anciens fichiers de déploiement (Windows)

echo ==================================
echo Nettoyage des anciens fichiers de déploiement
echo ==================================

REM Créer le répertoire d'archive s'il n'existe pas
if not exist .deployment_archive mkdir .deployment_archive
if not exist .deployment_archive\scripts mkdir .deployment_archive\scripts
if not exist .deployment_archive\configs mkdir .deployment_archive\configs

echo Archivage des anciens fichiers de déploiement...

REM Déplacer les scripts
if exist switch-deployment.sh (
  move switch-deployment.sh .deployment_archive\scripts\
  echo Archivé: switch-deployment.sh
)

if exist build-electron.sh (
  move build-electron.sh .deployment_archive\scripts\
  echo Archivé: build-electron.sh
)

if exist test-capacitor.sh (
  move test-capacitor.sh .deployment_archive\scripts\
  echo Archivé: test-capacitor.sh
)

if exist start-sqlite.sh (
  move start-sqlite.sh .deployment_archive\scripts\
  echo Archivé: start-sqlite.sh
)

if exist start-local.sh (
  move start-local.sh .deployment_archive\scripts\
  echo Archivé: start-local.sh
)

if exist start-local.bat (
  move start-local.bat .deployment_archive\scripts\
  echo Archivé: start-local.bat
)

if exist test-electron.sh (
  move test-electron.sh .deployment_archive\scripts\
  echo Archivé: test-electron.sh
)

if exist new-test-capacitor.sh (
  move new-test-capacitor.sh .deployment_archive\scripts\
  echo Archivé: new-test-capacitor.sh
)

if exist fix-dependencies.sh (
  move fix-dependencies.sh .deployment_archive\scripts\
  echo Archivé: fix-dependencies.sh
)

if exist start-windows.bat (
  move start-windows.bat .deployment_archive\scripts\
  echo Archivé: start-windows.bat
)

if exist fix-windows-dependencies.bat (
  move fix-windows-dependencies.bat .deployment_archive\scripts\
  echo Archivé: fix-windows-dependencies.bat
)

if exist new-test-electron.sh (
  move new-test-electron.sh .deployment_archive\scripts\
  echo Archivé: new-test-electron.sh
)

if exist new-test-electron.bat (
  move new-test-electron.bat .deployment_archive\scripts\
  echo Archivé: new-test-electron.bat
)

if exist deploy-caprover.sh (
  move deploy-caprover.sh .deployment_archive\scripts\
  echo Archivé: deploy-caprover.sh
)

if exist new-start-local.sh (
  move new-start-local.sh .deployment_archive\scripts\
  echo Archivé: new-start-local.sh
)

if exist new-start-local.bat (
  move new-start-local.bat .deployment_archive\scripts\
  echo Archivé: new-start-local.bat
)

if exist build-capacitor.sh (
  move build-capacitor.sh .deployment_archive\scripts\
  echo Archivé: build-capacitor.sh
)

if exist test-migrations.sh (
  move test-migrations.sh .deployment_archive\scripts\
  echo Archivé: test-migrations.sh
)

if exist test-migrations.bat (
  move test-migrations.bat .deployment_archive\scripts\
  echo Archivé: test-migrations.bat
)

REM Déplacer les configurations
if exist .npmrc.capacitor (
  move .npmrc.capacitor .deployment_archive\configs\
  echo Archivé: .npmrc.capacitor
)

if exist .npmrc.electron (
  move .npmrc.electron .deployment_archive\configs\
  echo Archivé: .npmrc.electron
)

if exist .npmrc.caprover (
  move .npmrc.caprover .deployment_archive\configs\
  echo Archivé: .npmrc.caprover
)

echo ==================================
echo Nettoyage terminé!
echo Les fichiers sont archivés dans .deployment_archive
echo ==================================