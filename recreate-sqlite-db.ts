import { drizzle } from 'drizzle-orm/better-sqlite3';
import Database from 'better-sqlite3';
import * as schema from '@shared/schema-sqlite';
import { scrypt, randomBytes } from 'crypto';
import { promisify } from 'util';

const scryptAsync = promisify(scrypt);

async function hashPassword(password: string) {
  const salt = randomBytes(16).toString('hex');
  const buf = (await scryptAsync(password, salt, 64)) as Buffer;
  return `${buf.toString('hex')}.${salt}`;
}

async function recreateDatabase() {
  const sqlite = new Database('sqlite.db');
  const db = drizzle(sqlite, { schema });
  
  console.log('Dropping all tables...');
  // Drop tables in reverse order of dependencies
  try {
    sqlite.exec('DROP TABLE IF EXISTS activities;');
    sqlite.exec('DROP TABLE IF EXISTS loans;');
    sqlite.exec('DROP TABLE IF EXISTS books;');
    sqlite.exec('DROP TABLE IF EXISTS readers;');
    sqlite.exec('DROP TABLE IF EXISTS locations;');
    sqlite.exec('DROP TABLE IF EXISTS sessions;');
    sqlite.exec('DROP TABLE IF EXISTS users;');
    console.log('All tables dropped successfully');
  } catch (error) {
    console.error('Error dropping tables:', error);
  }
  
  console.log('Creating tables...');
  
  // Create users table
  sqlite.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT NOT NULL UNIQUE,
      password TEXT NOT NULL,
      display_name TEXT NOT NULL,
      role TEXT NOT NULL
    );
  `);
  
  // Create sessions table
  sqlite.exec(`
    CREATE TABLE IF NOT EXISTS sessions (
      sid TEXT PRIMARY KEY,
      sess TEXT NOT NULL,
      expired INTEGER NOT NULL
    );
  `);
  
  // Create locations table
  sqlite.exec(`
    CREATE TABLE IF NOT EXISTS locations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL UNIQUE,
      description TEXT,
      capacity INTEGER NOT NULL DEFAULT 100,
      is_active INTEGER NOT NULL DEFAULT 1
    );
  `);
  
  // Create books table
  sqlite.exec(`
    CREATE TABLE IF NOT EXISTS books (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      author TEXT NOT NULL,
      isbn TEXT NOT NULL UNIQUE,
      cover_url TEXT,
      publish_year INTEGER,
      genre TEXT,
      format TEXT NOT NULL,
      location_id INTEGER,
      language TEXT,
      description TEXT,
      total_copies INTEGER NOT NULL DEFAULT 1,
      available_copies INTEGER NOT NULL DEFAULT 1,
      is_available INTEGER NOT NULL DEFAULT 1,
      FOREIGN KEY (location_id) REFERENCES locations(id)
    );
  `);
  
  // Create readers table
  sqlite.exec(`
    CREATE TABLE IF NOT EXISTS readers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      email TEXT NOT NULL UNIQUE,
      phone TEXT,
      address TEXT,
      member_since TEXT NOT NULL,
      photo TEXT
    );
  `);
  
  // Create loans table
  sqlite.exec(`
    CREATE TABLE IF NOT EXISTS loans (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      book_id INTEGER NOT NULL,
      reader_id INTEGER NOT NULL,
      loan_date TEXT NOT NULL,
      due_date TEXT NOT NULL,
      return_date TEXT,
      status TEXT NOT NULL,
      FOREIGN KEY (book_id) REFERENCES books(id),
      FOREIGN KEY (reader_id) REFERENCES readers(id)
    );
  `);
  
  // Create activities table
  sqlite.exec(`
    CREATE TABLE IF NOT EXISTS activities (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      user_id INTEGER NOT NULL,
      user_display_name TEXT NOT NULL,
      action TEXT NOT NULL,
      details TEXT NOT NULL,
      timestamp TEXT NOT NULL,
      FOREIGN KEY (user_id) REFERENCES users(id)
    );
  `);
  
  console.log('All tables created successfully');
  
  // Insérer un utilisateur administrateur par défaut
  console.log('Creating admin user...');
  try {
    const hashedPassword = await hashPassword('admin123');
    sqlite.exec(`
      INSERT INTO users (username, password, display_name, role)
      VALUES ('admin', '${hashedPassword}', 'Administrator', 'admin');
    `);
    console.log('Admin user created successfully');
  } catch (error) {
    console.error('Error creating admin user:', error);
  }
  
  // Insérer quelques emplacements par défaut
  console.log('Creating default locations...');
  try {
    sqlite.exec(`
      INSERT INTO locations (name, description, capacity)
      VALUES 
        ('Section A', 'Fiction, romans et littérature générale', 150),
        ('Section B', 'Non-fiction, documentaires et biographies', 120),
        ('Section C', 'Jeunesse et bandes dessinées', 100),
        ('Section D', 'Sciences et technologies', 80),
        ('Réserve', 'Stockage des livres moins demandés', 300);
    `);
    console.log('Default locations created successfully');
  } catch (error) {
    console.error('Error creating default locations:', error);
  }
  
  console.log('Database setup completed');
  sqlite.close();
}

recreateDatabase().catch(console.error);