/**
 * <PERSON>ript de démarrage direct du serveur Electron sans ts-node
 * Ce script utilise directement les fichiers JavaScript compilés
 */

console.log('Démarrage du serveur iQraa (SQLite) en mode CommonJS...');

// Définir les variables d'environnement nécessaires
process.env.NODE_ENV = 'production';
process.env.SERVER_TYPE = 'sqlite';

// Importer notre script d'initialisation des alias
try {
  require('./electron-alias-setup.cjs');
} catch (error) {
  console.error('Erreur lors du chargement des alias:', error);
  
  // Fallback: configurer directement les alias ici
  const path = require('path');
  const moduleAlias = require('module-alias');

  // Enregistrer les alias
  moduleAlias.addAliases({
    '@shared': path.join(__dirname, 'server-compiled/shared'),
    '@server': path.join(__dirname, 'server-compiled/server'),
    '@': path.join(__dirname, 'client/src'),
  });
  
  // Ajouter un alias spécifique pour schema-sqlite
  moduleAlias.addAlias('@shared/schema-sqlite', path.join(__dirname, 'server-compiled/shared/schema-sqlite'));
}

try {
  // Importer le fichier principal du serveur (version JS compilée)
  // On utilise require pour les modules CommonJS
  require('./server-compiled/server/index-sqlite.js');
  console.log('Serveur démarré avec succès!');
} catch (error) {
  console.error('Erreur lors du démarrage du serveur:', error);
  console.error('Stack:', error.stack);
}