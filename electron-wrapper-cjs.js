// Version CommonJS de l'application Electron
const { app, BrowserWindow, Menu, ipcMain } = require('electron');
const path = require('path');
const url = require('url');
const fs = require('fs');

// Désactiver les avertissements de sécurité en développement
process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = 'true';

// Garder une référence globale de l'objet window
let mainWindow;

// Charger la configuration Electron
let config = {
  mainWindow: {
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    title: "iQraa - Gestion de bibliotheque",
    icon: "./public/assets/favicon.png",
    backgroundColor: "#ffffff"
  },
  server: {
    port: 5000,
    host: "localhost"
  },
  api: {
    syncEndpoint: "https://api.iqraa-library.com",
    enableSync: true
  },
  database: {
    mode: "offline",
    syncInterval: 30 * 60 * 1000
  },
  settings: {
    checkForUpdates: true,
    language: "fr",
    theme: "light"
  }
};

// Charger la configuration si elle existe
try {
  const configPath = path.join(__dirname, 'dist', 'electron-config.json');
  if (fs.existsSync(configPath)) {
    const configFile = fs.readFileSync(configPath, 'utf8');
    config = JSON.parse(configFile);
    console.log("Configuration chargee depuis:", configPath);
  }
} catch (error) {
  console.error("Erreur lors du chargement de la configuration:", error);
}

// Configurer les variables d'environnement pour le serveur
process.env.NODE_ENV = process.env.NODE_ENV || 'production';
process.env.PG_DISABLED = 'true'; // Toujours utiliser SQLite en mode desktop
process.env.SYNC_ENABLED = config.api.enableSync ? 'true' : 'false';
process.env.SYNC_API_ENDPOINT = config.api.syncEndpoint;
process.env.SYNC_INTERVAL = config.database.syncInterval.toString();

function createWindow() {
  // Créer la fenêtre du navigateur avec la configuration
  mainWindow = new BrowserWindow({
    width: config.mainWindow.width,
    height: config.mainWindow.height,
    minWidth: config.mainWindow.minWidth,
    minHeight: config.mainWindow.minHeight,
    icon: path.join(__dirname, config.mainWindow.icon),
    backgroundColor: config.mainWindow.backgroundColor,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: true, // Garder activé en production
      preload: path.join(__dirname, 'electron', 'preload.js') // Script de preload
    },
    title: config.mainWindow.title
  });

  // Afficher les Outils de développement en mode développement
  if (process.env.NODE_ENV === 'development') {
    mainWindow.webContents.openDevTools();
  }

  // Déterminer l'URL à charger en fonction de l'environnement
  const isDev = process.env.NODE_ENV === 'development';
  const serverUrl = `http://${config.server.host}:${config.server.port}`;
  const appUrl = isDev 
    ? serverUrl  // URL de développement
    : url.format({
        pathname: path.join(__dirname, 'dist/index.html'),
        protocol: 'file:',
        slashes: true
      });

  console.log(`Mode ${isDev ? 'developpement' : 'production'}, chargement de ${appUrl}`);
  
  // Charger l'URL de l'application
  mainWindow.loadURL(appUrl);

  // Configurer le menu en fonction de l'environnement
  const menuTemplate = [
    {
      label: 'iQraa',
      submenu: [
        { role: 'about', label: 'A propos' },
        { type: 'separator' },
        { role: 'services', label: 'Services' },
        { type: 'separator' },
        { role: 'hide', label: 'Masquer' },
        { role: 'hideOthers', label: 'Masquer les autres' },
        { role: 'unhide', label: 'Afficher tout' },
        { type: 'separator' },
        { role: 'quit', label: 'Quitter' }
      ]
    },
    {
      label: 'Edition',
      submenu: [
        { role: 'undo', label: 'Annuler' },
        { role: 'redo', label: 'Refaire' },
        { type: 'separator' },
        { role: 'cut', label: 'Couper' },
        { role: 'copy', label: 'Copier' },
        { role: 'paste', label: 'Coller' },
        { role: 'delete', label: 'Supprimer' },
        { role: 'selectAll', label: 'Selectionner tout' }
      ]
    },
    {
      label: 'Affichage',
      submenu: [
        { role: 'reload', label: 'Actualiser' },
        { role: 'forceReload', label: 'Forcer l\'actualisation' },
        { role: 'toggleDevTools', label: 'Outils de developpement' },
        { type: 'separator' },
        { role: 'resetZoom', label: 'Taille reelle' },
        { role: 'zoomIn', label: 'Zoom avant' },
        { role: 'zoomOut', label: 'Zoom arriere' },
        { type: 'separator' },
        { role: 'togglefullscreen', label: 'Plein ecran' }
      ]
    },
    {
      label: 'Fenetre',
      submenu: [
        { role: 'minimize', label: 'Reduire' },
        { role: 'zoom', label: 'Zoom' },
        { type: 'separator' },
        { role: 'front', label: 'Tout ramener au premier plan' }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(menuTemplate);
  Menu.setApplicationMenu(menu);

  // Ajouter un menu contextuel pour la configuration de synchronisation
  mainWindow.webContents.on('context-menu', (_, params) => {
    // Ce menu n'apparaît que lorsqu'on fait un clic droit sur la page
    const syncMenu = Menu.buildFromTemplate([
      {
        label: 'Configuration de synchronisation',
        click: () => {
          mainWindow.webContents.send('open-sync-settings');
        }
      },
      {
        label: `Mode ${config.database.mode === 'offline' ? 'hors ligne' : 'en ligne'}`,
        enabled: false
      },
      {
        type: 'separator'
      },
      {
        label: 'Synchronisation manuelle',
        click: () => {
          mainWindow.webContents.send('trigger-sync');
        },
        enabled: config.api.enableSync
      }
    ]);
    syncMenu.popup();
  });

  // Écouter les événements IPC du renderer
  ipcMain.on('get-app-config', (event) => {
    event.returnValue = config;
  });

  ipcMain.on('update-app-config', (_, newConfig) => {
    // Mettre à jour la configuration
    Object.assign(config, newConfig);
    
    // Sauvegarder la nouvelle configuration
    try {
      const configPath = path.join(__dirname, 'dist', 'electron-config.json');
      fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
      console.log("Configuration mise a jour et enregistree");
      
      // Mettre à jour les variables d'environnement
      process.env.SYNC_ENABLED = config.api.enableSync ? 'true' : 'false';
      process.env.SYNC_API_ENDPOINT = config.api.syncEndpoint;
      process.env.SYNC_INTERVAL = config.database.syncInterval.toString();
      
      mainWindow.webContents.send('config-updated', config);
    } catch (error) {
      console.error("Erreur lors de l'enregistrement de la configuration:", error);
    }
  });

  // Émettre un événement quand la fenêtre est fermée
  mainWindow.on('closed', function () {
    // Déréférencer l'objet window, sinon on pourrait garder en mémoire un tableau
    // d'objets window, ce qui provoquerait des fuites de mémoire.
    mainWindow = null;
  });

  console.log('Application iQraa demarree avec succes en mode Electron');
  console.log('Mode Base de donnees:', config.database.mode);
  console.log('Synchronisation:', config.api.enableSync ? 'Activee' : 'Desactivee');
  console.log('URL API:', config.api.syncEndpoint);
}

// Cette méthode sera appelée quand Electron aura fini
// de s'initialiser et sera prêt à créer des fenêtres de navigateur.
// Certaines APIs peuvent être utilisées uniquement après cet événement.
app.on('ready', createWindow);

// Quitter quand toutes les fenêtres sont fermées.
app.on('window-all-closed', function () {
  // Sur macOS, il est commun pour une application et leur barre de menu
  // de rester active tant que l'utilisateur ne quitte pas explicitement
  // avec Cmd + Q
  if (process.platform !== 'darwin') app.quit();
});

app.on('activate', function () {
  // Sur macOS, il est commun de re-créer une fenêtre dans l'application quand
  // l'icône du dock est cliquée et qu'il n'y a pas d'autres fenêtres ouvertes.
  if (mainWindow === null) createWindow();
});

// Dans ce fichier, vous pouvez inclure le reste du code spécifique du
// processus principal de votre application.