# iQraa - Guide de Déploiement

Ce document explique les différentes options de déploiement pour l'application iQraa, les problèmes courants et leurs solutions.

## Vue d'ensemble des options de déploiement

iQraa peut être déployé de trois manières différentes:

1. **Mode Local** - Application web locale accessible via un navigateur
2. **Mode Desktop (Electron)** - Application de bureau pour Windows, MacOS, Linux
3. **Mode Serveur (CapRover)** - Application déployée sur un serveur web

## Scripts simplifiés (Solutions aux problèmes)

### 1. Pour Electron (Desktop)

Plusieurs problèmes ont été identifiés avec le déploiement Electron:
- Conflits entre ESM et CommonJS
- Erreurs de syntaxe dans les fichiers JavaScript
- Problèmes de chargement des fichiers HTML

**Solution**: Utilisez l'un des scripts suivants:

- `run-electron.bat` - Exécute Electron avec une configuration minimale
- `ultra-test-electron.bat` - Version ultra-simplifiée qui crée tous les fichiers nécessaires

Ces scripts utilisent des fichiers JavaScript purs en CommonJS et évitent les problèmes de compilation.

### 2. Pour CapRover (Serveur Web)

Plusieurs problèmes ont été identifiés avec le déploiement CapRover:
- Conflit de dépendances entre `drizzle-orm` et `@neondatabase/serverless`
- Fichier compilé `dist/server/index.js` manquant
- Erreurs de syntaxe dans les fichiers générés

**Solution**: Utilisez l'un des scripts suivants:

- `caprover-with-build.bat` - Inclut une version précompilée minimale
- `caprover-final.bat` - Version corrigée qui évite les erreurs de syntaxe

Ces scripts:
1. Incluent directement les fichiers compilés (pas besoin de build)
2. Utilisent un ensemble minimal de dépendances sans conflits
3. Évitent les problèmes de syntaxe dans le code généré

## Instructions détaillées

### Pour tester Electron:

1. Exécutez `ultra-test-electron.bat`
2. Une fenêtre Electron minimaliste devrait s'ouvrir
3. Si cela fonctionne, vous pouvez intégrer le vrai code de l'application

### Pour déployer sur CapRover:

1. Exécutez `caprover-final.bat`
2. Renommez le fichier ZIP généré en `.tar.gz`
3. Téléchargez le fichier sur votre serveur CapRover
4. Suivez les instructions de déploiement standard de CapRover

## Configuration de l'API pour la synchronisation

L'application iQraa peut être configurée pour synchroniser les données avec un serveur distant.

### Options de configuration:

- **URL API**: L'adresse du serveur API pour la synchronisation
- **Intervalle de synchronisation**: Fréquence des synchronisations en minutes
- **Mode de synchronisation**: Automatique ou manuelle

Ces paramètres peuvent être définis dans:
- Le fichier de configuration (`client/src/lib/env.ts`)
- Lors du déploiement via les scripts `configure-api.bat` ou `configure-api.sh`

## Résolution des problèmes courants

### Erreur: "Cannot find module '/app/dist/server/index.js'"
**Solution**: Utilisez `caprover-with-build.bat` qui inclut déjà les fichiers compilés

### Erreur: "SyntaxError: Unexpected token ')'"
**Solution**: Utilisez `ultra-test-electron.bat` qui utilise un code JavaScript compatible

### Erreur: "Failed to load URL: file:///path/to/index.html with error: ERR_FILE_NOT_FOUND"
**Solution**: Utilisez `ultra-test-electron.bat` qui crée automatiquement le fichier HTML nécessaire

### Erreur: "Invalid left-hand side in assignment"
**Solution**: Utilisez `caprover-final.bat` qui contient la syntaxe correcte pour les fonctions flèches