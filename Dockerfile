# Base sur Node.js 20 (Active LTS)
FROM node:20-slim AS base

# Image de développement - Dépendances de développement
FROM base AS deps
WORKDIR /app

# Installer les paquets nécessaires pour la compilation et PostgreSQL
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    python3 \
    gcc \
    g++ \
    make \
    postgresql-client \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Copier les fichiers de configuration du projet
COPY package.json package-lock.json ./.npmrc.caprover ./
COPY ./tsconfig.json ./tsconfig.json
COPY ./drizzle.config.ts ./drizzle.config.ts

# Copier et activer la configuration CapRover
RUN cp .npmrc.caprover .npmrc

# Solution au problème de Rollup: installation manuelle de la dépendance native
RUN npm install -g npm@latest
RUN npm install -g vite drizzle-kit tsx ts-node
RUN npm i @rollup/rollup-linux-x64-gnu --no-save

# Installer uniquement les dépendances nécessaires pour CapRover
# Exclure les dépendances de SQLite et Electron/Capacitor
RUN npm ci --omit=optional

# Image de construction
FROM base AS builder
WORKDIR /app

# Copier les dépendances installées
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# S'assurer que .npmrc.caprover est utilisé également dans l'étape de construction
RUN cp .npmrc.caprover .npmrc

# Créer un fichier .env.production explicite pour PostgreSQL si non existant
RUN if [ ! -f .env.production ]; then \
    echo "NODE_ENV=production" > .env.production && \
    echo "DB_TYPE=postgresql" >> .env.production; \
    fi

# Forcer l'utilisation de PostgreSQL et supprimer les dépendances SQLite
RUN echo "DB_TYPE=postgresql" >> .env.production && \
    sed -i '/better-sqlite3/d' package.json && \
    sed -i '/sqlite3/d' package.json

# Installer Vite globalement pour s'assurer qu'il est disponible lors de la construction
RUN npm install -g vite

# Générer les migrations PostgreSQL si nécessaire
RUN if [ -f ./drizzle.config.ts ]; then npx drizzle-kit generate:pg; fi

# Construire le frontend avec Vite et le backend avec esbuild
# Utiliser spécifiquement le point d'entrée pour PostgreSQL
RUN NODE_ENV=production DB_TYPE=postgresql npx vite build && \
    npx esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist

# Image de production
FROM base AS runner
WORKDIR /app

# Configurer l'environnement de production explicitement pour PostgreSQL
ENV NODE_ENV=production
ENV DB_TYPE=postgresql

# Installer les dépendances PostgreSQL et outils nécessaires
RUN apt-get update && apt-get install -y --no-install-recommends \
    postgresql-client \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Installer seulement les dépendances de production
COPY --from=builder /app/package.json /app/package-lock.json ./.npmrc.caprover ./
RUN cp .npmrc.caprover .npmrc
RUN npm ci --omit=dev --omit=optional
RUN npm install -g tsx ts-node

# Copier les fichiers nécessaires pour l'exécution
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/public ./public
COPY --from=builder /app/.env.production ./.env
COPY --from=builder /app/drizzle ./drizzle
COPY --from=builder /app/shared ./shared
COPY --from=builder /app/tsconfig.json ./tsconfig.json

# Copier les fichiers de migration PostgreSQL
COPY --from=builder /app/server/db.ts ./server/db.ts
COPY --from=builder /app/server/setup.ts ./server/setup.ts
COPY --from=builder /app/server/migrate-pg.ts ./server/migrate-pg.ts

# Copier les scripts SQL personnalisés s'ils existent
COPY --from=builder /app/drizzle/sql ./drizzle/sql

# Exposer le port 80
EXPOSE 80

# Script de démarrage pour exécuter les migrations PostgreSQL complètes
RUN echo '#!/bin/sh\n\
echo "Exécution des migrations PostgreSQL automatiques..."\n\
cd /app && NODE_ENV=production npx tsx ./server/migrate-pg.ts\n\
echo "Démarrage de l'\''application..."\n\
exec node dist/index.js\n' > /app/start.sh && chmod +x /app/start.sh

# Commande par défaut pour démarrer l'application
CMD ["/app/start.sh"]