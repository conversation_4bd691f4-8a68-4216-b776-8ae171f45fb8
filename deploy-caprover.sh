#!/bin/bash
# Script pour préparer et déployer l'application iQraa sur CapRover
# Auteur: iQraa Team
# Usage: ./deploy-caprover.sh [options]
#   Options:
#     --help          Affiche ce message d'aide
#     --no-build      Ignore l'étape de construction
#     --no-db-prep    Ignore la préparation de la base de données
#     --no-tar        Ignore la création du fichier tar
#     --tar-only      Crée seulement le fichier tar sans déployer
#     --env-file=<fichier>  Spécifie un fichier .env personnalisé (par défaut: .env.production)

# Définir les couleurs pour une meilleure lisibilité
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Variables par défaut
BUILD=true
DB_PREP=true
CREATE_TAR=true
DEPLOY=true
ENV_FILE=".env.production"

# Traitement des arguments
for arg in "$@"; do
  case $arg in
    --help)
      echo "Usage: ./deploy-caprover.sh [options]"
      echo "Options:"
      echo "  --help          Affiche ce message d'aide"
      echo "  --no-build      Ignore l'étape de construction"
      echo "  --no-db-prep    Ignore la préparation de la base de données"
      echo "  --no-tar        Ignore la création du fichier tar"
      echo "  --tar-only      Crée seulement le fichier tar sans déployer"
      echo "  --env-file=<fichier>  Spécifie un fichier .env personnalisé (par défaut: .env.production)"
      exit 0
      ;;
    --no-build)
      BUILD=false
      ;;
    --no-db-prep)
      DB_PREP=false
      ;;
    --no-tar)
      CREATE_TAR=false
      ;;
    --tar-only)
      DEPLOY=false
      ;;
    --env-file=*)
      ENV_FILE="${arg#*=}"
      ;;
    *)
      echo -e "${RED}Option inconnue: $arg${NC}"
      echo "Utilisez --help pour afficher les options disponibles"
      exit 1
      ;;
  esac
done

echo -e "${BLUE}========================================================${NC}"
echo -e "${GREEN}🚀 Déploiement de l'application iQraa vers CapRover${NC}"
echo -e "${BLUE}========================================================${NC}"

# Vérifier si le CLI CapRover est installé
if [ "$DEPLOY" = true ] && ! command -v caprover &> /dev/null; then
  echo -e "${YELLOW}⚠️ CLI CapRover non trouvé. Installation...${NC}"
  npm install -g caprover || {
    echo -e "${RED}❌ Impossible d'installer CapRover CLI.${NC}"
    echo -e "Installez-le manuellement avec: npm install -g caprover"
    DEPLOY=false
  }
fi

# Vérifier si le fichier .env.production existe
if [ ! -f "$ENV_FILE" ]; then
  echo -e "${YELLOW}⚠️ Fichier $ENV_FILE non trouvé.${NC}"
  
  # Créer un fichier .env.production à partir de .env.local ou un modèle
  if [ -f ".env.local" ]; then
    echo -e "${YELLOW}📄 Création de $ENV_FILE à partir de .env.local...${NC}"
    cp .env.local "$ENV_FILE"
    # Mettre à jour le mode en production
    sed -i 's/NODE_ENV=.*/NODE_ENV=production/' "$ENV_FILE"
    # Forcer l'utilisation de PostgreSQL (explicitement désactiver SQLite)
    sed -i '/DB_TYPE=/d' "$ENV_FILE"  # Supprimer toute ligne existante
    echo "# Force l'utilisation de PostgreSQL uniquement pour le déploiement CapRover" >> "$ENV_FILE"
    echo "DB_TYPE=postgresql" >> "$ENV_FILE"
    
    # Ajouter la variable DATABASE_URL si elle n'existe pas
    if ! grep -q "DATABASE_URL" "$ENV_FILE"; then
      echo -e "${YELLOW}⚠️ Vous devez configurer DATABASE_URL dans $ENV_FILE${NC}"
      echo "# Configuration PostgreSQL pour CapRover" >> "$ENV_FILE"
      echo "DATABASE_URL=postgres://utilisateur:mot_de_passe@hôte:port/nom_base_données" >> "$ENV_FILE"
    fi
  else
    echo -e "${YELLOW}📄 Création d'un nouveau $ENV_FILE...${NC}"
    cat > "$ENV_FILE" << EOL
# Configuration de production pour iQraa
NODE_ENV=production

# Utilisation explicite de PostgreSQL uniquement pour le déploiement CapRover
DB_TYPE=postgresql

# Clé secrète pour les sessions
SESSION_SECRET=$(cat /dev/urandom | tr -dc 'a-zA-Z0-9' | fold -w 32 | head -n 1)

# Configuration PostgreSQL pour CapRover
DATABASE_URL=postgres://utilisateur:mot_de_passe@hôte:port/nom_base_données

# Configuration du serveur
PORT=80

# Configuration de synchronisation (serveur)
SYNC_ENABLED=false
SYNC_API_KEY=$(cat /dev/urandom | tr -dc 'a-zA-Z0-9' | fold -w 32 | head -n 1)
CLIENT_ID=iqraa-server-$(cat /dev/urandom | tr -dc 'a-zA-Z0-9' | fold -w 8 | head -n 1)

# Désactiver explicitement SQLite pour le déploiement CapRover
SQLITE_DISABLED=true
EOL
  fi
  
  echo -e "${YELLOW}⚠️ Veuillez éditer $ENV_FILE pour configurer les variables d'environnement${NC}"
  read -p "Continuer le déploiement sans modifier $ENV_FILE? (o/n) " -n 1 -r
  echo
  if [[ ! $REPLY =~ ^[Oo]$ ]]; then
    echo -e "${YELLOW}📝 Edition de $ENV_FILE...${NC}"
    ${EDITOR:-vi} "$ENV_FILE"
  fi
fi

# Basculer vers la configuration CapRover
echo -e "${YELLOW}⚙️ Basculement vers la configuration CapRover...${NC}"
if [ -f "switch-deployment.sh" ]; then
  bash switch-deployment.sh caprover
elif [ -f ".npmrc.caprover" ]; then
  echo -e "${YELLOW}📄 Copie manuelle de .npmrc.caprover vers .npmrc...${NC}"
  cp .npmrc.caprover .npmrc
else
  echo -e "${RED}⚠️ Configuration CapRover non trouvée. Utilisation de la configuration actuelle.${NC}"
fi

# Création du dossier .temp pour les logs et fichiers temporaires
if [ ! -d ".temp" ]; then
  mkdir .temp
fi

# Préparation de la base de données
if [ "$DB_PREP" = true ]; then
  echo -e "${YELLOW}🗄️ Préparation des scripts de migration de base de données...${NC}"
  
  # Générer le script de migration si nécessaire
  if command -v npx &> /dev/null && [ -f "drizzle.config.ts" ]; then
    echo -e "${YELLOW}📄 Génération du script de migration DrizzleORM...${NC}"
    npx drizzle-kit generate:pg || {
      echo -e "${RED}⚠️ Erreur de génération des migrations. Continuez quand même.${NC}"
    }
  fi
fi

# Construction
if [ "$BUILD" = true ]; then
  echo -e "${YELLOW}🔨 Construction de l'application...${NC}"
  npm run build || {
    echo -e "${RED}❌ Erreur lors de la construction de l'application.${NC}"
    exit 1
  }
fi

# Création du tarball
if [ "$CREATE_TAR" = true ]; then
  TIMESTAMP=$(date +%Y%m%d%H%M%S)
  TAR_FILE=".temp/iqraa-deploy-$TIMESTAMP.tar.gz"
  
  echo -e "${YELLOW}📦 Création de l'archive de déploiement: $TAR_FILE...${NC}"
  
  # Liste des fichiers/dossiers à exclure
  EXCLUDES=(
    "--exclude=node_modules"
    "--exclude=.git"
    "--exclude=.temp"
    "--exclude=*.log"
    "--exclude=dist-electron"
    "--exclude=android"
    "--exclude=ios"
    "--exclude=.vscode"
    "--exclude=.idea"
  )
  
  # Création du tar avec les exclusions
  tar -czf "$TAR_FILE" ${EXCLUDES[@]} . || {
    echo -e "${RED}❌ Erreur lors de la création de l'archive.${NC}"
    exit 1
  }
  
  echo -e "${GREEN}✅ Archive créée avec succès: $TAR_FILE${NC}"
  
  # Si option tar-only, arrêter ici
  if [ "$DEPLOY" = false ]; then
    echo -e "${GREEN}✅ Archive de déploiement créée avec succès. Déploiement manuel requis.${NC}"
    echo -e "${YELLOW}📋 Instructions:${NC}"
    echo -e "1. Connectez-vous au panneau d'administration CapRover (https://captain.votre-domaine.com)"
    echo -e "2. Allez dans 'Apps' et sélectionnez votre application"
    echo -e "3. Cliquez sur 'Deployment' et sélectionnez 'Upload .tar.gz'"
    echo -e "4. Téléchargez le fichier: $TAR_FILE"
    echo -e "5. Cliquez sur 'Deploy Now'"
    exit 0
  fi
fi

# Déploiement avec CapRover CLI
if [ "$DEPLOY" = true ]; then
  echo -e "${YELLOW}🚀 Déploiement de l'application via CapRover CLI...${NC}"
  
  # Vérifier si l'utilisateur est connecté
  if ! caprover list &> /dev/null; then
    echo -e "${YELLOW}🔑 Connexion à CapRover nécessaire...${NC}"
    caprover login || {
      echo -e "${RED}❌ Erreur de connexion à CapRover.${NC}"
      exit 1
    }
  fi
  
  # Si on a créé un tarball, le déployer
  if [ "$CREATE_TAR" = true ] && [ -f "$TAR_FILE" ]; then
    echo -e "${YELLOW}📤 Téléchargement et déploiement de $TAR_FILE...${NC}"
    caprover deploy -t "$TAR_FILE" || {
      echo -e "${RED}❌ Erreur lors du déploiement.${NC}"
      exit 1
    }
  else
    # Sinon déployer directement
    echo -e "${YELLOW}📤 Déploiement direct de l'application...${NC}"
    caprover deploy || {
      echo -e "${RED}❌ Erreur lors du déploiement.${NC}"
      exit 1
    }
  fi
  
  echo -e "${GREEN}✅ Déploiement terminé avec succès!${NC}"
  echo -e "${YELLOW}📋 Vérifiez le déploiement sur le panneau d'administration CapRover.${NC}"
fi

echo -e "${BLUE}========================================================${NC}"
echo -e "${GREEN}✅ Processus de déploiement CapRover terminé${NC}"
echo -e "${BLUE}========================================================${NC}"