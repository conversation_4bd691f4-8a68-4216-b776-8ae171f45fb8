#!/bin/bash
# Script principal de déploiement pour iQraa

# Couleurs pour la sortie
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction d'affichage du menu
display_menu() {
  echo -e "${YELLOW}==================================${NC}"
  echo -e "${GREEN}Application iQraa - Gestionnaire de déploiement${NC}"
  echo -e "${YELLOW}==================================${NC}"
  echo -e "${BLUE}1.${NC} Démarrer en mode développement local (Linux/macOS)"
  echo -e "${BLUE}2.${NC} Démarrer en mode développement local (Windows)"
  echo -e "${BLUE}3.${NC} Déployer vers CapRover (version en ligne)"
  echo -e "${BLUE}4.${NC} Construire pour Electron (version bureau)"
  echo -e "${BLUE}5.${NC} Tester avec Electron (développement)"
  echo -e "${BLUE}6.${NC} Construire pour Capacitor (version mobile)"
  echo -e "${BLUE}7.${NC} Tester avec Capacitor (développement)"
  echo -e "${BLUE}8.${NC} Nettoyer les anciens fichiers de déploiement"
  echo -e "${BLUE}9.${NC} Afficher la documentation"
  echo -e "${BLUE}10.${NC} Installer les dépendances (résoudre erreurs d'installation)"
  echo -e "${BLUE}11.${NC} Configurer l'URL de l'API de synchronisation"
  echo -e "${BLUE}0.${NC} Quitter"
  echo -e "${YELLOW}==================================${NC}"
  echo -e "Documentation : README-Deployment.md"
  echo -e "${YELLOW}==================================${NC}"
}

# Rendre les scripts exécutables
chmod +x deployment/local/start.sh
chmod +x deployment/caprover/deploy.sh
chmod +x deployment/electron/build.sh
chmod +x deployment/electron/test.sh
chmod +x deployment/capacitor/build.sh
chmod +x deployment/capacitor/test.sh

# Afficher le menu et gérer les choix
while true; do
  display_menu
  read -p "Choisissez une option (0-11): " choice
  
  case $choice in
    1)
      echo -e "${YELLOW}Démarrage en mode développement local (Linux/macOS)...${NC}"
      ./deployment/local/start.sh
      ;;
    2)
      echo -e "${YELLOW}Démarrage en mode développement local (Windows)...${NC}"
      if command -v cmd.exe &> /dev/null; then
        cmd.exe /c deployment\\local\\start.bat
      else
        echo -e "${RED}Erreur: Cette option nécessite Windows.${NC}"
      fi
      ;;
    3)
      echo -e "${YELLOW}Déploiement vers CapRover...${NC}"
      ./deployment/caprover/deploy.sh
      ;;
    4)
      echo -e "${YELLOW}Construction pour Electron...${NC}"
      ./deployment/electron/build.sh
      ;;
    5)
      echo -e "${YELLOW}Test avec Electron...${NC}"
      ./deployment/electron/test.sh
      ;;
    6)
      echo -e "${YELLOW}Construction pour Capacitor...${NC}"
      ./deployment/capacitor/build.sh
      ;;
    7)
      echo -e "${YELLOW}Test avec Capacitor...${NC}"
      ./deployment/capacitor/test.sh
      ;;
    8)
      echo -e "${YELLOW}Nettoyage des anciens fichiers de déploiement...${NC}"
      ./cleanup-deployment.sh
      ;;
    9)
      echo -e "${YELLOW}Affichage de la documentation...${NC}"
      if command -v less &> /dev/null; then
        less README-Deployment.md
      else
        cat README-Deployment.md | more
      fi
      echo -e "${YELLOW}Pour plus de détails, consultez également :${NC}"
      echo -e "- deployment/DOCUMENTATION.md"
      echo -e "- deployment/README.md"
      echo -e "- deployment/caprover/README.md"
      echo -e "- deployment/electron/README.md"
      echo -e "- deployment/capacitor/README.md"
      echo -e "- deployment/local/README.md"
      ;;
    10)
      echo -e "${YELLOW}Installation des dépendances...${NC}"
      ./deployment/local/install-dependencies.sh
      ;;
    11)
      echo -e "${YELLOW}Configuration de l'URL de l'API de synchronisation...${NC}"
      ./deployment/configure-api.sh
      ;;
    0)
      echo -e "${GREEN}Au revoir!${NC}"
      exit 0
      ;;
    *)
      echo -e "${RED}Option invalide. Veuillez choisir une option entre 0 et 11.${NC}"
      ;;
  esac
  
  echo -e "${YELLOW}Appuyez sur Entrée pour continuer...${NC}"
  read
done