omit=dev
# Exclure les dépendances optionnelles (Electron et Capacitor)
optional=false
fund=false
audit=false
# Configuration pour résoudre le problème de Rollup
legacy-peer-deps=true

# Passer en format NPM_CONFIG pour assurer la compatibilité avec npm 10+
npm_config_ignore_workspace_root_check=true
npm_config_electron_skip_binary_download=true
npm_config_capacitor_skip_android_build=true
npm_config_capacitor_skip_ios_build=true

# Désactiver explicitement l'installation des packages optionnels et de développement
production=true