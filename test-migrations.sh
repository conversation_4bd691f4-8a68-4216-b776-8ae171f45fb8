#!/bin/bash
# Script de test des migrations PostgreSQL
# Ce script exécute les migrations sur la base PostgreSQL locale
# pour vérifier qu'elles fonctionnent bien avant le déploiement

echo "=================================="
echo "Test des migrations PostgreSQL"
echo "=================================="

# Vérifier si la variable d'environnement DATABASE_URL est définie
if [ -z "$DATABASE_URL" ]; then
  echo "⚠️ La variable DATABASE_URL n'est pas définie."
  echo "Pour continuer, définissez DATABASE_URL avec votre connexion PostgreSQL."
  exit 1
fi

# Exécuter les migrations via le script
echo "Exécution des migrations..."
NODE_ENV=production DB_TYPE=postgresql npx tsx server/migrate-pg.ts

# Vérifier le code de sortie
if [ $? -eq 0 ]; then
  echo "✅ Test des migrations réussi!"
else
  echo "❌ Erreur lors du test des migrations."
  exit 1
fi