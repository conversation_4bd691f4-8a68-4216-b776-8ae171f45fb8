import { exec } from 'child_process';

// Utiliser curl pour récupérer les données
exec('curl -X GET "http://localhost:5000/api/genres" -H "Accept: application/json" --cookie cookies.txt -v', 
  (error, stdout, stderr) => {
    if (error) {
      console.error(`Erreur lors de l'exécution de curl: ${error}`);
      return;
    }
    console.log(`Sortie standard curl: ${stdout}`);
    console.error(`Sortie d'erreur curl: ${stderr}`);
  }
);