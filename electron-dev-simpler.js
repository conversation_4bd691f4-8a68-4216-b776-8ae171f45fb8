// Version simplifiée du script de développement Electron
const { spawn } = require('child_process');
const path = require('path');

// Obtenir le répertoire actuel
const projectRoot = __dirname;

console.log('🚀 Démarrage d\'Electron en mode développement...');

// Variables d'environnement pour le mode développement
const env = {
  ...process.env,
  NODE_ENV: 'development',
  DB_TYPE: 'sqlite'
};

// Utilisation directe du serveur (pas de tsx)
console.log('📦 Démarrage du serveur Express directement...');
const serverProcess = spawn('node', ['server/index-sqlite.js'], {
  cwd: projectRoot,
  stdio: 'inherit',
  env
});

// Attendre que le serveur soit prêt
setTimeout(() => {
  console.log('🖥️ Démarrage d\'Electron...');
  // Utilisation de l'electron local
  const electronProcess = spawn(path.join(projectRoot, 'node_modules', '.bin', 'electron'), ['electron-wrapper.js'], {
    cwd: projectRoot,
    stdio: 'inherit',
    env
  });

  // Gérer la fermeture propre
  electronProcess.on('close', () => {
    console.log('⛔ Electron fermé, arrêt du serveur...');
    serverProcess.kill();
    process.exit(0);
  });

  // Gérer les erreurs
  electronProcess.on('error', (err) => {
    console.error('⚠️ Erreur lors du démarrage d\'Electron:', err);
    console.log('Tentative de démarrage avec "electron.cmd"...');
    
    // Tentative avec electron.cmd (Windows)
    const electronCmdProcess = spawn(path.join(projectRoot, 'node_modules', '.bin', 'electron.cmd'), ['electron-wrapper.js'], {
      cwd: projectRoot,
      stdio: 'inherit',
      env
    });
    
    electronCmdProcess.on('error', (err2) => {
      console.error('❌ Impossible de démarrer Electron:', err2);
      console.error('Vérifiez que electron est installé correctement.');
      serverProcess.kill();
      process.exit(1);
    });
  });
}, 3000); // Attendre 3 secondes pour que le serveur démarre

// Gérer les signaux pour arrêter proprement
const cleanup = () => {
  console.log('\n🛑 Arrêt des processus...');
  serverProcess.kill();
  process.exit(0);
};

process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);