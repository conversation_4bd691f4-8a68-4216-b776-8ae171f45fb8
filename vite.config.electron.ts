import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import tailwindcss from '@tailwindcss/vite';
import cartographer from '@replit/vite-plugin-cartographer';
import runtimeErrorModal from '@replit/vite-plugin-runtime-error-modal';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  // Chargement des variables d'environnement
  const env = loadEnv(mode, process.cwd(), '');

  return {
    plugins: [
      react(),
      tailwindcss(),
      cartographer(),
      runtimeErrorModal(),
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './client/src'),
        '@shared': path.resolve(__dirname, './shared'),
        '@assets': path.resolve(__dirname, './public'),
      },
    },
    // Configuration spécifique pour Electron
    base: './',
    build: {
      outDir: 'dist',
      emptyOutDir: true,
      target: 'modules',
      sourcemap: true,
      rollupOptions: {
        input: {
          main: path.resolve(__dirname, 'client/index.html'),
        },
        output: {
          manualChunks: {
            react: ['react', 'react-dom'],
            tanstack: ['@tanstack/react-query'],
            components: [
              'react-hook-form',
              'class-variance-authority',
              'clsx',
              'lucide-react',
              'tailwind-merge',
            ],
          },
        },
      },
    },
    // Options pour le développement
    server: {
      port: 5173,
      host: '0.0.0.0',
      strictPort: true,
      hmr: {
        clientPort: 5173,
      },
    },
    // Configuration variables Electron/SQLite
    define: {
      'process.env.IS_ELECTRON': JSON.stringify(true),
      'process.env.DB_TYPE': JSON.stringify('sqlite'),
    },
  };
});