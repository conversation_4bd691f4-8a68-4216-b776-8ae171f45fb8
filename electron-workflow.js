#!/usr/bin/env node
// Script pour créer un workflow Replit pour Electron

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const replitConfigPath = path.join(__dirname, '.replit');

// Créer le workflow si le fichier .replit existe
if (fs.existsSync(replitConfigPath)) {
  try {
    // Lire le fichier actuel
    let configContent = fs.readFileSync(replitConfigPath, 'utf8');
    
    // Vérifier si le workflow "Start Electron" existe déjà
    if (!configContent.includes('[nix.workflow.Start\\ Electron]')) {
      // Ajouter le workflow Electron
      const electronWorkflow = `
[nix.workflow.Start\\ Electron]
name = "Start Electron"
nixPkgs = ["nodejs", "nodemon"]
command = "node scripts/electron-start.js"
restartOn = {
  nixpkgs = true
}
`;
      
      // Ajouter le workflow au fichier .replit
      fs.appendFileSync(replitConfigPath, electronWorkflow);
      console.log('✅ Workflow "Start Electron" ajouté avec succès à .replit');
    } else {
      console.log('ℹ️ Le workflow "Start Electron" existe déjà dans .replit');
    }
    
    // Vérifier si le workflow "Start Capacitor" existe déjà
    if (!configContent.includes('[nix.workflow.Start\\ Capacitor]')) {
      // Ajouter le workflow Capacitor (mobile)
      const capacitorWorkflow = `
[nix.workflow.Start\\ Capacitor]
name = "Start Capacitor (Mobile)"
nixPkgs = ["nodejs", "nodemon"]
command = "node scripts/capacitor-dev.js"
restartOn = {
  nixpkgs = true
}
`;
      
      // Ajouter le workflow au fichier .replit
      fs.appendFileSync(replitConfigPath, capacitorWorkflow);
      console.log('✅ Workflow "Start Capacitor (Mobile)" ajouté avec succès à .replit');
    } else {
      console.log('ℹ️ Le workflow "Start Capacitor (Mobile)" existe déjà dans .replit');
    }
    
    // Vérifier si le workflow "Start Local" existe déjà
    if (!configContent.includes('[nix.workflow.Start\\ Local]')) {
      // Ajouter le workflow Local (SQLite)
      const localWorkflow = `
[nix.workflow.Start\\ Local]
name = "Start Local (SQLite)"
nixPkgs = ["nodejs", "nodemon"]
command = "node scripts/local-dev.js"
restartOn = {
  nixpkgs = true
}
`;
      
      // Ajouter le workflow au fichier .replit
      fs.appendFileSync(replitConfigPath, localWorkflow);
      console.log('✅ Workflow "Start Local (SQLite)" ajouté avec succès à .replit');
    } else {
      console.log('ℹ️ Le workflow "Start Local (SQLite)" existe déjà dans .replit');
    }
    
  } catch (error) {
    console.error('❌ Erreur lors de la modification du fichier .replit:', error.message);
  }
} else {
  console.error('❌ Fichier .replit non trouvé');
}

// Message final
console.log('\n📋 Instructions:');
console.log('Pour démarrer l\'application:');
console.log('1. Mode développement standard (PostgreSQL): Utilisez le workflow "Start application"');
console.log('2. Mode développement local (SQLite): Utilisez le workflow "Start Local (SQLite)"');
console.log('3. Mode développement Electron: Utilisez le workflow "Start Electron"');
console.log('4. Mode développement Capacitor: Utilisez le workflow "Start Capacitor (Mobile)"');