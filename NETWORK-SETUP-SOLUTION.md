# 🌐 IQRAA Manager - Network Setup Solution

## Issue Fixed ✅

The `npm run setup:network` command was failing because the script was using CommonJS syntax (`require`) in an ES module environment. I've fixed this and provided multiple working solutions.

## 🔧 What Was Fixed

1. **ES Module Conversion**: Updated `scripts/setup-network.js` to use ES module syntax (`import` instead of `require`)
2. **NODE_ENV Fix**: Changed from `production` to `development` mode for proper API routing
3. **Alternative Setup Methods**: Created multiple backup setup methods

## 🚀 How to Setup Network Configuration

### Method 1: Fixed NPM Script (Recommended)
```bash
npm run setup:network
```

### Method 2: PowerShell Script (Windows)
```powershell
powershell -ExecutionPolicy Bypass -File setup-network.ps1
```

### Method 3: Simple Node Script
```bash
node setup-network-simple.js
```

### Method 4: Manual Configuration
If all scripts fail, manually update `.env.local` with your IP address:

1. Find your IP address:
   ```cmd
   ipconfig
   ```
   Look for "IPv4 Address" (e.g., *************)

2. Update `.env.local` file:
   ```env
   SERVER_IP=YOUR_IP_HERE
   API_URL=http://YOUR_IP_HERE:5000
   VITE_API_URL=http://YOUR_IP_HERE:5000
   VITE_SERVER_IP=YOUR_IP_HERE
   ```

## 📋 Current Configuration Status

Your `.env.local` is already configured with:
- **Server IP**: ***********
- **Environment**: Development mode
- **Database**: SQLite
- **CORS**: Enabled for network access
- **Ports**: Backend (5000), Frontend (5173)

## 🚀 Starting the Application

### Option 1: All-in-One Script
```bash
start-network-fixed.bat
```

### Option 2: NPM Scripts
```bash
# Start both backend and frontend together
npm run dev:network-full

# Or start separately:
# Terminal 1 - Backend
npm run dev:network

# Terminal 2 - Frontend
npm run dev:network-frontend
```

### Option 3: Simple Scripts
```bash
# Backend
start-simple-server.bat

# Frontend (in another terminal)
start-simple-frontend.bat
```

## 🌐 Access URLs

Once the servers are running:

### From the server machine:
- **Frontend**: http://localhost:5173
- **Backend**: http://localhost:5000

### From other devices on the network:
- **Frontend**: http://***********:5173
- **Backend**: http://***********:5000
- **Health Check**: http://***********:5000/health

## 👤 Login Credentials

- **Admin**: username: `admin`, password: `admin123`
- **Librarian**: username: `librarian`, password: `librarian123`

## 🔍 Verification Steps

1. **Check if backend is running**:
   ```bash
   curl http://***********:5000/health
   ```
   Should return JSON with status "ok"

2. **Check if frontend is accessible**:
   Open browser to http://***********:5173

3. **Test from mobile device**:
   - Connect to same WiFi
   - Open http://***********:5173 in mobile browser

## 🛠️ Troubleshooting

### If setup scripts still fail:
1. Check Node.js version: `node --version` (should be 16+)
2. Check if project has ES modules: Look for `"type": "module"` in package.json
3. Use manual configuration method above

### If server won't start:
1. Check if ports are free: `netstat -an | findstr :5000`
2. Kill existing processes if needed
3. Check firewall settings
4. Verify .env.local has correct IP address

### If can't access from other devices:
1. Verify all devices on same network
2. Check Windows Firewall settings
3. Try accessing health endpoint: http://***********:5000/health
4. Ensure CORS_ORIGIN=* in .env.local

## 📁 Files Created/Modified

- ✅ `scripts/setup-network.js` - Fixed ES module syntax
- ✅ `setup-network.ps1` - PowerShell alternative
- ✅ `setup-network-simple.js` - Simple Node.js alternative
- ✅ `start-network-fixed.bat` - Reliable startup script
- ✅ `package.json` - Fixed dev:network script
- ✅ `.env.local` - Network configuration

## 🎯 Next Steps

1. Run one of the setup methods above to ensure configuration is correct
2. Start the application using `start-network-fixed.bat` or `npm run dev:network-full`
3. Test access from the server machine first
4. Test access from other devices on the network
5. Login with default credentials and test functionality

The network mode should now work properly without the ECONNREFUSED errors!
