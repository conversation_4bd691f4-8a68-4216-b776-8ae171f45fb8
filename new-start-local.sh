#!/bin/bash
# Script pour démarrer l'application iQraa en mode développement local sous Unix/Linux/Mac
# Version améliorée pour une meilleure compatibilité et robustesse

# Définir les couleurs pour une meilleure lisibilité
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================================${NC}"
echo -e "${GREEN}🚀 Démarrage de l'application iQraa en mode développement local${NC}"
echo -e "${BLUE}========================================================${NC}"

# Détecter le système d'exploitation
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" || "$OSTYPE" == "cygwin" ]]; then
  WINDOWS=true
  echo -e "${BLUE}💻 Environnement Windows détecté (Git Bash)${NC}"
else
  WINDOWS=false
  echo -e "${BLUE}💻 Environnement Unix/Linux/Mac détecté${NC}"
fi

# Vérifier si Node.js est installé
if ! command -v node &> /dev/null; then
  echo -e "${RED}❌ Node.js n'est pas installé ou n'est pas dans le PATH.${NC}"
  echo -e "Veuillez installer Node.js depuis https://nodejs.org/"
  exit 1
fi

# Vérifier si les dépendances sont installées
if [ ! -d "node_modules" ]; then
  echo -e "${YELLOW}📦 Installation des dépendances...${NC}"
  npm install || {
    echo -e "${RED}❌ Erreur lors de l'installation des dépendances.${NC}"
    exit 1
  }
fi

# Créer le fichier .env.local s'il n'existe pas
if [ ! -f ".env.local" ]; then
  echo -e "${YELLOW}📄 Création du fichier .env.local...${NC}"
  echo "# Configuration de développement local pour iQraa" > .env.local
  echo "NODE_ENV=development" >> .env.local
  
  echo "# Forcer l'utilisation de SQLite uniquement" >> .env.local
  echo "DB_TYPE=sqlite" >> .env.local
  echo "SQLITE_PATH=sqlite.db" >> .env.local
  echo "PG_DISABLED=true" >> .env.local
  
  # Désactiver explicitement PostgreSQL pour les environnements de dev local
  echo "# PostgreSQL désactivé en mode développement local" >> .env.local
  
  # Générer un identifiant client unique
  echo "CLIENT_ID=iqraa-local-dev-$(cat /dev/urandom | tr -dc 'a-zA-Z0-9' | fold -w 8 | head -n 1)" >> .env.local
  
  # Configuration de la synchronisation (désactivée par défaut en développement)
  echo "# Configuration de synchronisation" >> .env.local
  echo "SYNC_ENABLED=false" >> .env.local
fi

# Arrêter les processus existants sur les ports requis
echo -e "${YELLOW}🔄 Nettoyage des ports...${NC}"
if [ "$WINDOWS" = true ]; then
  # Windows (Git Bash)
  for port in 3000 5000 5173; do
    PID=$(netstat -ano | grep ":$port" | awk '{print $5}' | head -n 1)
    if [ ! -z "$PID" ]; then
      echo -e " - Arrêt du processus sur le port $port (PID: $PID)"
      taskkill //F //PID $PID > /dev/null 2>&1
    fi
  done
else
  # Unix/Linux/Mac
  for port in 3000 5000 5173; do
    if lsof -ti:$port &> /dev/null; then
      echo -e " - Arrêt du processus sur le port $port"
      lsof -ti:$port | xargs kill -9 > /dev/null 2>&1
    fi
  done
fi

# Vérifier si SQLite est disponible
if [ ! -f "sqlite.db" ]; then
  echo -e "${YELLOW}🗃️ Initialisation de la base de données SQLite...${NC}"
  npx tsx server/init-sqlite-db.ts || {
    echo -e "${RED}❌ Erreur lors de l'initialisation de la base de données.${NC}"
    exit 1
  }
fi

# Vérifier les erreurs de compilation TypeScript
echo -e "${YELLOW}🔧 Vérification des erreurs TypeScript...${NC}"
if ! npx tsc --noEmit; then
  echo -e "${RED}⚠️ Des erreurs TypeScript ont été détectées.${NC}"
  read -p "Continuer quand même? (o/n) " -n 1 -r
  echo
  if [[ ! $REPLY =~ ^[Oo]$ ]]; then
    exit 1
  fi
  echo -e "${GREEN}✅ Continuer malgré les erreurs.${NC}"
fi

echo -e "${GREEN}✨ Démarrage des serveurs...${NC}"

# Utiliser local-dev.js si disponible, sinon script shell
if [ -f "scripts/local-dev.js" ]; then
  echo -e "${YELLOW}🔧 Utilisation du script Node.js unifié...${NC}"
  exec npx cross-env NODE_ENV=development tsx scripts/local-dev.js
else
  echo -e "${YELLOW}🔧 Utilisation du mode shell direct...${NC}"
  
  # Définir les ports
  BACKEND_PORT=5000
  FRONTEND_PORT=5173
  
  # Démarrer le backend
  echo -e "${GREEN}🌐 Démarrage du serveur backend sur http://localhost:${BACKEND_PORT}${NC}"
  npx cross-env NODE_ENV=development DB_TYPE=sqlite PG_DISABLED=true SQLITE_PATH=./sqlite.db npx tsx server/index-sqlite.ts &
  BACKEND_PID=$!
  
  # Attendre que le backend se lance
  echo -e "${YELLOW}⏳ Attente du démarrage du backend...${NC}"
  sleep 3
  
  # Démarrer le frontend
  echo -e "${GREEN}🖥️ Démarrage du serveur frontend sur http://localhost:${FRONTEND_PORT}${NC}"
  npx cross-env NODE_ENV=development DB_TYPE=sqlite PG_DISABLED=true npx vite &
  FRONTEND_PID=$!
  
  echo
  echo -e "${GREEN}✅ Serveurs démarrés avec succès${NC}"
  echo -e "${GREEN}👉 Accédez à l'application via: http://localhost:${FRONTEND_PORT}${NC}"
  echo -e "${GREEN}📊 API disponible sur: http://localhost:${BACKEND_PORT}/api${NC}"
  echo
  echo -e "${YELLOW}💾 Mode SQLite uniquement (PostgreSQL désactivé en local)${NC}"
  echo -e "${YELLOW}🔄 Synchronisation disponible mais désactivée par défaut${NC}"
  echo
  echo -e "${RED}⛔ Appuyez sur Ctrl+C pour arrêter les serveurs${NC}"
  echo -e "${BLUE}========================================================${NC}"
  
  # Fonction de nettoyage
  cleanup() {
    echo
    echo -e "${YELLOW}🛑 Arrêt des serveurs...${NC}"
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    echo -e "${GREEN}✅ Serveurs arrêtés${NC}"
    exit 0
  }
  
  # Attraper les signaux pour un arrêt propre
  trap cleanup SIGINT SIGTERM EXIT
  
  # Attendre indéfiniment
  wait
fi