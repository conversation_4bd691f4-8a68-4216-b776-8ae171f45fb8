# 🚀 IQRAA Manager - Configuration Serveur Local

Ce guide vous explique comment configurer et démarrer l'application IQRAA Manager avec un serveur local pour le développement.

## 📋 Prérequis

- **Node.js** (version 16 ou supérieure)
- **NPM** (version 8 ou supérieure)
- **Git** (pour cloner le projet)

## 🛠️ Installation

1. **<PERSON><PERSON><PERSON> le projet** (si ce n'est pas déjà fait)
```bash
git clone <url-du-projet>
cd IqraaManager
```

2. **Installer les dépendances**
```bash
npm install
```

## 🚀 Démarrage Rapide

### Option 1: Scripts automatisés (Recommandé)

**Windows:**
```bash
./start-local-server.bat
```

**Linux/Mac:**
```bash
chmod +x start-local-server.sh
./start-local-server.sh
```

### Option 2: Scripts NPM

**Démarrer backend et frontend ensemble:**
```bash
npm run dev:full
```

**Démarrer séparément:**
```bash
# Terminal 1 - Backend
npm run dev:local

# Terminal 2 - Frontend
npm run dev:frontend
```

### Option 3: Commandes manuelles

**Backend (Terminal 1):**
```bash
npx cross-env NODE_ENV=development DB_TYPE=sqlite PG_DISABLED=true PORT=5000 npx tsx server/index-sqlite.ts
```

**Frontend (Terminal 2):**
```bash
npx cross-env NODE_ENV=development npx vite --config vite-local.config.ts
```

## 🌐 Accès à l'Application

Une fois démarrée, l'application est accessible à :

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:5000/api
- **Documentation API**: http://localhost:5000/api/docs (si disponible)

## 👤 Comptes par Défaut

- **Administrateur**:
  - Identifiant: `admin`
  - Mot de passe: `admin123`

- **Bibliothécaire**:
  - Identifiant: `librarian`
  - Mot de passe: `librarian123`

## 🗄️ Base de Données

L'application utilise **SQLite** en mode local :
- Fichier de base: `sqlite.db`
- Création automatique au premier démarrage
- Données persistantes entre les sessions

## ⚙️ Configuration

### Variables d'Environnement

Le fichier `.env.local` contient la configuration pour le développement local :

```env
NODE_ENV=development
DB_TYPE=sqlite
PG_DISABLED=true
PORT=5000
SYNC_ENABLED=false
```

### Ports Utilisés

- **5000**: Serveur backend (API)
- **5173**: Serveur frontend (Vite)

## 🔧 Dépannage

### Problème: Port déjà utilisé
```bash
# Windows
netstat -ano | findstr :5000
taskkill /PID <PID> /F

# Linux/Mac
lsof -i:5000
kill -9 <PID>
```

### Problème: Base de données corrompue
```bash
# Supprimer et recréer la base
rm sqlite.db
# Redémarrer l'application
```

### Problème: Dépendances manquantes
```bash
# Réinstaller les dépendances
rm -rf node_modules package-lock.json
npm install
```

## 📁 Structure des Fichiers

```
IqraaManager/
├── client/                 # Frontend React
├── server/                 # Backend Express
├── shared/                 # Code partagé
├── sqlite.db              # Base de données SQLite
├── .env.local             # Configuration locale
├── vite-local.config.ts   # Configuration Vite locale
└── start-local-server.*   # Scripts de démarrage
```

## 🔄 Synchronisation

En mode local, la synchronisation avec le serveur distant est **désactivée** par défaut pour éviter les conflits. Pour l'activer :

1. Modifier `.env.local` : `SYNC_ENABLED=true`
2. Configurer `SYNC_API_URL` avec l'URL du serveur distant
3. Redémarrer l'application

## 📝 Développement

### Hot Reload
- Le frontend se recharge automatiquement lors des modifications
- Le backend redémarre automatiquement avec `tsx`

### Logs
- Backend : Logs dans la console du terminal
- Frontend : Logs dans la console du navigateur (F12)

### Tests
```bash
# Lancer les tests (si configurés)
npm test
```

## 🆘 Support

En cas de problème :
1. Vérifier les logs dans les terminaux
2. Vérifier que les ports ne sont pas utilisés
3. Redémarrer l'application
4. Consulter la documentation complète dans `README.md`
