#!/usr/bin/env node

/**
 * Script pour générer une version bundled du serveur avec CommonJS
 * qui fonctionnera avec Electron sans conflit ES/CommonJS
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('Création d\'un bundle serveur compatible CommonJS');
console.log('-------------------------------------------------');

const outputDir = path.join(__dirname, 'dist', 'server-compiled');

// Créer le dossier de sortie s'il n'existe pas
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

// Compiler le serveur avec tsc en mode CommonJS
try {
  console.log('Compilation TypeScript en mode CommonJS...');
  
  // Astuce: on va utiliser tsconfig.compile.json qui est configuré en mode CommonJS
  execSync('npx tsc --project ./tsconfig.compile.json', {
    stdio: 'inherit'
  });
  
  console.log('✓ Compilation réussie!');
  
  // Vérifier si le fichier principal a été compilé
  if (fs.existsSync(path.join(outputDir, 'server', 'index-sqlite.js'))) {
    console.log('✓ Fichier principal index-sqlite.js trouvé');
  } else {
    console.error('❌ Fichier principal index-sqlite.js non généré');
  }
  
  console.log('\nBundle serveur prêt à être utilisé avec Electron');
} catch (error) {
  console.error('❌ Erreur lors de la compilation:', error);
  process.exit(1);
}