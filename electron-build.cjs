const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Script de build Electron pour iQraa');
console.log('------------------------------------');

// Vérifier si le dossier dist existe déjà et le supprimer si c'est le cas
if (fs.existsSync(path.join(__dirname, 'dist'))) {
  console.log('Suppression du dossier dist existant...');
  fs.rmSync(path.join(__dirname, 'dist'), { recursive: true, force: true });
}

// Créer le dossier dist et la structure nécessaire
console.log('Création de la structure des dossiers...');
fs.mkdirSync(path.join(__dirname, 'dist'), { recursive: true });
fs.mkdirSync(path.join(__dirname, 'dist', 'client'), { recursive: true });
fs.mkdirSync(path.join(__dirname, 'dist', 'client', 'src'), { recursive: true });
fs.mkdirSync(path.join(__dirname, 'dist', 'public'), { recursive: true });
fs.mkdirSync(path.join(__dirname, 'dist', 'public', 'assets'), { recursive: true });

// Copier le fichier index.html de base
console.log('Copie des fichiers de base...');
if (fs.existsSync(path.join(__dirname, 'client', 'index.html'))) {
  fs.copyFileSync(
    path.join(__dirname, 'client', 'index.html'),
    path.join(__dirname, 'dist', 'index.html')
  );
  console.log('✓ index.html copié avec succès');
} else {
  console.warn('⚠️ client/index.html introuvable, création d\'un fichier par défaut');
  // Créer un fichier index.html par défaut
  fs.writeFileSync(path.join(__dirname, 'dist', 'index.html'), `
  <!DOCTYPE html>
  <html lang="fr">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iQraa - Gestion de bibliothèque</title>
    <style>
      body {
        font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        max-width: 1200px;
        margin: 0 auto;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
      }
      .loading { text-align: center; padding: 40px 0; }
      .spinner {
        border: 4px solid rgba(0, 0, 0, 0.1);
        width: 36px;
        height: 36px;
        border-radius: 50%;
        border-left-color: #166534;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="loading">
        <div class="spinner"></div>
        <p>Chargement de l'application iQraa...</p>
      </div>
    </div>
    <script>
      window.onload = function() {
        setTimeout(function() {
          window.location.href = 'http://localhost:5000';
        }, 1000);
      };
    </script>
  </body>
  </html>
  `);
}

// Copier les fichiers client principaux
console.log('Compilation et copie du code client...');
try {
  // Copier src/main.tsx directement (requis par Electron)
  if (fs.existsSync(path.join(__dirname, 'client', 'src', 'main.tsx'))) {
    // Créer le dossier dist/src s'il n'existe pas
    fs.mkdirSync(path.join(__dirname, 'dist', 'src'), { recursive: true });
    
    fs.copyFileSync(
      path.join(__dirname, 'client', 'src', 'main.tsx'),
      path.join(__dirname, 'dist', 'src', 'main.tsx')
    );
    console.log('✓ main.tsx copié avec succès');
  }

  // Créer un bundle JavaScript de l'application client
  console.log('Transpilation du code client avec esbuild...');
  execSync(
    'npx esbuild client/src/main.tsx --bundle --format=esm --outfile=dist/main.js',
    { stdio: 'inherit' }
  );
  
  // Ajouter un script de chargement pour le bundle dans index.html
  const indexPath = path.join(__dirname, 'dist', 'index.html');
  let indexContent = fs.readFileSync(indexPath, 'utf8');
  
  // Ajouter le script dans le head si pas déjà présent
  if (!indexContent.includes('<script src="./main.js"')) {
    indexContent = indexContent.replace(
      '</head>',
      '  <script src="./main.js" type="module"></script>\n</head>'
    );
    fs.writeFileSync(indexPath, indexContent);
  }
  
  console.log('✓ Bundle JavaScript client créé avec succès');
} catch (error) {
  console.error('❌ Erreur lors de la compilation du client:', error.message);
  // Ne pas quitter, on continue avec le reste du build
}

// Compiler le serveur
console.log('Compilation du serveur...');
try {
  // Au lieu d'utiliser esbuild, nous allons simplement copier les fichiers et utiliser typescript directement
  console.log('Création d\'un script de démarrage serveur simplifié...');
  
  // Copier le fichier serveur direct
  console.log('Copie du fichier serveur direct...');
  if (fs.existsSync(path.join(__dirname, 'server-direct.cjs'))) {
    fs.copyFileSync(
      path.join(__dirname, 'server-direct.cjs'),
      path.join(__dirname, 'dist', 'server.cjs')
    );
    console.log('✓ Fichier serveur.cjs créé avec succès');
  } else {
    console.error('❌ Fichier server-direct.cjs introuvable, création d\'une version basique');
    
    const serverScript = `
// Ce script démarre le serveur SQLite en mode production
console.log('Démarrage du serveur iQraa (SQLite)...');

// Définir les variables d'environnement nécessaires
process.env.NODE_ENV = 'production';
process.env.SERVER_TYPE = 'sqlite';

// Importer le fichier principal du serveur (version JS compilée)
require('./server-compiled/server/index-sqlite.js');
`;

    fs.writeFileSync(path.join(__dirname, 'dist', 'server.cjs'), serverScript);
  }
  
  // Compiler les fichiers TypeScript en JavaScript
  console.log('Compilation des fichiers TypeScript du serveur...');
  
  try {
    // Créer le dossier server-compiled s'il n'existe pas
    if (!fs.existsSync(path.join(__dirname, 'dist', 'server-compiled'))) {
      fs.mkdirSync(path.join(__dirname, 'dist', 'server-compiled'), { recursive: true });
    }
    
    // Exécuter tsc pour compiler les fichiers TypeScript
    execSync('npx tsc --project ./tsconfig.compile.json', {
      stdio: 'inherit'
    });
    console.log('Compilation TypeScript réussie!');
  } catch (error) {
    console.error('Erreur lors de la compilation TypeScript:', error);
  }
  
  // Nous n'avons plus besoin de copier les fichiers sources du serveur 
  // car nous les compilons directement en JavaScript avec tsc
  console.log('Préparation des ressources supplémentaires...');
  
  // Toutefois, nous devons copier certains fichiers nécessaires
  
  // Copier sqlite.db si elle existe
  if (fs.existsSync(path.join(__dirname, 'sqlite.db'))) {
    fs.copyFileSync(
      path.join(__dirname, 'sqlite.db'), 
      path.join(__dirname, 'dist', 'sqlite.db')
    );
    console.log('Fichier de base de données SQLite copié');
  } else {
    console.log('Aucun fichier SQLite trouvé à copier');
  }
  
  // Copier le fichier d'initialisation des alias
  if (fs.existsSync(path.join(__dirname, 'electron-alias-setup.cjs'))) {
    fs.copyFileSync(
      path.join(__dirname, 'electron-alias-setup.cjs'),
      path.join(__dirname, 'dist', 'electron-alias-setup.cjs')
    );
    console.log('✓ Fichier d\'initialisation des alias copié');
  } else {
    console.error('❌ electron-alias-setup.cjs introuvable');
  }
  
  // Le dossier shared sera déjà dans dist/server-compiled après la compilation TypeScript
  // Il n'est donc plus nécessaire de le copier ici
  console.log('Le dossier shared sera disponible dans server-compiled après compilation TypeScript');
  
  // Copier d'autres fichiers de configuration si nécessaire
  if (fs.existsSync(path.join(__dirname, '.env'))) {
    fs.copyFileSync(
      path.join(__dirname, '.env'), 
      path.join(__dirname, 'dist', '.env')
    );
    console.log('Fichier .env copié');
  }
  
  // Copier electron-package.json vers package.json dans le dossier dist
  console.log('Copie du fichier package.json pour Electron...');
  if (fs.existsSync(path.join(__dirname, 'electron-package.json'))) {
    fs.copyFileSync(
      path.join(__dirname, 'electron-package.json'), 
      path.join(__dirname, 'dist', 'package.json')
    );
    console.log('✓ package.json pour Electron copié avec succès');
  } else {
    console.error('❌ electron-package.json introuvable');
  }
  
  // Copier tsconfig.json
  if (fs.existsSync(path.join(__dirname, 'tsconfig.json'))) {
    fs.copyFileSync(
      path.join(__dirname, 'tsconfig.json'),
      path.join(__dirname, 'dist', 'tsconfig.json')
    );
  }
  
  console.log('✓ Les fichiers du serveur ont été copiés avec succès');
  console.log('✓ Installation des dépendances nécessaires pour le serveur...');
  
  // Vérifier si ts-node est installé
  try {
    execSync('npm list ts-node || npm install --no-save ts-node typescript @types/node', { 
      stdio: 'inherit' 
    });
  } catch (err) {
    // Ignorer les erreurs potentielles de npm list
  }
  
  console.log('✓ Le serveur a été préparé avec succès');
} catch (error) {
  console.error('❌ Erreur lors de la préparation du serveur:', error.message);
  // Ne pas quitter, essayer de continuer
}

// Créer un fichier Electron supplémentaire avec redirection
console.log('Création du fichier de redirection pour Electron...');
fs.writeFileSync(path.join(__dirname, 'dist', 'electron-loader.html'), `
<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>iQraa - Gestion de bibliothèque</title>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
      color: #333;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      padding: 20px;
    }
    h1 {
      color: #166534;
      margin-top: 0;
    }
    .logo {
      max-width: 150px;
      margin-bottom: 20px;
    }
    .loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 0;
    }
    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      width: 36px;
      height: 36px;
      border-radius: 50%;
      border-left-color: #166534;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <img src="./public/assets/iqraa.png" alt="iQraa Logo" class="logo">
    <h1>iQraa - Gestion de bibliothèque</h1>
    <div class="loading">
      <div class="spinner"></div>
      <p>Chargement de l'application...</p>
      <p><small>L'interface complète est en cours de chargement, veuillez patienter.</small></p>
    </div>
  </div>
  <script>
    // Redirection vers l'application après quelques secondes
    setTimeout(() => {
      window.location.href = 'http://localhost:5000';
    }, 2000);
  </script>
</body>
</html>
`);

// Copier les assets (iqraa.png et favicon)
console.log('Copie des assets...');
try {
  // Copier les assets depuis attached_assets si disponibles
  if (fs.existsSync(path.join(__dirname, 'attached_assets', 'iqraa.png'))) {
    fs.copyFileSync(
      path.join(__dirname, 'attached_assets', 'iqraa.png'),
      path.join(__dirname, 'dist', 'public', 'assets', 'iqraa.png')
    );
  }
  
  if (fs.existsSync(path.join(__dirname, 'attached_assets', 'favicon.png'))) {
    fs.copyFileSync(
      path.join(__dirname, 'attached_assets', 'favicon.png'),
      path.join(__dirname, 'dist', 'public', 'assets', 'favicon.png')
    );
  }
  
  // Copier les assets depuis public si disponible
  if (fs.existsSync(path.join(__dirname, 'public'))) {
    console.log('Copie du dossier public...');
    fs.cpSync(
      path.join(__dirname, 'public'),
      path.join(__dirname, 'dist', 'public'),
      { recursive: true }
    );
  }
  
  console.log('✓ Assets copiés avec succès');
} catch (error) {
  console.error('❌ Erreur lors de la copie des assets:', error.message);
  // Ne pas quitter, ce n'est pas critique
}

// Modifier electron-wrapper.js pour utiliser le fichier electron-loader.html
console.log('Modification du wrapper Electron...');
try {
  // Créer une version modifiée du electron-wrapper.js dans dist
  if (fs.existsSync(path.join(__dirname, 'electron-wrapper.js'))) {
    let wrapperContent = fs.readFileSync(path.join(__dirname, 'electron-wrapper.js'), 'utf8');
    
    // Modifier le chemin de chargement pour notre fichier electron-loader.html
    if (wrapperContent.includes("path.join(__dirname, 'dist/index.html')")) {
      wrapperContent = wrapperContent.replace(
        "path.join(__dirname, 'dist/index.html')",
        "path.join(__dirname, 'dist/electron-loader.html')"
      );
    }
    
    // Copier le fichier modifié dans dist
    fs.writeFileSync(path.join(__dirname, 'dist', 'electron-wrapper.js'), wrapperContent);
    console.log('✓ electron-wrapper.js modifié et copié dans dist');
    
    // Créer une version CommonJS compatible du wrapper pour les systèmes qui ne supportent pas ES modules
    let commonjsWrapper = `
const { app, BrowserWindow, Menu } = require('electron');
const path = require('path');
const url = require('url');
const { spawn } = require('child_process');
const fs = require('fs');

let mainWindow;
let serverProcess;

// Lire la configuration si disponible 
const configPath = path.join(__dirname, 'electron-config.json');
const defaultConfig = {
  mainWindow: {
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    title: "iQraa - Gestion de bibliothèque",
    icon: "./public/assets/favicon.png",
    backgroundColor: "#ffffff"
  },
  server: {
    port: 5000,
    host: "localhost"
  }
};

let config = defaultConfig;
if (fs.existsSync(configPath)) {
  try {
    const configData = fs.readFileSync(configPath, 'utf-8');
    config = JSON.parse(configData);
    console.log("Configuration chargée depuis:", configPath);
  } catch (error) {
    console.error("Erreur lors de la lecture de la configuration:", error);
  }
}

function startServer() {
  console.log("Démarrage du serveur backend...");
  
  // Options d'environnement pour le serveur
  const env = Object.assign({}, process.env, {
    NODE_ENV: 'production',
    PORT: config.server.port,
    HOST: config.server.host,
    SERVER_TYPE: 'sqlite'
  });

  // Chemin vers le script du serveur
  const serverPath = path.join(__dirname, 'server.cjs');
  
  if (!fs.existsSync(serverPath)) {
    console.error(\`ERREUR: Le script du serveur n'existe pas: \${serverPath}\`);
    return false;
  }

  // Lancer le serveur en tant que processus enfant
  serverProcess = spawn('node', [serverPath], { 
    env,
    stdio: 'inherit' // Redirige stdout/stderr vers la console principale
  });

  // Gestion des erreurs du serveur
  serverProcess.on('error', (err) => {
    console.error('Erreur du serveur:', err);
  });

  serverProcess.on('exit', (code) => {
    console.log(\`Le serveur s'est arrêté avec le code: \${code}\`);
    if (code !== 0 && mainWindow) {
      mainWindow.webContents.executeJavaScript(\`
        alert("Le serveur backend s'est arrêté de manière inattendue. Veuillez redémarrer l'application.");
      \`);
    }
  });

  console.log("Processus du serveur démarré avec PID:", serverProcess.pid);
  return true;
}

function createWindow() {
  // Créer la fenêtre du navigateur.
  mainWindow = new BrowserWindow({
    width: config.mainWindow.width,
    height: config.mainWindow.height,
    minWidth: config.mainWindow.minWidth,
    minHeight: config.mainWindow.minHeight,
    icon: config.mainWindow.icon,
    backgroundColor: config.mainWindow.backgroundColor,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: true,
    },
    title: config.mainWindow.title
  });

  // Charger l'application
  const loaderPath = path.join(__dirname, 'electron-loader.html');
  if (fs.existsSync(loaderPath)) {
    console.log("Chargement de la page intermédiaire:", loaderPath);
    mainWindow.loadFile(loaderPath);
  }

  // Attendre que le serveur soit prêt puis rediriger
  console.log("Attente du démarrage du serveur...");
  setTimeout(() => {
    const serverUrl = \`http://\${config.server.host}:\${config.server.port}\`;
    console.log("Redirection vers:", serverUrl);
    mainWindow.loadURL(serverUrl);
  }, 2000);

  // Émettre un événement quand la fenêtre est fermée
  mainWindow.on('closed', function () {
    mainWindow = null;
    
    // Arrêter le serveur lors de la fermeture de la fenêtre
    if (serverProcess) {
      console.log("Arrêt du serveur backend...");
      
      if (process.platform === 'win32') {
        // Sous Windows, il faut utiliser taskkill pour terminer le processus et ses enfants
        spawn('taskkill', ['/pid', serverProcess.pid, '/f', '/t']);
      } else {
        // Sous Unix, on peut utiliser kill directement
        serverProcess.kill();
      }
    }
  });

  console.log('Application iQraa démarrée avec succès en mode Electron');
}

// Cette méthode sera appelée quand Electron aura fini de s'initialiser
app.on('ready', () => {
  console.log("Application prête, démarrage du serveur...");
  if (startServer()) {
    // Attendre un peu pour que le serveur démarre avant de créer la fenêtre
    setTimeout(createWindow, 1000);
  } else {
    console.error("ERREUR: Impossible de démarrer le serveur backend");
    app.quit();
  }
});

// Quitter quand toutes les fenêtres sont fermées.
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});

app.on('activate', function () {
  if (mainWindow === null) createWindow();
});
`;
    
    fs.writeFileSync(path.join(__dirname, 'dist', 'electron-wrapper.cjs'), commonjsWrapper);
    console.log('✓ Version CommonJS du wrapper créée: electron-wrapper.cjs');
  }
} catch (error) {
  console.error('❌ Erreur lors de la modification du wrapper Electron:', error.message);
}

// Créer un fichier manifest.json pour Electron
console.log('Création du fichier de configuration Electron...');
fs.writeFileSync(path.join(__dirname, 'dist', 'electron-config.json'), JSON.stringify({
  mainWindow: {
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    title: "iQraa - Gestion de bibliothèque",
    icon: "./public/assets/favicon.png",
    backgroundColor: "#ffffff"
  },
  server: {
    port: 5000,
    host: "localhost"
  }
}, null, 2));

console.log('✓ Build Electron terminé avec succès!');
console.log('------------------------------------');
console.log('Vous pouvez maintenant lancer l\'application avec:');
console.log('NODE_ENV=production node dist/server.js');
console.log('NODE_ENV=production npx electron electron-wrapper.js');