/**
 * Script d'initialisation des alias de modules pour Electron
 * Ce script doit être exécuté avant tout autre code dans le processus principal
 */

// Configurer module-alias pour résoudre les chemins
const path = require('path');
const moduleAlias = require('module-alias');

console.log('Configuration des alias de modules pour Electron...');

// Définir le chemin de base du projet
const baseDir = __dirname;

// Enregistrer les alias de modules
moduleAlias.addAliases({
  '@': path.join(baseDir, 'client/src'),
  '@shared': path.join(baseDir, 'server-compiled/shared'),
  '@server': path.join(baseDir, 'server-compiled/server'),
  '@assets': path.join(baseDir, 'public/assets')
});

// Ajouter un alias spécifique pour schema-sqlite qui semble poser problème
moduleAlias.addAlias('@shared/schema-sqlite', path.join(baseDir, 'server-compiled/shared/schema-sqlite'));

console.log('Alias de modules configurés avec succès');