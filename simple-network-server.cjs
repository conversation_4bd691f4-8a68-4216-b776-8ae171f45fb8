const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();

// Get local IP address
function getLocalIP() {
  const { networkInterfaces } = require('os');
  const nets = networkInterfaces();
  
  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
      if (net.family === 'IPv4' && !net.internal) {
        return net.address;
      }
    }
  }
  return '***********'; // fallback
}

const LOCAL_IP = getLocalIP();
console.log(`🌐 Detected local IP: ${LOCAL_IP}`);

// CORS setup for network access
app.use(cors({
  origin: '*',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
}));

app.use(express.json());

// Serve static files from dist/public
app.use(express.static(path.join(__dirname, 'dist/public')));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: 'network',
    database: 'sqlite',
    version: '1.0.0',
    server_ip: LOCAL_IP
  });
});

// Basic API endpoints for demo
app.get('/api/status', (req, res) => {
  res.json({
    message: 'IqraaManager Network Server is running!',
    timestamp: new Date().toISOString(),
    server_ip: LOCAL_IP,
    frontend_url: `http://${LOCAL_IP}:5173`,
    backend_url: `http://${LOCAL_IP}:5000`
  });
});

// Mock authentication endpoint
app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body;
  
  // Simple mock authentication
  if ((username === 'admin' && password === 'admin123') || 
      (username === 'librarian' && password === 'librarian123')) {
    res.json({
      success: true,
      user: {
        id: username === 'admin' ? 1 : 2,
        username: username,
        role: username === 'admin' ? 'admin' : 'librarian'
      },
      token: 'mock-jwt-token'
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }
});

// Mock books endpoint
app.get('/api/books', (req, res) => {
  res.json({
    books: [
      {
        id: 1,
        title: 'Sample Book 1',
        author: 'Author 1',
        isbn: '978-**********',
        status: 'available'
      },
      {
        id: 2,
        title: 'Sample Book 2', 
        author: 'Author 2',
        isbn: '978-**********',
        status: 'borrowed'
      }
    ],
    total: 2
  });
});

// Catch all handler for SPA routing
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist/public/index.html'));
});

// Start server
const PORT = 5000;

app.listen(PORT, '0.0.0.0', () => {
  console.log('');
  console.log('🚀 IqraaManager Network Server Started!');
  console.log('========================================');
  console.log(`🌐 Server running on: http://${LOCAL_IP}:${PORT}`);
  console.log(`📊 Health check: http://${LOCAL_IP}:${PORT}/health`);
  console.log(`🔗 API status: http://${LOCAL_IP}:${PORT}/api/status`);
  console.log('');
  console.log('📱 Access from other devices:');
  console.log(`   Connect to same WiFi and open: http://${LOCAL_IP}:${PORT}`);
  console.log('');
  console.log('👤 Default login credentials:');
  console.log('   Admin: admin / admin123');
  console.log('   Librarian: librarian / librarian123');
  console.log('');
  console.log('⛔ Press Ctrl+C to stop the server');
  console.log('========================================');
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  process.exit(0);
});
