const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();

// Simple in-memory session storage
let authenticatedSessions = new Set();
let currentUser = null;

// In-memory storage for rental requests and active rentals
let rentalRequests = [];
let activeRentals = [];
let requestIdCounter = 1;
let rentalIdCounter = 1;

// Define user permissions based on role
function getUserPermissions(username) {
  switch(username) {
    case 'admin':
      return {
        canManageBooks: true,
        canManageUsers: true,
        canApproveRentals: true,
        canViewDashboard: true,
        canSearchBooks: true,
        canRequestRentals: true,
        canViewAllRentals: true
      };
    case 'librarian':
      return {
        canManageBooks: true,
        canManageUsers: false,
        canApproveRentals: true,
        canViewDashboard: true,
        canSearchBooks: true,
        canRequestRentals: true,
        canViewAllRentals: true
      };
    case 'user':
    default:
      return {
        canManageBooks: false,
        canManageUsers: false,
        canApproveRentals: false,
        canViewDashboard: false,
        canSearchBooks: true,
        canRequestRentals: true,
        canViewAllRentals: false
      };
  }
}

// Get local IP address
function getLocalIP() {
  const { networkInterfaces } = require('os');
  const nets = networkInterfaces();
  
  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
      if (net.family === 'IPv4' && !net.internal) {
        return net.address;
      }
    }
  }
  return '***********'; // fallback
}

const LOCAL_IP = getLocalIP();
console.log(`🌐 Detected local IP: ${LOCAL_IP}`);

// CORS setup for network access
app.use(cors({
  origin: '*',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
}));

app.use(express.json());

// Note: Static files will be served after custom routes

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: 'network',
    database: 'sqlite',
    version: '1.0.0',
    server_ip: LOCAL_IP
  });
});

// Basic API endpoints for demo
app.get('/api/status', (req, res) => {
  res.json({
    message: 'IqraaManager Network Server is running!',
    timestamp: new Date().toISOString(),
    server_ip: LOCAL_IP,
    frontend_url: `http://${LOCAL_IP}:5173`,
    backend_url: `http://${LOCAL_IP}:5000`
  });
});

// Add logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  if (req.body && Object.keys(req.body).length > 0) {
    console.log('Request body:', req.body);
  }
  next();
});

// Mock authentication endpoints
app.post('/api/auth/login', (req, res) => {
  console.log('Login attempt:', req.body);
  const { username, password } = req.body;

  // Simple mock authentication with three user types
  if ((username === 'admin' && password === 'admin123') ||
      (username === 'librarian' && password === 'librarian123') ||
      (username === 'user' && password === 'user123')) {
    const user = {
      id: username === 'admin' ? 1 : (username === 'librarian' ? 2 : 3),
      username: username,
      role: username === 'admin' ? 'admin' : (username === 'librarian' ? 'librarian' : 'user'),
      email: `${username}@iqraa.local`,
      name: username === 'admin' ? 'Administrator' : (username === 'librarian' ? 'Librarian' : 'Library User'),
      permissions: getUserPermissions(username)
    };

    // Set current user session
    currentUser = user;
    const sessionToken = 'session-' + Date.now();
    authenticatedSessions.add(sessionToken);

    console.log('Login successful for:', username);
    res.json({
      success: true,
      user: user,
      token: sessionToken
    });
  } else {
    console.log('Login failed for:', username);
    res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }
});

// Alternative login endpoint (some apps use different paths)
app.post('/api/login', (req, res) => {
  console.log('Alternative login attempt:', req.body);
  const { username, password } = req.body;

  // Simple mock authentication with three user types
  if ((username === 'admin' && password === 'admin123') ||
      (username === 'librarian' && password === 'librarian123') ||
      (username === 'user' && password === 'user123')) {
    const user = {
      id: username === 'admin' ? 1 : (username === 'librarian' ? 2 : 3),
      username: username,
      role: username === 'admin' ? 'admin' : (username === 'librarian' ? 'librarian' : 'user'),
      email: `${username}@iqraa.local`,
      name: username === 'admin' ? 'Administrator' : (username === 'librarian' ? 'Librarian' : 'Library User'),
      permissions: getUserPermissions(username)
    };

    // Set current user session
    currentUser = user;
    const sessionToken = 'session-' + Date.now();
    authenticatedSessions.add(sessionToken);

    console.log('Login successful for:', username);
    res.json({
      success: true,
      user: user,
      token: sessionToken
    });
  } else {
    console.log('Login failed for:', username);
    res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }
});

// Session check endpoint
app.get('/api/auth/me', (req, res) => {
  // Mock authenticated user
  res.json({
    success: true,
    user: {
      id: 1,
      username: 'admin',
      role: 'admin',
      email: '<EMAIL>',
      name: 'Administrator'
    }
  });
});

// User endpoint (what the frontend is actually calling)
app.get('/api/user', (req, res) => {
  console.log('User info request - Current user:', currentUser ? currentUser.username : 'none');

  if (currentUser) {
    // Return current authenticated user
    res.json({
      id: currentUser.id,
      username: currentUser.username,
      role: currentUser.role,
      email: currentUser.email,
      name: currentUser.name,
      isAuthenticated: true
    });
  } else {
    // No authenticated user - return 401 or empty response
    res.status(401).json({
      error: 'Not authenticated',
      isAuthenticated: false
    });
  }
});

// Check authentication status
app.get('/api/auth/status', (req, res) => {
  if (currentUser) {
    res.json({
      success: true,
      authenticated: true,
      user: currentUser
    });
  } else {
    res.json({
      success: true,
      authenticated: false,
      user: null
    });
  }
});

// Logout endpoint
app.post('/api/auth/logout', (req, res) => {
  console.log('Logout request');
  // Clear session
  currentUser = null;
  authenticatedSessions.clear();

  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

// Alternative logout endpoint
app.post('/api/logout', (req, res) => {
  console.log('Alternative logout request');
  // Clear session
  currentUser = null;
  authenticatedSessions.clear();

  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

// Mock books endpoints with search functionality
app.get('/api/books', (req, res) => {
  const { search, category, status } = req.query;

  let books = [
    {
      id: 1,
      title: 'Introduction to Library Science',
      author: 'Dr. Ahmed Hassan',
      isbn: '978-0123456789',
      status: 'available',
      category: 'Education',
      location: 'A-001',
      publishedYear: 2020,
      description: 'A comprehensive guide to modern library science principles and practices.'
    },
    {
      id: 2,
      title: 'Digital Library Management',
      author: 'Prof. Sarah Johnson',
      isbn: '978-0987654321',
      status: 'borrowed',
      category: 'Technology',
      location: 'B-002',
      publishedYear: 2021,
      borrowedBy: 'John Doe',
      dueDate: '2025-07-15',
      description: 'Modern approaches to managing digital library systems and resources.'
    },
    {
      id: 3,
      title: 'Islamic History and Culture',
      author: 'Dr. Omar Al-Rashid',
      isbn: '978-0456789123',
      status: 'available',
      category: 'History',
      location: 'C-003',
      publishedYear: 2019,
      description: 'An in-depth exploration of Islamic civilization and cultural heritage.'
    },
    {
      id: 4,
      title: 'Programming Fundamentals',
      author: 'Dr. Michael Chen',
      isbn: '978-0789123456',
      status: 'available',
      category: 'Technology',
      location: 'B-004',
      publishedYear: 2022,
      description: 'Learn the basics of programming with practical examples and exercises.'
    },
    {
      id: 5,
      title: 'World Literature Anthology',
      author: 'Prof. Elena Rodriguez',
      isbn: '978-0321654987',
      status: 'available',
      category: 'Literature',
      location: 'D-005',
      publishedYear: 2020,
      description: 'A collection of classic and contemporary works from around the world.'
    }
  ];

  // Apply search filters
  if (search) {
    const searchLower = search.toLowerCase();
    books = books.filter(book =>
      book.title.toLowerCase().includes(searchLower) ||
      book.author.toLowerCase().includes(searchLower) ||
      book.isbn.includes(search) ||
      book.category.toLowerCase().includes(searchLower)
    );
  }

  if (category) {
    books = books.filter(book => book.category.toLowerCase() === category.toLowerCase());
  }

  if (status) {
    books = books.filter(book => book.status === status);
  }

  res.json({
    success: true,
    data: books,
    total: books.length,
    page: 1,
    limit: 50,
    filters: { search, category, status }
  });
});

// Add book endpoint
app.post('/api/books', (req, res) => {
  console.log('Adding book:', req.body);
  res.json({
    success: true,
    message: 'Book added successfully',
    data: {
      id: Date.now(),
      ...req.body,
      status: 'available',
      createdAt: new Date().toISOString()
    }
  });
});

// Update book endpoint
app.put('/api/books/:id', (req, res) => {
  console.log(`Updating book ${req.params.id}:`, req.body);
  res.json({
    success: true,
    message: 'Book updated successfully',
    data: {
      id: parseInt(req.params.id),
      ...req.body,
      updatedAt: new Date().toISOString()
    }
  });
});

// Delete book endpoint
app.delete('/api/books/:id', (req, res) => {
  console.log(`Deleting book ${req.params.id}`);
  res.json({
    success: true,
    message: 'Book deleted successfully'
  });
});

// Rental request endpoints
app.post('/api/rental-requests', (req, res) => {
  console.log('Rental request:', req.body);
  const { bookId, notes } = req.body;

  if (!currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  // Check if user has permission to request rentals
  if (!currentUser.permissions.canRequestRentals) {
    return res.status(403).json({
      success: false,
      message: 'You do not have permission to request book rentals'
    });
  }

  const request = {
    id: requestIdCounter++,
    bookId: parseInt(bookId),
    userId: currentUser.id,
    username: currentUser.username,
    userEmail: currentUser.email,
    status: 'pending',
    notes: notes || '',
    requestDate: new Date().toISOString(),
    approvedBy: null,
    approvedDate: null
  };

  rentalRequests.push(request);

  res.json({
    success: true,
    message: 'Rental request submitted successfully',
    data: request
  });
});

// Get rental requests (filtered by user role)
app.get('/api/rental-requests', (req, res) => {
  if (!currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  let filteredRequests = rentalRequests;

  // Regular users can only see their own requests
  if (currentUser.role === 'user') {
    filteredRequests = rentalRequests.filter(req => req.userId === currentUser.id);
  }
  // Admins and librarians can see all requests

  res.json({
    success: true,
    data: filteredRequests,
    total: filteredRequests.length
  });
});

// Approve/reject rental request (admin/librarian only)
app.patch('/api/rental-requests/:id', (req, res) => {
  const requestId = parseInt(req.params.id);
  const { status, notes } = req.body;

  if (!currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  if (!currentUser.permissions.canApproveRentals) {
    return res.status(403).json({
      success: false,
      message: 'You do not have permission to approve rental requests'
    });
  }

  const requestIndex = rentalRequests.findIndex(req => req.id === requestId);
  if (requestIndex === -1) {
    return res.status(404).json({
      success: false,
      message: 'Rental request not found'
    });
  }

  const request = rentalRequests[requestIndex];

  // Update request status
  request.status = status;
  request.approvedBy = currentUser.username;
  request.approvedDate = new Date().toISOString();
  if (notes) {
    request.adminNotes = notes;
  }

  // If approved, create an active rental
  if (status === 'approved') {
    const dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + 14); // 2 weeks rental period

    const rental = {
      id: rentalIdCounter++,
      bookId: request.bookId,
      userId: request.userId,
      username: request.username,
      userEmail: request.userEmail,
      requestId: request.id,
      startDate: new Date().toISOString(),
      dueDate: dueDate.toISOString(),
      status: 'active',
      approvedBy: currentUser.username,
      returnDate: null,
      notes: request.notes,
      adminNotes: request.adminNotes || ''
    };

    activeRentals.push(rental);
    console.log(`Created active rental ${rental.id} for user ${request.username}`);
  }

  console.log(`Rental request ${requestId} ${status} by ${currentUser.username}`);

  res.json({
    success: true,
    message: `Rental request ${status} successfully`,
    data: request
  });
});

// Get user's active rentals
app.get('/api/rentals', (req, res) => {
  if (!currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  let userRentals = activeRentals;

  // Regular users can only see their own rentals
  if (currentUser.role === 'user') {
    userRentals = activeRentals.filter(rental => rental.userId === currentUser.id);
  }
  // Admins and librarians can see all rentals

  // Enhance rentals with book information
  const rentalsWithBooks = userRentals.map(rental => {
    // Find book info (mock data for now)
    const books = [
      { id: 1, title: 'Introduction to Library Science', author: 'Dr. Ahmed Hassan' },
      { id: 2, title: 'Digital Library Management', author: 'Prof. Sarah Johnson' },
      { id: 3, title: 'Islamic History and Culture', author: 'Dr. Omar Al-Rashid' },
      { id: 4, title: 'Programming Fundamentals', author: 'Dr. Michael Chen' },
      { id: 5, title: 'World Literature Anthology', author: 'Prof. Elena Rodriguez' }
    ];

    const book = books.find(b => b.id === rental.bookId) || {
      id: rental.bookId,
      title: 'Unknown Book',
      author: 'Unknown Author'
    };

    return {
      ...rental,
      book: book
    };
  });

  res.json({
    success: true,
    data: rentalsWithBooks,
    total: rentalsWithBooks.length
  });
});

// Return a book (end rental)
app.patch('/api/rentals/:id/return', (req, res) => {
  const rentalId = parseInt(req.params.id);

  if (!currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  const rentalIndex = activeRentals.findIndex(rental => rental.id === rentalId);
  if (rentalIndex === -1) {
    return res.status(404).json({
      success: false,
      message: 'Rental not found'
    });
  }

  const rental = activeRentals[rentalIndex];

  // Users can only return their own books
  if (currentUser.role === 'user' && rental.userId !== currentUser.id) {
    return res.status(403).json({
      success: false,
      message: 'You can only return your own books'
    });
  }

  // Mark as returned
  rental.status = 'returned';
  rental.returnDate = new Date().toISOString();

  console.log(`Book returned: rental ${rentalId} by ${currentUser.username}`);

  res.json({
    success: true,
    message: 'Book returned successfully',
    data: rental
  });
});

// Mock users/members endpoints (admin/librarian only)
app.get('/api/users', (req, res) => {
  if (!currentUser || !currentUser.permissions.canManageUsers) {
    return res.status(403).json({
      success: false,
      message: 'Access denied'
    });
  }

  res.json({
    success: true,
    data: [
      {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        membershipId: 'M001',
        status: 'active',
        joinDate: '2024-01-15'
      },
      {
        id: 2,
        name: 'Jane Smith',
        email: '<EMAIL>',
        membershipId: 'M002',
        status: 'active',
        joinDate: '2024-02-20'
      }
    ],
    total: 2
  });
});

// Dashboard stats endpoint (admin/librarian only)
app.get('/api/dashboard/stats', (req, res) => {
  if (!currentUser || !currentUser.permissions.canViewDashboard) {
    return res.status(403).json({
      success: false,
      message: 'Access denied. Dashboard access requires admin or librarian privileges.'
    });
  }

  res.json({
    success: true,
    data: {
      totalBooks: 150,
      availableBooks: 120,
      borrowedBooks: 30,
      totalMembers: 45,
      activeMembers: 42,
      overdueBooks: 5,
      newMembersThisMonth: 8,
      pendingRequests: rentalRequests.filter(req => req.status === 'pending').length,
      activeRentals: activeRentals.filter(rental => rental.status === 'active').length,
      popularCategories: [
        { name: 'Education', count: 45 },
        { name: 'Technology', count: 32 },
        { name: 'History', count: 28 },
        { name: 'Science', count: 25 }
      ]
    }
  });
});

// User profile endpoint
app.get('/api/profile', (req, res) => {
  if (!currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  const userStats = {
    totalRentals: activeRentals.filter(rental => rental.userId === currentUser.id).length,
    activeRentals: activeRentals.filter(rental => rental.userId === currentUser.id && rental.status === 'active').length,
    pendingRequests: rentalRequests.filter(req => req.userId === currentUser.id && req.status === 'pending').length,
    overdueBooks: 0 // Calculate based on due dates
  };

  res.json({
    success: true,
    data: {
      user: currentUser,
      stats: userStats
    }
  });
});

// Navigation/menu endpoint - returns what the user can access
app.get('/api/navigation', (req, res) => {
  if (!currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  const navigation = [];

  // All users can access books
  navigation.push({
    name: 'Books',
    path: '/books',
    icon: 'book',
    description: 'Search and browse library books'
  });

  // All users can see their rentals
  navigation.push({
    name: 'My Rentals',
    path: '/rentals',
    icon: 'rental',
    description: 'View your current and past book rentals'
  });

  // Only admin and librarian can access dashboard
  if (currentUser.permissions.canViewDashboard) {
    navigation.push({
      name: 'Dashboard',
      path: '/dashboard',
      icon: 'dashboard',
      description: 'Library management dashboard'
    });
  }

  // Only admin and librarian can manage rental requests
  if (currentUser.permissions.canApproveRentals) {
    navigation.push({
      name: 'Rental Requests',
      path: '/rental-requests',
      icon: 'requests',
      description: 'Manage pending rental requests'
    });
  }

  // Only admin can manage users
  if (currentUser.permissions.canManageUsers) {
    navigation.push({
      name: 'Users',
      path: '/users',
      icon: 'users',
      description: 'Manage library users'
    });
  }

  res.json({
    success: true,
    data: {
      navigation: navigation,
      userRole: currentUser.role,
      permissions: currentUser.permissions
    }
  });
});

// Legacy endpoint mappings (for frontend compatibility)
app.get('/api/loans', (req, res) => {
  // Redirect to rentals endpoint
  req.url = '/api/rentals';
  app._router.handle(req, res);
});

app.get('/api/readers', (req, res) => {
  if (!currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  // For regular users, return empty or limited data
  if (currentUser.role === 'user') {
    return res.json({
      success: true,
      data: [],
      message: 'Access restricted to user role'
    });
  }

  // For admin/librarian, redirect to users endpoint
  req.url = '/api/users';
  app._router.handle(req, res);
});

app.get('/api/stats', (req, res) => {
  if (!currentUser) {
    return res.status(401).json({
      success: false,
      message: 'Authentication required'
    });
  }

  // For regular users, return limited stats
  if (currentUser.role === 'user') {
    const userStats = {
      myActiveRentals: activeRentals.filter(rental => rental.userId === currentUser.id && rental.status === 'active').length,
      myPendingRequests: rentalRequests.filter(req => req.userId === currentUser.id && req.status === 'pending').length,
      myTotalRentals: activeRentals.filter(rental => rental.userId === currentUser.id).length
    };

    return res.json({
      success: true,
      data: userStats
    });
  }

  // For admin/librarian, redirect to dashboard stats
  req.url = '/api/dashboard/stats';
  app._router.handle(req, res);
});

// Sync status endpoint (for frontend polling)
app.get('/api/sync/status', (req, res) => {
  res.json({
    success: true,
    status: 'online',
    timestamp: new Date().toISOString(),
    server: 'IqraaManager Network Server'
  });
});

// Serve the simple working app as default
app.get('/', (req, res) => {
  console.log(`${new Date().toISOString()} - Serving main app`);
  res.sendFile(path.join(__dirname, 'dist/public/simple-app.html'));
});

// Serve the original complex app at /app
app.get('/app', (req, res) => {
  const fs = require('fs');
  const indexPath = path.join(__dirname, 'dist/public/index.html');

  try {
    let html = fs.readFileSync(indexPath, 'utf8');

    // Inject environment variables
    const envScript = `
    <script>
      // Override import.meta.env for network mode
      window.__VITE_ENV_OVERRIDE__ = {
        VITE_API_URL: 'http://${LOCAL_IP}:5000',
        VITE_SERVER_IP: '${LOCAL_IP}',
        VITE_BACKEND_PORT: '5000',
        VITE_FRONTEND_PORT: '5000',
        VITE_NETWORK_MODE: 'true',
        VITE_DB_TYPE: 'sqlite',
        VITE_PG_DISABLED: 'true',
        VITE_SYNC_ENABLED: 'false',
        MODE: 'production'
      };

      // Patch import.meta.env if it exists
      if (typeof window !== 'undefined') {
        window.addEventListener('DOMContentLoaded', function() {
          console.log('🌐 IqraaManager Network Mode - API URL:', window.__VITE_ENV_OVERRIDE__.VITE_API_URL);
        });
      }
    </script>`;

    // Insert the script before the closing head tag
    html = html.replace('</head>', envScript + '\n  </head>');

    res.setHeader('Content-Type', 'text/html');
    res.send(html);
  } catch (error) {
    console.error('Error serving index.html:', error);
    res.status(500).send('Error loading application');
  }
});

// Page routing with access control
app.get('/dashboard', (req, res) => {
  // Dashboard requires admin/librarian access
  res.sendFile(path.join(__dirname, 'dist/public/simple-app.html'));
});

app.get('/loans', (req, res) => {
  // Loans page (rentals)
  res.sendFile(path.join(__dirname, 'dist/public/simple-app.html'));
});

app.get('/rentals', (req, res) => {
  // Rentals page
  res.sendFile(path.join(__dirname, 'dist/public/simple-app.html'));
});

app.get('/books', (req, res) => {
  // Books page
  res.sendFile(path.join(__dirname, 'dist/public/simple-app.html'));
});

app.get('/readers', (req, res) => {
  // Readers/Users page
  res.sendFile(path.join(__dirname, 'dist/public/simple-app.html'));
});

app.get('/settings', (req, res) => {
  // Settings page - should be restricted but we'll handle it in frontend
  res.sendFile(path.join(__dirname, 'dist/public/simple-app.html'));
});

app.get('/auth', (req, res) => {
  // Auth page
  res.sendFile(path.join(__dirname, 'dist/public/simple-app.html'));
});

// Serve static files (CSS, JS, images) but not HTML files
app.use('/assets', express.static(path.join(__dirname, 'dist/public/assets')));
app.use('/favicon.png', express.static(path.join(__dirname, 'dist/public/favicon.png')));
app.use('/iqraa.png', express.static(path.join(__dirname, 'dist/public/iqraa.png')));

// Catch all handler for SPA routing (except root)
app.get('*', (req, res) => {
  // For SPA routes, redirect to root which will handle the routing
  if (!req.path.includes('.') && !req.path.startsWith('/api')) {
    res.sendFile(path.join(__dirname, 'dist/public/simple-app.html'));
  } else {
    res.sendFile(path.join(__dirname, 'dist/public/index.html'));
  }
});

// Start server
const PORT = 5000;

app.listen(PORT, '0.0.0.0', () => {
  console.log('');
  console.log('🚀 IqraaManager Network Server Started!');
  console.log('========================================');
  console.log(`🌐 Main App: http://${LOCAL_IP}:${PORT}`);
  console.log(`📊 Health check: http://${LOCAL_IP}:${PORT}/health`);
  console.log(`🔗 API status: http://${LOCAL_IP}:${PORT}/api/status`);
  console.log(`🧪 Test page: http://${LOCAL_IP}:${PORT}/test.html`);
  console.log(`⚡ Original app: http://${LOCAL_IP}:${PORT}/app`);
  console.log('');
  console.log('📱 Access from other devices:');
  console.log(`   Connect to same WiFi and open: http://${LOCAL_IP}:${PORT}`);
  console.log('');
  console.log('👤 Default login credentials:');
  console.log('   👑 Admin: admin / admin123 (Full access)');
  console.log('   📚 Librarian: librarian / librarian123 (Manage books, approve rentals)');
  console.log('   👤 User: user / user123 (Search books, request rentals)');
  console.log('');
  console.log('✨ Features:');
  console.log('   • Role-based access control');
  console.log('   • Book search and browsing');
  console.log('   • Rental request workflow');
  console.log('   • Admin/Librarian approval system');
  console.log('   • Network-accessible from any device');
  console.log('');
  console.log('⛔ Press Ctrl+C to stop the server');
  console.log('========================================');
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  process.exit(0);
});
