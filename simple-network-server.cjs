const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();

// Get local IP address
function getLocalIP() {
  const { networkInterfaces } = require('os');
  const nets = networkInterfaces();
  
  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
      if (net.family === 'IPv4' && !net.internal) {
        return net.address;
      }
    }
  }
  return '***********'; // fallback
}

const LOCAL_IP = getLocalIP();
console.log(`🌐 Detected local IP: ${LOCAL_IP}`);

// CORS setup for network access
app.use(cors({
  origin: '*',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
}));

app.use(express.json());

// Serve static files from dist/public
app.use(express.static(path.join(__dirname, 'dist/public')));

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: 'network',
    database: 'sqlite',
    version: '1.0.0',
    server_ip: LOCAL_IP
  });
});

// Basic API endpoints for demo
app.get('/api/status', (req, res) => {
  res.json({
    message: 'IqraaManager Network Server is running!',
    timestamp: new Date().toISOString(),
    server_ip: LOCAL_IP,
    frontend_url: `http://${LOCAL_IP}:5173`,
    backend_url: `http://${LOCAL_IP}:5000`
  });
});

// Add logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  if (req.body && Object.keys(req.body).length > 0) {
    console.log('Request body:', req.body);
  }
  next();
});

// Mock authentication endpoints
app.post('/api/auth/login', (req, res) => {
  console.log('Login attempt:', req.body);
  const { username, password } = req.body;

  // Simple mock authentication
  if ((username === 'admin' && password === 'admin123') ||
      (username === 'librarian' && password === 'librarian123')) {
    const user = {
      id: username === 'admin' ? 1 : 2,
      username: username,
      role: username === 'admin' ? 'admin' : 'librarian',
      email: `${username}@iqraa.local`,
      name: username === 'admin' ? 'Administrator' : 'Librarian'
    };

    res.json({
      success: true,
      user: user,
      token: 'mock-jwt-token-' + Date.now()
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'Invalid credentials'
    });
  }
});

// Alternative login endpoint (some apps use different paths)
app.post('/api/login', (req, res) => {
  console.log('Alternative login attempt:', req.body);
  // Redirect to main login endpoint
  req.url = '/api/auth/login';
  app._router.handle(req, res);
});

// Session check endpoint
app.get('/api/auth/me', (req, res) => {
  // Mock authenticated user
  res.json({
    success: true,
    user: {
      id: 1,
      username: 'admin',
      role: 'admin',
      email: '<EMAIL>',
      name: 'Administrator'
    }
  });
});

// Logout endpoint
app.post('/api/auth/logout', (req, res) => {
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

// Mock books endpoints
app.get('/api/books', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: 1,
        title: 'Introduction to Library Science',
        author: 'Dr. Ahmed Hassan',
        isbn: '978-0123456789',
        status: 'available',
        category: 'Education',
        location: 'A-001',
        publishedYear: 2020
      },
      {
        id: 2,
        title: 'Digital Library Management',
        author: 'Prof. Sarah Johnson',
        isbn: '978-0987654321',
        status: 'borrowed',
        category: 'Technology',
        location: 'B-002',
        publishedYear: 2021,
        borrowedBy: 'John Doe',
        dueDate: '2025-07-15'
      },
      {
        id: 3,
        title: 'Islamic History and Culture',
        author: 'Dr. Omar Al-Rashid',
        isbn: '978-0456789123',
        status: 'available',
        category: 'History',
        location: 'C-003',
        publishedYear: 2019
      }
    ],
    total: 3,
    page: 1,
    limit: 10
  });
});

// Add book endpoint
app.post('/api/books', (req, res) => {
  console.log('Adding book:', req.body);
  res.json({
    success: true,
    message: 'Book added successfully',
    data: {
      id: Date.now(),
      ...req.body,
      status: 'available',
      createdAt: new Date().toISOString()
    }
  });
});

// Update book endpoint
app.put('/api/books/:id', (req, res) => {
  console.log(`Updating book ${req.params.id}:`, req.body);
  res.json({
    success: true,
    message: 'Book updated successfully',
    data: {
      id: parseInt(req.params.id),
      ...req.body,
      updatedAt: new Date().toISOString()
    }
  });
});

// Delete book endpoint
app.delete('/api/books/:id', (req, res) => {
  console.log(`Deleting book ${req.params.id}`);
  res.json({
    success: true,
    message: 'Book deleted successfully'
  });
});

// Mock users/members endpoints
app.get('/api/users', (req, res) => {
  res.json({
    success: true,
    data: [
      {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        membershipId: 'M001',
        status: 'active',
        joinDate: '2024-01-15'
      },
      {
        id: 2,
        name: 'Jane Smith',
        email: '<EMAIL>',
        membershipId: 'M002',
        status: 'active',
        joinDate: '2024-02-20'
      }
    ],
    total: 2
  });
});

// Dashboard stats endpoint
app.get('/api/dashboard/stats', (req, res) => {
  res.json({
    success: true,
    data: {
      totalBooks: 150,
      availableBooks: 120,
      borrowedBooks: 30,
      totalMembers: 45,
      activeMembers: 42,
      overdueBooks: 5,
      newMembersThisMonth: 8,
      popularCategories: [
        { name: 'Education', count: 45 },
        { name: 'Technology', count: 32 },
        { name: 'History', count: 28 },
        { name: 'Science', count: 25 }
      ]
    }
  });
});

// Catch all handler for SPA routing
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'dist/public/index.html'));
});

// Start server
const PORT = 5000;

app.listen(PORT, '0.0.0.0', () => {
  console.log('');
  console.log('🚀 IqraaManager Network Server Started!');
  console.log('========================================');
  console.log(`🌐 Server running on: http://${LOCAL_IP}:${PORT}`);
  console.log(`📊 Health check: http://${LOCAL_IP}:${PORT}/health`);
  console.log(`🔗 API status: http://${LOCAL_IP}:${PORT}/api/status`);
  console.log('');
  console.log('📱 Access from other devices:');
  console.log(`   Connect to same WiFi and open: http://${LOCAL_IP}:${PORT}`);
  console.log('');
  console.log('👤 Default login credentials:');
  console.log('   Admin: admin / admin123');
  console.log('   Librarian: librarian / librarian123');
  console.log('');
  console.log('⛔ Press Ctrl+C to stop the server');
  console.log('========================================');
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  process.exit(0);
});
