import React, { useEffect, useState } from 'react';
import axios from 'axios';

const DashboardPage = () => {
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await axios.get('/api/dashboard');
                console.log('Fetched data:', response.data); // Debugging log
                setData(response.data);
            } catch (err) {
                console.error('Error fetching dashboard data:', err);
                setError('Failed to load dashboard data.');
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    if (loading) return <div>Loading...</div>;
    if (error) return <div>{error}</div>;

    return (
        <div>
            {/* Render dashboard data */}
            {data && Object.keys(data).length > 0 ? (
                <div>
                    <h2>Dashboard Data</h2>
                    {Object.entries(data).map(([key, value]) => {
                        console.log(`Rendering key: ${key}, value:`, value); // Debugging log
                        return (
                            <div key={key} className="dashboard-item">
                                <strong>{key}:</strong> {JSON.stringify(value)}
                            </div>
                        );
                    })}
                </div>
            ) : (
                <div>No data available.</div>
            )}
        </div>
    );
};

export default DashboardPage;
