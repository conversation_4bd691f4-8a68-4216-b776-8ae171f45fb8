import React, { useEffect, useState } from 'react';
import axios from 'axios';

const RequestsPage = () => {
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await axios.get('/api/requests');
                setData(response.data);
            } catch (err) {
                console.error('Error fetching requests data:', err);
                setError('Failed to load requests data.');
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    if (loading) return <div>Loading...</div>;
    if (error) return <div>{error}</div>;

    return (
        <div>
            {/* Render requests data */}
            {data ? (
                <div>{JSON.stringify(data)}</div>
            ) : (
                <div>No data available.</div>
            )}
        </div>
    );
};

export default RequestsPage;
