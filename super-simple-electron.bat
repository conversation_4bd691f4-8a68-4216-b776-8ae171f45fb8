@echo off
echo ==================================
echo Test ELECTRON SUPER-SIMPLE
echo ==================================

REM Installer electron globalement si necessaire
where electron >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
  echo Installation d'Electron...
  call npm install electron -g
)

REM Creer un fichier HTML statique
echo ^<!DOCTYPE html^> > test.html
echo ^<html^> >> test.html
echo ^<head^>^<title^>iQraa Test^</title^>^</head^> >> test.html
echo ^<body style="text-align:center;padding:50px;font-family:Arial;"^> >> test.html
echo   ^<h1^>iQraa Test^</h1^> >> test.html
echo   ^<p^>Test Electron - L'application fonctionne correctement!^</p^> >> test.html
echo ^</body^> >> test.html
echo ^</html^> >> test.html

REM Creer un fichier main ultra-simple pour Electron
echo const {app, BrowserWindow} = require('electron'); > electron-main.js
echo let win; >> electron-main.js
echo function createWindow() { >> electron-main.js
echo   win = new BrowserWindow({width: 800, height: 600}); >> electron-main.js
echo   win.loadFile('test.html'); >> electron-main.js
echo   win.on('closed', () => { win = null; }); >> electron-main.js
echo } >> electron-main.js
echo app.on('ready', createWindow); >> electron-main.js
echo app.on('window-all-closed', () => { app.quit(); }); >> electron-main.js

echo Demarrage de l'application Electron...
electron electron-main.js

echo Test termine!