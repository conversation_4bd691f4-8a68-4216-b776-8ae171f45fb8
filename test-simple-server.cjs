const express = require('express');
const cors = require('cors');

const app = express();

// Basic CORS setup
app.use(cors({
  origin: '*',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
}));

app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    environment: 'test',
    database: 'sqlite',
    version: '1.0.0'
  });
});

// Simple API endpoint
app.get('/api/test', (req, res) => {
  res.json({
    message: 'Server is working!',
    timestamp: new Date().toISOString()
  });
});

// Start server
const port = 5000;
const host = '0.0.0.0';

app.listen(port, host, () => {
  console.log(`🚀 Test server running on http://***********:${port}`);
  console.log(`📊 Health check: http://***********:${port}/health`);
  console.log(`🧪 Test API: http://***********:${port}/api/test`);
  console.log(`🌐 Server accessible from network on host: ${host}`);
});
