# 🪟 Windows Startup Solution for IQRAA Manager

## Issue Fixed ✅

The problem was that the npm scripts were using Unix-style environment variable syntax (`NODE_ENV=value`) which doesn't work on Windows Command Prompt. I've fixed this by adding `cross-env` to all the scripts.

## ✅ Fixed Scripts

The following scripts have been updated to work on Windows:

```json
{
  "dev:local": "cross-env NODE_ENV=development DB_TYPE=sqlite PG_DISABLED=true tsx server/index-sqlite.ts",
  "dev:frontend": "cross-env NODE_ENV=development vite --config vite-local.config.ts",
  "dev:network": "cross-env NODE_ENV=development DB_TYPE=sqlite PG_DISABLED=true HOST=0.0.0.0 CORS_ORIGIN=* tsx server/index-sqlite.ts",
  "dev:network-frontend": "cross-env VITE_API_URL=http://***********:5000 VITE_SERVER_IP=*********** vite --config vite.config.network.ts"
}
```

## 🚀 How to Start the Application

### Method 1: NPM Scripts (Now Fixed)
```bash
# Terminal 1 - Backend
npm run dev:network

# Terminal 2 - Frontend
npm run dev:network-frontend

# Or both together (if concurrently works)
npm run dev:network-full
```

### Method 2: Batch Scripts (Recommended for Windows)
```bash
# Start both servers
start-both-servers.bat

# Or start individually
test-backend-only.bat
start-simple-frontend.bat
```

### Method 3: PowerShell (Advanced)
```powershell
# Backend
powershell -ExecutionPolicy Bypass -File start-backend-debug.ps1

# Frontend
powershell -ExecutionPolicy Bypass -File start-network-servers.ps1
```

### Method 4: Manual Commands (Fallback)
```bash
# Backend
npx cross-env NODE_ENV=development DB_TYPE=sqlite PG_DISABLED=true HOST=0.0.0.0 CORS_ORIGIN=* npx tsx server/index-sqlite.ts

# Frontend (new terminal)
npx cross-env VITE_API_URL=http://***********:5000 VITE_SERVER_IP=*********** npx vite --config vite.config.network.ts
```

## 🔍 Verification Steps

### 1. Check if Backend is Running
Open browser or use curl:
```
http://***********:5000/health
```

Should return:
```json
{
  "status": "ok",
  "timestamp": "...",
  "environment": "development",
  "database": "sqlite",
  "version": "1.0.0"
}
```

### 2. Check if Frontend is Running
Open browser:
```
http://***********:5173
```

Should show the IQRAA Manager login page.

### 3. Test API Connection
Once both are running, the frontend should be able to connect to the backend without ECONNREFUSED errors.

## 🌐 Access URLs

### From the server machine:
- **Frontend**: http://localhost:5173 or http://***********:5173
- **Backend**: http://localhost:5000 or http://***********:5000

### From other devices on the network:
- **Frontend**: http://***********:5173
- **Backend**: http://***********:5000

## 👤 Login Credentials

- **Admin**: username: `admin`, password: `admin123`
- **Librarian**: username: `librarian`, password: `librarian123`

## 🛠️ Troubleshooting

### If npm scripts still don't work:
1. **Install dependencies**: `npm install`
2. **Use batch scripts**: `start-both-servers.bat`
3. **Use manual commands** (Method 4 above)

### If "cross-env not found":
```bash
npm install cross-env
```

### If "tsx not found":
```bash
npm install tsx
```

### If port conflicts:
```bash
# Check what's using the ports
netstat -an | findstr :5000
netstat -an | findstr :5173

# Kill processes if needed
taskkill /PID <PID_NUMBER> /F
```

### If database errors:
- Check if `sqlite.db` exists in the project root
- If corrupted, delete it and restart the server (it will recreate)

## 📱 Mobile Testing

1. Ensure your mobile device is on the same WiFi network
2. Find your computer's IP address: `ipconfig`
3. Update `.env.local` if IP changed
4. Access: http://YOUR_IP:5173 from mobile browser

## 🎯 Quick Start (Choose One)

**Easiest (Windows)**:
```bash
start-both-servers.bat
```

**NPM Scripts (Now Fixed)**:
```bash
npm run dev:network-full
```

**Manual (Most Reliable)**:
```bash
# Terminal 1
npx cross-env NODE_ENV=development DB_TYPE=sqlite PG_DISABLED=true HOST=0.0.0.0 CORS_ORIGIN=* npx tsx server/index-sqlite.ts

# Terminal 2
npx cross-env VITE_API_URL=http://***********:5000 VITE_SERVER_IP=*********** npx vite --config vite.config.network.ts
```

## 📊 Expected Success Output

### Backend (Terminal 1):
```
Base de données SQLite initialisée avec succès
Métadonnées de synchronisation initialisées avec succès
🚀 iQraa Server running on http://***********:5000
📊 API endpoints available at http://***********:5000/api
🗄️ Database: SQLite (sqlite.db)
🔄 Sync: Disabled
🌐 Network access enabled
```

### Frontend (Terminal 2):
```
VITE v5.x.x ready in xxx ms

➜  Local:   http://localhost:5173/
➜  Network: http://***********:5173/
```

## 🔄 Next Steps

1. ✅ Start backend: `npm run dev:network`
2. ✅ Start frontend: `npm run dev:network-frontend`
3. 🌐 Access: http://***********:5173
4. 👤 Login: admin / admin123
5. 📱 Test from mobile: same URL

The Windows environment variable issue has been resolved with `cross-env`!
