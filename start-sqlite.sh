#!/bin/bash
# Script pour démarrer l'application avec SQLite uniquement

echo "🚀 Démarrage de l'application iQraa avec SQLite uniquement"

# Vérifier si nous sommes dans un environnement Windows
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
  echo "💻 Environnement Windows détecté"
  CMD_PREFIX="npx cross-env"
else  
  echo "💻 Environnement Unix/Linux/Mac détecté"
  CMD_PREFIX=""
fi

echo "📦 Démarrage du serveur en mode SQLite sur http://localhost:5000"
$CMD_PREFIX NODE_ENV=development DB_TYPE=sqlite node ./node_modules/.bin/tsx server/index-sqlite.ts

echo ""
echo "⛔ Serveur arrêté"