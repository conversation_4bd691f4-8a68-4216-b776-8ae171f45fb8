@echo off
setlocal EnableDelayedExpansion

REM Définir les couleurs pour une meilleure lisibilité
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "RESET=[0m"

title iQraa - Serveur Local

echo %BLUE%========================================================%RESET%
echo %GREEN%🚀 Démarrage de l'application iQraa avec serveur local%RESET%
echo %BLUE%========================================================%RESET%

REM Vérifier les prérequis
echo %YELLOW%🔍 Vérification des prérequis...%RESET%
where node >nul 2>&1 || (
  echo %RED%❌ Node.js n'est pas installé ou n'est pas dans le PATH.%RESET%
  echo Veuillez installer Node.js depuis https://nodejs.org/
  pause
  exit /b 1
)

where npm >nul 2>&1 || (
  echo %RED%❌ NPM n'est pas installé ou n'est pas dans le PATH.%RESET%
  pause
  exit /b 1
)

REM Vérifier si les dépendances sont installées
if not exist "node_modules" (
  echo %YELLOW%📦 Installation des dépendances...%RESET%
  call npm install
  if %ERRORLEVEL% NEQ 0 (
    echo %RED%❌ Erreur lors de l'installation des dépendances.%RESET%
    pause
    exit /b 1
  )
)

REM Nettoyer les processus existants
echo %YELLOW%🔄 Nettoyage des processus existants...%RESET%
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :5000') DO (
  echo  - Arrêt du processus sur le port 5000 (PID: %%P)
  TaskKill /PID %%P /F /T >NUL 2>&1
)
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :5173') DO (
  echo  - Arrêt du processus sur le port 5173 (PID: %%P)
  TaskKill /PID %%P /F /T >NUL 2>&1
)

REM Créer le fichier de base de données SQLite s'il n'existe pas
if not exist "sqlite.db" (
  echo %YELLOW%🗄️ Initialisation de la base de données SQLite...%RESET%
  echo. > sqlite.db
)

REM Démarrer le serveur backend
echo %GREEN%🌐 Démarrage du serveur backend sur http://localhost:5000%RESET%
start "iQraa Backend" cmd /c "npx cross-env NODE_ENV=development DB_TYPE=sqlite PG_DISABLED=true SQLITE_PATH=./sqlite.db PORT=5000 npx tsx server/index-sqlite.ts"

REM Attendre que le backend se lance
echo %YELLOW%⏳ Attente du démarrage du backend (5 secondes)...%RESET%
ping -n 6 127.0.0.1 > nul

REM Vérifier si le serveur backend est démarré
set "BACKEND_STARTED=0"
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :5000') DO set BACKEND_STARTED=1
if %BACKEND_STARTED% NEQ 1 (
  echo %RED%⚠️ Le serveur backend ne semble pas démarré sur le port 5000%RESET%
  echo %YELLOW%⏳ Attente supplémentaire (5 secondes)...%RESET%
  ping -n 6 127.0.0.1 > nul
)

REM Démarrer le serveur frontend
echo %GREEN%🖥️ Démarrage du serveur frontend sur http://localhost:5173%RESET%
start "iQraa Frontend" cmd /c "npx cross-env NODE_ENV=development npx vite --config vite-local.config.ts"

echo.
echo %GREEN%✅ Application démarrée avec succès!%RESET%
echo %GREEN%👉 Frontend: http://localhost:5173%RESET%
echo %GREEN%👉 Backend API: http://localhost:5000/api%RESET%
echo.
echo %BLUE%========================================================%RESET%
echo %YELLOW%Comptes par défaut:%RESET%
echo %YELLOW%  Admin: admin / admin123%RESET%
echo %YELLOW%  Bibliothécaire: librarian / librarian123%RESET%
echo %BLUE%========================================================%RESET%
echo %YELLOW%⛔ Fermez cette fenêtre pour terminer tous les processus.%RESET%
echo %BLUE%========================================================%RESET%

REM Attendre que l'utilisateur appuie sur une touche
pause > nul

REM Tuer tous les processus
echo.
echo %YELLOW%🛑 Arrêt des processus...%RESET%
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :5000') DO TaskKill /PID %%P /F /T >NUL 2>&1
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :5173') DO TaskKill /PID %%P /F /T >NUL 2>&1

echo %GREEN%✅ Application arrêtée.%RESET%
timeout /t 2 > nul
