# Guide spécial pour Windows

Ce document contient des instructions spécifiques pour faire fonctionner l'application iQraa sous Windows, en particulier pour résoudre les problèmes courants liés aux chemins contenant des espaces (comme "Program Files").

## Résolution des problèmes courants

### Problème d'exécution avec chemins contenant des espaces

Si vous rencontrez des erreurs du type :
```
'C:\Program' n'est pas reconnu en tant que commande interne ou externe, un programme exécutable ou un fichier de commandes.
```

Ou des erreurs comme :
```
Cannot find package 'vite' imported from C:\Users\<USER>\server\vite.ts
Le binaire TSX n'a pas été trouvé
```

C'est parce que Windows a des problèmes avec les chemins contenant des espaces. Nous avons préparé des solutions :

> Pour une documentation détaillée sur la résolution de tous les problèmes de chemins sous Windows, consultez le guide [fix-windows-paths.md](fix-windows-paths.md).

## Scripts améliorés pour Windows

Nous avons créé de nouveaux scripts optimisés pour Windows. Utilisez ces versions au lieu des scripts originaux :

### 1. Pour le développement local (frontend + backend)

Utilisez `start-windows.bat` pour une fiabilité maximale :

```cmd
start-windows.bat
```

Ce nouveau script est spécialement optimisé pour Windows et :
- Vérifie automatiquement les dépendances manquantes
- Installe les modules nécessaires s'ils ne sont pas présents
- Résout les problèmes de modules non trouvés (vite, tsx, etc.)
- Démarre les serveurs backend et frontend dans des fenêtres séparées

Comme alternative, vous pouvez toujours utiliser `new-start-local.bat` :

```cmd
new-start-local.bat
```

### 2. Pour tester Electron

Utilisez `new-test-electron.sh` au lieu de `test-electron.sh` :

```bash
# Si vous utilisez Git Bash sur Windows
chmod +x new-test-electron.sh
./new-test-electron.sh
```

### 3. Pour tester Capacitor (mobile)

Utilisez `new-test-capacitor.sh` au lieu de `test-capacitor.sh` :

```bash
# Si vous utilisez Git Bash sur Windows
chmod +x new-test-capacitor.sh
./new-test-capacitor.sh
```

## Conseils pour Windows

1. **PowerShell vs Command Prompt vs Git Bash**
   - Si vous utilisez Git Bash, les scripts `.sh` fonctionneront généralement mieux
   - Si vous utilisez PowerShell ou CMD, privilégiez les scripts `.bat`

2. **Installez Node.js dans un chemin sans espaces**
   - Évitez les chemins contenant "Program Files"
   - Préférez un chemin comme `C:\nodejs` ou `C:\dev\nodejs`

3. **Utilisez `npx` pour lancer les commandes**
   - `npx` est plus fiable que d'appeler directement les binaires dans `node_modules/.bin`
   - Exemple : `npx tsx server/index.ts` au lieu de `node ./node_modules/.bin/tsx server/index.ts`

4. **Préférez les scripts Node.js aux scripts shell**
   - Les scripts JavaScript fonctionnent mieux sous Windows que les scripts shell
   - Utilisez `node scripts/local-dev.js` au lieu de scripts shell complexes

## Scripts de résolution automatique des problèmes

Pour vous aider à résoudre les problèmes de dépendances et de chemins sous Windows, nous avons créé plusieurs scripts automatiques:

### 1. Méthode rapide : Utiliser start-windows.bat

Le moyen le plus rapide et fiable de résoudre tous les problèmes est d'utiliser le script `start-windows.bat` :

```cmd
start-windows.bat
```

Ce script va automatiquement :
- Réparer toutes les dépendances
- Installer les modules manquants
- Résoudre les problèmes de modules non trouvés
- Démarrer l'application correctement

### 2. Résolution des problèmes spécifiques de dépendances

Si vous préférez résoudre manuellement les problèmes de dépendances :

```cmd
fix-windows-dependencies.bat
```

Ce script va :
- Nettoyer complètement vos dépendances existantes
- Réinstaller toutes les dépendances nécessaires
- Configurer correctement l'environnement pour Windows
- Créer une correction temporaire pour les modules non trouvés

### 3. Résolution des problèmes généraux de chemins

```bash
# Sous Git Bash
chmod +x fix-dependencies.sh
./fix-dependencies.sh

# Ou sous CMD/PowerShell
bash fix-dependencies.sh
```

### 4. Correction des problèmes de Capacitor

Si vous rencontrez l'erreur `'apacitor-dev.js"' n'est pas reconnu` :

```bash
node fix-capacitor.js
```

Ce script corrige les erreurs typographiques et crée les fichiers nécessaires pour Capacitor.

### 5. Méthode manuelle (si rien d'autre ne fonctionne)

Lancez deux fenêtres de commande séparées et exécutez :

```cmd
# Dans la fenêtre 1 (serveur backend) :
npx cross-env NODE_ENV=development tsx server/index.ts

# Dans la fenêtre 2 (serveur frontend) :
npx vite --port 5173
```