# Journal des modifications (Changelog)

Ce document retrace les principales modifications apportées au projet iQraa.

## [Unreleased]

### Corrections de bugs
- Correction du problème de build Docker dans CapRover avec l'erreur "vite: not found"
- Installation globale de vite dans le Dockerfile pour assurer sa disponibilité
- Utilisation de npx pour exécuter vite et esbuild dans le Dockerfile
- Correction du chemin d'importation dans new-capacitor-dev.js
- Amélioration de new-test-capacitor.sh pour détecter le bon script à exécuter

### Améliorations
- Scripts Windows et MacOS/Linux optimisés pour gérer les chemins contenant des espaces
- Documentation complète pour la configuration PostgreSQL dans CapRover
- Documentation étendue avec des instructions de résolution de problèmes CapRover
- Guide spécifique (fix-windows-paths.md) pour résoudre les problèmes Windows
- Compatibilité Windows améliorée pour tous les modes de déploiement

### Nouveaux scripts
- `start-windows.bat` - Script tout-en-un optimisé pour Windows avec résolution automatique des problèmes
- `new-start-local.bat` - Version Windows optimisée du script de démarrage local
- `new-start-local.sh` - Version Linux/Mac optimisée du script de démarrage local
- `new-test-electron.sh` - Version optimisée du script de test Electron
- `new-test-capacitor.sh` - Version optimisée du script de test Capacitor
- `new-capacitor-dev.js` - Script JavaScript optimisé pour le développement Capacitor
- `fix-dependencies.sh` - Script de résolution automatique des problèmes de dépendances
- `fix-windows-paths.md` - Guide détaillé pour résoudre les problèmes de chemins Windows
- `fix-windows-dependencies.bat` - Script spécifique à Windows pour les problèmes de Rollup/Vite
- `fix-capacitor.js` - Script pour corriger les erreurs spécifiques à Capacitor sous Windows

## [1.0.0] - 2025-05-08

### Fonctionnalités principales
- Système de gestion de bibliothèque complet
- Mode hors ligne avec SQLite
- Synchronisation bidirectionnelle avec PostgreSQL
- Déploiement multi-plateformes (Web, Desktop, Mobile)
- Prise en charge multilingue (Français, Arabe, Anglais)
- Interface d'administration complète

### Fonctionnalités détaillées
- Gestion complète des livres (ajout, modification, suppression)
- Gestion des lecteurs et des prêts
- Suivi des activités et statistiques
- Authentification avec contrôle d'accès par rôle
- Interface adaptative pour mobile, tablette et desktop