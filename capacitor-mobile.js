// Script pour initialiser et construire l'application mobile avec Capacitor
import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Vérifier si Capacitor est déjà initialisé
const androidDir = path.join(__dirname, 'android');
const iosDir = path.join(__dirname, 'ios');

try {
  // Étape 1: Construction de l'application web (avec DB_TYPE=sqlite)
  console.log('📦 Construction de l\'application web...');
  execSync('npx cross-env NODE_ENV=production DB_TYPE=sqlite npm run build', { stdio: 'inherit' });

  // Étape 2: Synchronisation avec Capacitor
  console.log('🔄 Synchronisation avec Capacitor...');
  execSync('npx cap sync', { stdio: 'inherit' });

  // Étape 3: Ajouter la plateforme Android si elle n'existe pas
  if (!fs.existsSync(androidDir)) {
    console.log('📱 Ajout de la plateforme Android...');
    execSync('npx cap add android', { stdio: 'inherit' });
  } else {
    console.log('✅ Plateforme Android déjà configurée.');
  }

  // Étape 4: Mise à jour des ressources
  console.log('🔃 Mise à jour des ressources...');
  execSync('npx cap copy android', { stdio: 'inherit' });

  // Étape 5: Ouvrir Android Studio pour la compilation
  console.log('🚀 Ouverture d\'Android Studio...');
  execSync('npx cap open android', { stdio: 'inherit' });

  console.log('✨ Configuration terminée avec succès!');
  console.log('\n📝 Instructions pour la compilation dans Android Studio:');
  console.log('1. Attendez qu\'Android Studio charge le projet');
  console.log('2. Sélectionnez "Build > Build Bundle(s) / APK(s) > Build APK(s)"');
  console.log('3. Une fois terminé, cliquez sur "locate" pour trouver le fichier APK généré');

} catch (error) {
  console.error('❌ Erreur lors de la configuration:', error.message);
  process.exit(1);
}