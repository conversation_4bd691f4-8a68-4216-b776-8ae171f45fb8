#!/usr/bin/env node

/**
 * Simple test script to start the server and check for issues
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🔍 Testing server startup...');

// Set environment variables
const env = {
  ...process.env,
  NODE_ENV: 'development',
  DB_TYPE: 'sqlite',
  PG_DISABLED: 'true',
  SQLITE_PATH: './sqlite.db',
  PORT: '5000',
  HOST: '0.0.0.0',
  SERVER_IP: '***********',
  CORS_ORIGIN: '*',
  ALLOW_NETWORK_ACCESS: 'true'
};

console.log('📋 Environment variables:');
console.log('  NODE_ENV:', env.NODE_ENV);
console.log('  DB_TYPE:', env.DB_TYPE);
console.log('  PORT:', env.PORT);
console.log('  HOST:', env.HOST);
console.log('  SERVER_IP:', env.SERVER_IP);

// Start the server
console.log('\n🚀 Starting server...');

const serverProcess = spawn('npx', ['tsx', 'server/index-sqlite.ts'], {
  env,
  stdio: 'inherit',
  shell: true
});

serverProcess.on('error', (error) => {
  console.error('❌ Server process error:', error);
});

serverProcess.on('exit', (code, signal) => {
  console.log(`\n📊 Server process exited with code ${code} and signal ${signal}`);
});

// Handle Ctrl+C
process.on('SIGINT', () => {
  console.log('\n🛑 Stopping server...');
  serverProcess.kill('SIGINT');
  process.exit(0);
});

// Test server after a delay
setTimeout(async () => {
  console.log('\n🧪 Testing server connection...');
  
  try {
    const http = require('http');
    
    const options = {
      hostname: '***********',
      port: 5000,
      path: '/health',
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      console.log(`✅ Server responded with status: ${res.statusCode}`);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log('📄 Response:', data);
      });
    });

    req.on('error', (error) => {
      console.error('❌ Connection error:', error.message);
    });

    req.on('timeout', () => {
      console.error('⏰ Request timeout');
      req.destroy();
    });

    req.end();
    
  } catch (error) {
    console.error('❌ Test error:', error);
  }
}, 10000); // Wait 10 seconds for server to start
