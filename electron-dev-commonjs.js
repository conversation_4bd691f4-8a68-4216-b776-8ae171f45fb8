// Script pour lancer Electron en mode développement
const { spawn } = require('child_process');
const path = require('path');

// Obtenir le répertoire actuel
const projectRoot = __dirname;

console.log('🚀 Démarrage d\'Electron en mode développement...');

// Variables d'environnement pour le mode développement
const env = {
  ...process.env,
  NODE_ENV: 'development',
  DB_TYPE: 'sqlite'
};

// Démarrer le serveur
console.log('📦 Démarrage du serveur backend (SQLite)...');
const serverProcess = spawn('node', ['./node_modules/.bin/tsx', 'server/index-sqlite.ts'], {
  cwd: projectRoot,
  stdio: 'inherit',
  env
});

// Attendre que le serveur soit prêt
setTimeout(() => {
  console.log('🖥️ Démarrage d\'Electron...');
  const electronProcess = spawn('electron', ['electron-wrapper.js'], {
    cwd: projectRoot,
    stdio: 'inherit',
    env
  });

  // Gérer la fermeture propre
  electronProcess.on('close', () => {
    console.log('⛔ Electron fermé, arrêt du serveur...');
    serverProcess.kill();
    process.exit(0);
  });
}, 3000); // Attendre 3 secondes pour que le serveur démarre

// Gérer les signaux pour arrêter proprement
const cleanup = () => {
  console.log('\n🛑 Arrêt des processus...');
  serverProcess.kill();
  process.exit(0);
};

process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);