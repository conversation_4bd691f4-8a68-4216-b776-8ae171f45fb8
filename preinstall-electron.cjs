#!/usr/bin/env node

/**
 * Script pour préinstaller les dépendances nécessaires pour Electron
 * dans le dossier dist
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('Préinstallation des dépendances pour Electron');
console.log('-------------------------------------------');

const distDir = path.join(__dirname, 'dist');

// Vérifier si le dossier dist existe
if (!fs.existsSync(distDir)) {
  console.error('❌ Erreur: Le dossier dist n\'existe pas. Veuillez d\'abord exécuter le build.');
  process.exit(1);
}

// Vérifier si package.json existe dans le dossier dist
if (!fs.existsSync(path.join(distDir, 'package.json'))) {
  console.error('❌ Erreur: package.json n\'existe pas dans le dossier dist.');
  process.exit(1);
}

// Installation des dépendances avec NPM
try {
  console.log('Installation des dépendances dans le dossier dist...');
  
  // Installer module-alias qui est nécessaire pour la résolution des chemins
  execSync('cd dist && npm install --save module-alias', {
    stdio: 'inherit'
  });
  
  // Installer les autres dépendances
  execSync('cd dist && npm install --production', {
    stdio: 'inherit'
  });
  
  console.log('✓ Dépendances installées avec succès!');
} catch (error) {
  console.error('❌ Erreur lors de l\'installation des dépendances:', error);
  process.exit(1);
}

console.log('\nApplication prête à être lancée avec Electron.');
console.log('Exécutez new-test-electron.bat pour tester l\'application.');