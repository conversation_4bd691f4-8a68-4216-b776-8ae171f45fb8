@echo off
echo ==================================
echo Test ULTRA-SIMPLIFIE de l'application iQraa avec Electron
echo ==================================

REM Installer electron globalement si necessaire
where electron >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
  echo Installation d'Electron (peut prendre quelques minutes)...
  call npm install electron -g
)

REM Creer le fichier index.html s'il n'existe pas deja
if not exist index.html (
  echo Creation d'un fichier HTML temporaire...
  echo ^<!DOCTYPE html^> > index.html
  echo ^<html^> >> index.html
  echo ^<head^>^<title^>iQraa Test^</title^>^</head^> >> index.html
  echo ^<body style="text-align:center;padding:50px;font-family:Arial;"^> >> index.html
  echo   ^<h1^>iQraa Test^</h1^> >> index.html
  echo   ^<p^>Test Electron - L'application fonctionne correctement!^</p^> >> index.html
  echo ^</body^> >> index.html
  echo ^</html^> >> index.html
)

REM Creer un fichier main.js temporaire pour Electron
echo const {app, BrowserWindow} = require('electron'); > temp-electron-main.js
echo let win; >> temp-electron-main.js
echo function createWindow() { >> temp-electron-main.js
echo   win = new BrowserWindow({width: 800, height: 600}); >> temp-electron-main.js
echo   win.loadFile('index.html'); >> temp-electron-main.js
echo   win.on('closed', () => { win = null; }); >> temp-electron-main.js
echo } >> temp-electron-main.js
echo app.on('ready', createWindow); >> temp-electron-main.js
echo app.on('window-all-closed', () => { app.quit(); }); >> temp-electron-main.js

echo Demarrage d'Electron avec une configuration minimale...
echo (Cette approche utilise uniquement les fichiers locaux, sans compilations)
electron temp-electron-main.js

REM Nettoyage
echo Nettoyage des fichiers temporaires...
del temp-electron-main.js

echo Test termine!