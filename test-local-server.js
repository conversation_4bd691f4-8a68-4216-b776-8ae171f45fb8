#!/usr/bin/env node

/**
 * Script de test pour vérifier la configuration du serveur local
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

// Configuration
const BACKEND_URL = 'http://localhost:5000';
const FRONTEND_URL = 'http://localhost:5173';
const TIMEOUT = 5000; // 5 secondes

// Détecter la configuration réseau
function getNetworkConfig() {
  try {
    const fs = require('fs');
    if (fs.existsSync('.env.local')) {
      const envContent = fs.readFileSync('.env.local', 'utf8');
      const serverIPMatch = envContent.match(/SERVER_IP=(.+)/);
      if (serverIPMatch) {
        const serverIP = serverIPMatch[1].trim();
        return {
          isNetworkMode: true,
          serverIP,
          networkBackendURL: `http://${serverIP}:5000`,
          networkFrontendURL: `http://${serverIP}:5173`
        };
      }
    }
  } catch (error) {
    // Ignore errors
  }
  return { isNetworkMode: false };
}

// Couleurs pour la console
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkUrl(url, description) {
  return new Promise((resolve) => {
    const request = http.get(url, { timeout: TIMEOUT }, (res) => {
      if (res.statusCode === 200) {
        log(`✅ ${description}: OK (${res.statusCode})`, 'green');
        resolve(true);
      } else {
        log(`⚠️ ${description}: ${res.statusCode}`, 'yellow');
        resolve(false);
      }
    });

    request.on('error', (err) => {
      log(`❌ ${description}: ${err.message}`, 'red');
      resolve(false);
    });

    request.on('timeout', () => {
      log(`⏰ ${description}: Timeout`, 'yellow');
      request.destroy();
      resolve(false);
    });
  });
}

function checkFile(filePath, description) {
  try {
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      log(`✅ ${description}: Existe (${stats.size} bytes)`, 'green');
      return true;
    } else {
      log(`❌ ${description}: Fichier manquant`, 'red');
      return false;
    }
  } catch (err) {
    log(`❌ ${description}: Erreur - ${err.message}`, 'red');
    return false;
  }
}

async function runTests() {
  const networkConfig = getNetworkConfig();

  if (networkConfig.isNetworkMode) {
    log('🧪 Test de la configuration réseau IQRAA', 'blue');
    log(`📍 IP du serveur: ${networkConfig.serverIP}`, 'cyan');
  } else {
    log('🧪 Test de la configuration du serveur local IQRAA', 'blue');
  }
  log('================================================', 'blue');

  // Test des fichiers de configuration
  log('\n📁 Vérification des fichiers de configuration:', 'yellow');
  const configFiles = [
    { path: '.env.local', desc: 'Fichier de configuration locale' },
    { path: 'vite-local.config.ts', desc: 'Configuration Vite locale' },
    { path: 'package.json', desc: 'Configuration NPM' },
    { path: 'sqlite.db', desc: 'Base de données SQLite' },
  ];

  let configOk = true;
  for (const file of configFiles) {
    if (!checkFile(file.path, file.desc)) {
      configOk = false;
    }
  }

  // Test des dépendances
  log('\n📦 Vérification des dépendances:', 'yellow');
  const nodeModulesOk = checkFile('node_modules', 'Dossier node_modules');

  // Test des serveurs
  log('\n🌐 Test de connectivité des serveurs:', 'yellow');
  const backendOk = await checkUrl(`${BACKEND_URL}/health`, 'Backend Health Check (Local)');
  const backendApiOk = await checkUrl(`${BACKEND_URL}/api/user`, 'Backend API (Local)');

  // Test réseau si configuré
  let networkBackendOk = true;
  let networkFrontendOk = true;

  if (networkConfig.isNetworkMode) {
    log('\n🌐 Test de connectivité réseau:', 'yellow');
    networkBackendOk = await checkUrl(`${networkConfig.networkBackendURL}/health`, 'Backend Health Check (Network)');
    networkFrontendOk = await checkUrl(networkConfig.networkFrontendURL, 'Frontend Vite Server (Network)');
  }

  // Note: Le frontend peut ne pas être démarré, c'est normal
  log('\n🖥️ Test du frontend local (optionnel):', 'yellow');
  const frontendOk = await checkUrl(FRONTEND_URL, 'Frontend Vite Server (Local)');

  // Résumé
  log('\n📊 Résumé des tests:', 'blue');
  log('==================', 'blue');
  
  if (configOk && nodeModulesOk && backendOk) {
    log('✅ Configuration de base: OK', 'green');
  } else {
    log('❌ Configuration de base: Problèmes détectés', 'red');
  }

  if (backendOk && backendApiOk) {
    log('✅ Serveur backend: Fonctionnel', 'green');
  } else {
    log('❌ Serveur backend: Non fonctionnel', 'red');
  }

  if (frontendOk) {
    log('✅ Serveur frontend: Fonctionnel', 'green');
  } else {
    log('⚠️ Serveur frontend: Non démarré (normal si pas encore lancé)', 'yellow');
  }

  // Recommandations
  log('\n💡 Recommandations:', 'blue');
  log('==================', 'blue');

  if (!nodeModulesOk) {
    log('📦 Exécutez: npm install', 'yellow');
  }

  if (!backendOk) {
    log('🌐 Démarrez le backend: npm run dev:local', 'yellow');
  }

  if (!frontendOk) {
    log('🖥️ Démarrez le frontend: npm run dev:frontend', 'yellow');
  }

  if (configOk && nodeModulesOk && backendOk) {
    log('\n🎉 Tout semble prêt! Vous pouvez accéder à l\'application:', 'green');
    log(`   Frontend: ${FRONTEND_URL}`, 'green');
    log(`   Backend API: ${BACKEND_URL}/api`, 'green');
  }
}

// Exécuter les tests
runTests().catch(console.error);
