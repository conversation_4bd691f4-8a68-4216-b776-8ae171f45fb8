@echo off
REM Script pour démarrer l'application en mode développement local sous Windows

echo 🚀 Démarrage de l'application iQraa en mode développement local

REM Vérifier si les scripts JS existent
if exist "scripts\local-dev.js" (
  echo 🔧 Utilisation du script Node.js...
  node scripts/local-dev.js
) else (
  echo ⚠️ Script JS non trouvé, utilisation du mode shell...
  
  echo 💻 Environnement Windows détecté
  
  REM Arrêter les processus existants sur les ports 3000 et 5173 si nécessaire
  echo 🔄 Nettoyage des ports...
  FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :3000') DO TaskKill /PID %%P /F /T >NUL 2>&1
  FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :5173') DO TaskKill /PID %%P /F /T >NUL 2>&1
  
  echo 🌐 Démarrage du serveur backend sur http://localhost:3000
  start /B cmd /c npx cross-env NODE_ENV=development DB_TYPE=sqlite node "node_modules\.bin\tsx.cmd" server/index-sqlite.ts
  
  echo ⏳ Attente du démarrage du backend...
  timeout /t 3 /nobreak > nul
  
  echo 🖥️ Démarrage du serveur frontend sur http://localhost:5173
  start /B cmd /c npx cross-env NODE_ENV=development node "node_modules\.bin\vite.cmd"
  
  echo.
  echo ✅ Serveurs démarrés avec succès
  echo 👉 Accédez à l'application via: http://localhost:5173
  echo 📊 API disponible sur: http://localhost:3000/api
  echo.
  echo ⛔ Appuyez sur Ctrl+C pour arrêter les serveurs
  echo.
  echo Appuyez sur une touche pour arrêter les serveurs...
  pause > nul
  
  echo.
  echo 🛑 Arrêt des serveurs...
  FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :3000') DO TaskKill /PID %%P /F /T >NUL 2>&1
  FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :5173') DO TaskKill /PID %%P /F /T >NUL 2>&1
)