#!/bin/bash

# Script pour tester l'application Electron sans build complet
echo "Préparation de l'environnement de test Electron..."

# Basculer vers la configuration Electron
./switch-deployment.sh electron

# Installation des dépendances avec support explicite pour les modules natifs
echo "Installation des dépendances avec support pour les modules natifs..."
npm install --include=optional

# Installation explicite des dépendances natives de Rollup pour éviter les erreurs
echo "Installation des dépendances natives de Rollup..."
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" || "$OSTYPE" == "cygwin" ]]; then
  # Windows
  npm install @rollup/rollup-win32-x64-msvc --no-save
elif [[ "$OSTYPE" == "darwin"* ]]; then
  # macOS
  npm install @rollup/rollup-darwin-x64 --no-save
else
  # Linux
  npm install @rollup/rollup-linux-x64-gnu --no-save
  # Pour les systèmes Linux alternatifs
  npm install @rollup/rollup-linux-x64-musl --no-save
fi

# Script de build simplifié sans dépendre de Vite
echo "Lancement du script de build personnalisé..."
node electron-build.cjs

# Vérifier si le build s'est bien passé
if [ ! -d "dist" ]; then
  echo "ERREUR: Le dossier 'dist' n'a pas été créé. La compilation a échoué."
  exit 1
fi

echo "Build terminé avec succès! Le dossier 'dist' a été créé."

# Lancer le serveur de développement SQLite dans un processus séparé
echo "Démarrage du serveur SQLite..."
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" || "$OSTYPE" == "cygwin" ]]; then
  # Windows - utiliser CMD directement
  cmd /c "set NODE_ENV=production && node dist/server.cjs" &
else 
  # Unix/Linux/Mac
  NODE_ENV=production node dist/server.cjs &
fi

SERVER_PID=$!

# Attendre que le serveur soit prêt
echo "Attente du démarrage du serveur (5 secondes)..."
sleep 5

# Lancer Electron directement avec la version CommonJS du wrapper
echo "Démarrage d'Electron..."
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" || "$OSTYPE" == "cygwin" ]]; then
  # Windows - utiliser CMD directement
  cmd /c "set NODE_ENV=production && npx electron dist/electron-wrapper.cjs" &
else 
  # Unix/Linux/Mac
  NODE_ENV=production npx electron dist/electron-wrapper.cjs &
fi

ELECTRON_PID=$!

# Fonction de nettoyage
function cleanup() {
  echo "Arrêt des processus..."
  kill $SERVER_PID $ELECTRON_PID 2>/dev/null
  exit 0
}

# Attraper les signaux pour un arrêt propre
trap cleanup SIGINT SIGTERM

echo "Application Electron lancée en mode production."
echo "Appuyez sur Ctrl+C pour terminer."

# Attendre indéfiniment
wait
cleanup