@echo off
setlocal EnableDelayedExpansion

REM Définir les couleurs pour une meilleure lisibilité
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "RESET=[0m"

title iQraa - Test Electron

echo %BLUE%========================================================%RESET%
echo %GREEN%🚀 Préparation et test de l'application iQraa avec Electron%RESET%
echo %BLUE%========================================================%RESET%

REM Vérifier les prérequis
echo %YELLOW%🔍 Vérification des prérequis...%RESET%
where node >nul 2>&1 || (
  echo %RED%❌ Node.js n'est pas installé ou n'est pas dans le PATH.%RESET%
  echo Veuillez installer Node.js depuis https://nodejs.org/
  pause
  exit /b 1
)

REM Basculer vers la configuration Electron
echo %YELLOW%⚙️ Configuration de l'environnement Electron...%RESET%
if exist "switch-deployment.sh" (
  call switch-deployment.sh electron
) else (
  echo %YELLOW%⚠️ Script switch-deployment.sh non trouvé, utilisation de la configuration actuelle%RESET%
  copy /y .npmrc.electron .npmrc 2>nul
)

REM Création du dossier .temp pour les logs
if not exist ".temp" mkdir .temp
set "LOG_FILE=.temp\electron-build-log.txt"
echo %YELLOW%📝 Les logs seront enregistrés dans %LOG_FILE%%RESET%

REM Nettoyer les anciens builds
echo %YELLOW%🧹 Nettoyage de l'environnement...%RESET%
if exist "dist" (
  echo  - Suppression du dossier dist...
  rd /s /q dist
)
if exist "dist-electron" (
  echo  - Suppression du dossier dist-electron...
  rd /s /q dist-electron
)

REM Installation des dépendances avec support explicite pour les modules natifs
echo %YELLOW%📦 Installation des dépendances avec support pour les modules natifs...%RESET%
call npm install --include=optional --no-audit > %LOG_FILE% 2>&1 || (
  echo %RED%❌ Erreur lors de l'installation des dépendances.%RESET%
  echo %YELLOW%📋 Consultez le fichier de log pour plus de détails: %LOG_FILE%%RESET%
  pause
  exit /b 1
)

REM Installation explicite des dépendances natives de Rollup
echo %YELLOW%📦 Installation des dépendances natives de Rollup...%RESET%
call npm install @rollup/rollup-win32-x64-msvc --no-save >> %LOG_FILE% 2>&1 || (
  echo %YELLOW%⚠️ Installation de @rollup/rollup-win32-x64-msvc échouée, mais on continue...%RESET%
)

REM Créer un fichier .env.production optimisé pour Electron (SQLite uniquement)
echo %YELLOW%📄 Création des variables d'environnement pour Electron...%RESET%
echo # Configuration Electron (production)> .env.production
echo NODE_ENV=production>> .env.production
echo # Forcer l'utilisation de SQLite uniquement>> .env.production
echo DB_TYPE=sqlite>> .env.production
echo PG_DISABLED=true>> .env.production
echo SQLITE_PATH=./sqlite.db>> .env.production
echo # Synchronisation (optionnelle, désactivée par défaut)>> .env.production
echo SYNC_ENABLED=false>> .env.production
echo # Copier d'autres variables depuis .env.local si existant
if exist ".env.local" (
  echo %YELLOW%📄 Copie des variables supplémentaires depuis .env.local...%RESET%
  for /f "tokens=*" %%a in ('findstr /v "NODE_ENV DB_TYPE PG_ DATABASE_URL" .env.local') do (
    echo %%a>> .env.production
  )
)

REM Script de build simplifié
echo %YELLOW%🔨 Lancement du script de build...%RESET%
node electron-build.cjs >> %LOG_FILE% 2>&1
if %ERRORLEVEL% NEQ 0 (
  echo %RED%❌ Erreur lors de la construction du build. Code: %ERRORLEVEL%%RESET%
  echo %YELLOW%📋 Consultez le fichier de log pour plus de détails: %LOG_FILE%%RESET%
  pause
  exit /b 1
)

REM Préinstaller les dépendances nécessaires
echo %YELLOW%📦 Installation des dépendances du serveur...%RESET%
node preinstall-electron.cjs >> %LOG_FILE% 2>&1
if %ERRORLEVEL% NEQ 0 (
  echo %RED%⚠️ Attention: Préinstallation des dépendances a échoué, mais on continue...%RESET%
)

REM Vérifier si le build s'est bien passé
if not exist "dist" (
  echo %RED%❌ ERREUR: Le dossier 'dist' n'a pas été créé. La compilation a échoué.%RESET%
  echo %YELLOW%📋 Consultez le fichier de log pour plus de détails: %LOG_FILE%%RESET%
  pause
  exit /b 1
)

echo %GREEN%✅ Build terminé avec succès! Le dossier 'dist' a été créé.%RESET%

REM Arrêter les processus existants si nécessaire
echo %YELLOW%🔄 Nettoyage des processus existants...%RESET%
taskkill /f /im electron.exe /t 2>nul
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :3000') DO (
  echo  - Arrêt du processus sur le port 3000 (PID: %%P)
  TaskKill /PID %%P /F /T >NUL 2>&1
)

REM Lancer le serveur SQLite en mode production
echo %GREEN%🌐 Démarrage du serveur SQLite... %RESET%
start "iQraa Server" /MIN cmd /c "set NODE_ENV=production && node dist/server.cjs 2>.temp\server-error.log"

REM Attendre que le serveur soit prêt
echo %YELLOW%⏳ Attente du démarrage du serveur (5 secondes)...%RESET%
ping -n 6 127.0.0.1 > nul

REM Vérifier si le serveur est bien démarré
set "SERVER_STARTED=0"
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :3000') DO set SERVER_STARTED=1
if %SERVER_STARTED% NEQ 1 (
  echo %RED%⚠️ Le serveur ne semble pas démarré sur le port 3000%RESET%
  echo %YELLOW%📋 Vérifiez le fichier de log: .temp\server-error.log%RESET%
  echo %YELLOW%⏳ Attente supplémentaire (5 secondes)...%RESET%
  ping -n 6 127.0.0.1 > nul
)

REM Lancer Electron directement avec la version CommonJS du wrapper
echo %GREEN%🖥️ Démarrage d'Electron...%RESET%
start "iQraa Electron" cmd /c "set NODE_ENV=production && npx electron dist/electron-wrapper.cjs 2>.temp\electron-error.log"

echo.
echo %GREEN%✅ Application Electron lancée en mode production.%RESET%
echo %GREEN%👉 L'application devrait apparaître dans quelques secondes.%RESET%
echo.
echo %BLUE%========================================================%RESET%
echo %YELLOW%⛔ Fermez cette fenêtre pour terminer tous les processus.%RESET%
echo %BLUE%========================================================%RESET%

REM Attendre que l'utilisateur appuie sur une touche
pause > nul

REM Tuer tous les processus Node.js et Electron
echo.
echo %YELLOW%🛑 Arrêt des processus...%RESET%
taskkill /f /im electron.exe /t 2>nul
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :3000') DO TaskKill /PID %%P /F /T >NUL 2>&1

echo %GREEN%✅ Application arrêtée.%RESET%
timeout /t 2 > nul