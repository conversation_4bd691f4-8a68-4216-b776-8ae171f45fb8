# 🚀 IQRAA Manager - Server Startup Guide

## Issue: Concurrently Not Found ❌

The `npm run dev:network-full` command failed because the `concurrently` package is not installed. Here are multiple working solutions.

## ✅ Solution Options

### Option 1: Install Concurrently (Recommended)
```bash
npm install
# Then run:
npm run dev:network-full
```

### Option 2: Use Batch Script (Windows - Easy)
```bash
start-both-servers.bat
```
This will open two separate command windows - one for backend, one for frontend.

### Option 3: Use PowerShell Script (Windows - Advanced)
```powershell
powershell -ExecutionPolicy Bypass -File start-network-servers.ps1
```

### Option 4: Manual Startup (Two Terminals)
**Terminal 1 - Backend:**
```bash
npm run dev:network
```

**Terminal 2 - Frontend:**
```bash
npm run dev:network-frontend
```

### Option 5: Direct Commands (Two Terminals)
**Terminal 1 - Backend:**
```bash
npx cross-env NODE_ENV=development DB_TYPE=sqlite PG_DISABLED=true HOST=0.0.0.0 CORS_ORIGIN=* SERVER_IP=*********** npx tsx server/index-sqlite.ts
```

**Terminal 2 - Frontend:**
```bash
npx cross-env VITE_API_URL=http://***********:5000 VITE_SERVER_IP=*********** npx vite --config vite.config.network.ts
```

## 🎯 Recommended Startup Sequence

1. **First, try installing dependencies:**
   ```bash
   npm install
   ```

2. **If that works, use:**
   ```bash
   npm run dev:network-full
   ```

3. **If concurrently still doesn't work, use:**
   ```bash
   start-both-servers.bat
   ```

## 🌐 Access Information

Once servers are running:

### URLs:
- **Frontend**: http://***********:5173
- **Backend API**: http://***********:5000/api
- **Health Check**: http://***********:5000/health

### Login Credentials:
- **Admin**: username: `admin`, password: `admin123`
- **Librarian**: username: `librarian`, password: `librarian123`

## 🔍 Verification Steps

1. **Check backend is running:**
   ```bash
   curl http://***********:5000/health
   ```
   Should return: `{"status":"ok",...}`

2. **Check frontend is accessible:**
   Open browser to: http://***********:5173

3. **Test from mobile device:**
   - Connect to same WiFi
   - Open: http://***********:5173

## 🛠️ Troubleshooting

### If "concurrently not found":
- Run `npm install` to install all dependencies
- Use alternative startup methods above

### If ports are in use:
```bash
# Check what's using the ports
netstat -ano | findstr :5000
netstat -ano | findstr :5173

# Kill processes if needed
taskkill /PID <PID_NUMBER> /F
```

### If can't access from other devices:
1. Check Windows Firewall
2. Verify all devices on same network
3. Try health check: http://***********:5000/health

### If backend won't start:
1. Check if SQLite database exists: `sqlite.db`
2. Verify Node.js version: `node --version` (should be 16+)
3. Check environment variables in `.env.local`

## 📁 Files Available

- ✅ `start-both-servers.bat` - Simple batch script
- ✅ `start-network-servers.ps1` - PowerShell script
- ✅ `start-network-fixed.bat` - Enhanced startup script
- ✅ Individual server scripts: `start-simple-server.bat`, `start-simple-frontend.bat`

## 🎯 Quick Start (Choose One)

**Easiest (Windows):**
```bash
start-both-servers.bat
```

**Most Reliable:**
```bash
# Terminal 1
npm run dev:network

# Terminal 2 (new terminal)
npm run dev:network-frontend
```

**After Dependencies Install:**
```bash
npm install
npm run dev:network-full
```

## 📱 Mobile Testing

1. Ensure your mobile device is on the same WiFi network
2. Open mobile browser
3. Go to: http://***********:5173
4. Login with: admin / admin123
5. Test library management features

The application should now be fully accessible across your local network!
