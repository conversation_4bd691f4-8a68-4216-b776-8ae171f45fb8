@echo off
echo ========================================================
echo 🔍 Testing Server Status
echo ========================================================

echo 📋 Checking if servers are running...

echo.
echo 🔍 Checking port 5000 (Backend)...
netstat -an | findstr :5000
if %ERRORLEVEL% EQU 0 (
    echo ✅ Something is running on port 5000
) else (
    echo ❌ Nothing running on port 5000
)

echo.
echo 🔍 Checking port 5173 (Frontend)...
netstat -an | findstr :5173
if %ERRORLEVEL% EQU 0 (
    echo ✅ Something is running on port 5173
) else (
    echo ❌ Nothing running on port 5173
)

echo.
echo 🔍 Checking Node.js processes...
tasklist | findstr node.exe
if %ERRORLEVEL% EQU 0 (
    echo ✅ Node.js processes found
) else (
    echo ❌ No Node.js processes found
)

echo.
echo 🧪 Testing backend health endpoint...
echo Trying to connect to http://***********:5000/health...

REM Try to test the health endpoint using PowerShell
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://***********:5000/health' -TimeoutSec 5; Write-Host '✅ Backend is responding!'; Write-Host 'Response:' $response.Content } catch { Write-Host '❌ Backend not responding:' $_.Exception.Message }"

echo.
echo 🧪 Testing frontend...
echo Frontend should be accessible at: http://***********:5173

echo.
echo ========================================================
echo 📋 Summary:
echo   Backend: http://***********:5000
echo   Frontend: http://***********:5173
echo   Health: http://***********:5000/health
echo.
echo 🚀 To start servers:
echo   npm run dev:network-full
echo   or: start-both-servers.bat
echo ========================================================

pause
