#!/bin/bash

# Couleurs pour la sortie
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================================${NC}"
echo -e "${GREEN}🌐 Démarrage de l'application iQraa sur le réseau local${NC}"
echo -e "${BLUE}========================================================${NC}"

# Détecter l'adresse IP locale
echo -e "${YELLOW}🔍 Détection de l'adresse IP locale...${NC}"

# <PERSON><PERSON>yer différentes méthodes pour détecter l'IP
if command -v ip &> /dev/null; then
    LOCAL_IP=$(ip route get ******* | grep -oP 'src \K\S+' 2>/dev/null)
elif command -v ifconfig &> /dev/null; then
    LOCAL_IP=$(ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -1)
elif command -v hostname &> /dev/null; then
    LOCAL_IP=$(hostname -I | awk '{print $1}' 2>/dev/null)
fi

# Si aucune IP n'est détectée, demander à l'utilisateur
if [ -z "$LOCAL_IP" ] || [ "$LOCAL_IP" = "127.0.0.1" ]; then
    echo -e "${RED}❌ Impossible de détecter l'adresse IP locale automatiquement.${NC}"
    read -p "Veuillez entrer l'adresse IP de ce serveur (ex: *************): " LOCAL_IP
fi

echo -e "${CYAN}📍 Adresse IP détectée: $LOCAL_IP${NC}"

# Vérifier les prérequis
echo -e "${YELLOW}🔍 Vérification des prérequis...${NC}"
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js n'est pas installé ou n'est pas dans le PATH.${NC}"
    echo "Veuillez installer Node.js depuis https://nodejs.org/"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ NPM n'est pas installé ou n'est pas dans le PATH.${NC}"
    exit 1
fi

# Vérifier si les dépendances sont installées
if [ ! -d "node_modules" ]; then
    echo -e "${YELLOW}📦 Installation des dépendances...${NC}"
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ Erreur lors de l'installation des dépendances.${NC}"
        exit 1
    fi
fi

# Nettoyer les processus existants
echo -e "${YELLOW}🔄 Nettoyage des processus existants...${NC}"
pkill -f "tsx server/index-sqlite.ts" 2>/dev/null || true
pkill -f "vite --config vite.config.network.ts" 2>/dev/null || true

# Attendre un peu pour que les processus se terminent
sleep 2

# Créer le fichier de base de données SQLite s'il n'existe pas
if [ ! -f "sqlite.db" ]; then
    echo -e "${YELLOW}🗄️ Initialisation de la base de données SQLite...${NC}"
    touch sqlite.db
fi

# Fonction pour nettoyer les processus à la sortie
cleanup() {
    echo -e "\n${YELLOW}🛑 Arrêt des processus...${NC}"
    pkill -f "tsx server/index-sqlite.ts" 2>/dev/null || true
    pkill -f "vite --config vite.config.network.ts" 2>/dev/null || true
    exit 0
}

# Capturer les signaux pour nettoyer proprement
trap cleanup SIGINT SIGTERM

# Configurer les variables d'environnement pour le réseau
export NODE_ENV=production
export DB_TYPE=sqlite
export PG_DISABLED=true
export SQLITE_PATH=./sqlite.db
export PORT=5000
export HOST=0.0.0.0
export SERVER_IP=$LOCAL_IP
export VITE_API_URL=http://$LOCAL_IP:5000
export VITE_SERVER_IP=$LOCAL_IP
export VITE_FRONTEND_PORT=5173
export VITE_BACKEND_PORT=5000
export CORS_ORIGIN=*
export ALLOW_NETWORK_ACCESS=true

# Démarrer le serveur backend
echo -e "${GREEN}🌐 Démarrage du serveur backend sur http://$LOCAL_IP:5000${NC}"
npx tsx server/index-sqlite.ts &
BACKEND_PID=$!

# Attendre que le backend se lance
echo -e "${YELLOW}⏳ Attente du démarrage du backend (8 secondes)...${NC}"
sleep 8

# Vérifier si le serveur backend est démarré
if ! lsof -i:5000 &> /dev/null; then
    echo -e "${RED}⚠️ Le serveur backend ne semble pas démarré sur le port 5000${NC}"
    echo -e "${YELLOW}⏳ Attente supplémentaire (5 secondes)...${NC}"
    sleep 5
fi

# Démarrer le serveur frontend
echo -e "${GREEN}🖥️ Démarrage du serveur frontend sur http://$LOCAL_IP:5173${NC}"
npx vite --config vite.config.network.ts &
FRONTEND_PID=$!

echo ""
echo -e "${GREEN}✅ Application démarrée avec succès sur le réseau local!${NC}"
echo ""
echo -e "${CYAN}🌐 Accès depuis ce serveur:${NC}"
echo -e "${CYAN}   👉 Frontend: http://localhost:5173${NC}"
echo -e "${CYAN}   👉 Backend API: http://localhost:5000/api${NC}"
echo ""
echo -e "${BLUE}🌐 Accès depuis d'autres appareils du réseau:${NC}"
echo -e "${BLUE}   👉 Frontend: http://$LOCAL_IP:5173${NC}"
echo -e "${BLUE}   👉 Backend API: http://$LOCAL_IP:5000/api${NC}"
echo ""
echo -e "${YELLOW}📱 Pour accéder depuis un téléphone/tablette:${NC}"
echo -e "${YELLOW}   Connectez-vous au même WiFi et ouvrez: http://$LOCAL_IP:5173${NC}"
echo ""
echo -e "${BLUE}========================================================${NC}"
echo -e "${YELLOW}Comptes par défaut:${NC}"
echo -e "${YELLOW}  Admin: admin / admin123${NC}"
echo -e "${YELLOW}  Bibliothécaire: librarian / librarian123${NC}"
echo -e "${BLUE}========================================================${NC}"
echo -e "${RED}⛔ Appuyez sur Ctrl+C pour arrêter l'application.${NC}"
echo -e "${BLUE}========================================================${NC}"

# Attendre que les processus se terminent
wait $BACKEND_PID $FRONTEND_PID
