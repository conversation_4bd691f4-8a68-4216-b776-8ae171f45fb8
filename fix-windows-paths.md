# Guide de résolution des problèmes de chemins sous Windows

## Erreur "Cannot find package 'vite'" ou "runtimeErrorOverlay is not defined"

Si vous rencontrez cette erreur lors du démarrage de l'application, cela est dû à un problème spécifique à Windows avec les modules Replit qui ne sont pas compatibles avec les environnements locaux Windows.

### Solution 1: Utiliser le script tout-en-un (Recommandé)

Exécutez simplement :

```cmd
start-windows.bat
```

Ce script va automatiquement :
1. Installer les dépendances manquantes
2. Créer les correctifs nécessaires pour contourner les erreurs des plugins Replit
3. Démarrer l'application avec une configuration Vite simplifiée

### Solution 2: Réparer manuellement les dépendances

Si vous préférez résoudre le problème manuellement :

```cmd
fix-windows-dependencies.bat
```

Puis démarrez l'application :

```cmd
start-windows.bat
```

### Solution 3: Méthode manuelle en deux étapes

Si les solutions précédentes ne fonctionnent pas, ouvrez deux fenêtres de commande distinctes :

**Terminal 1 (Backend) :**
```cmd
set NODE_PATH=%TEMP%\iqraa-fix && npx cross-env NODE_ENV=development tsx server/index.ts
```

**Terminal 2 (Frontend) :**
```cmd
set NODE_PATH=%TEMP%\iqraa-fix && npx vite --config vite-local.config.ts
```

## Comment fonctionne la solution

La solution utilise plusieurs techniques pour contourner les problèmes :

1. **Configuration Vite alternative** : Nous utilisons `vite-local.config.ts` qui est une version simplifiée de la configuration originale sans les plugins Replit qui causent les erreurs.

2. **Modules fictifs** : Nous créons des modules JavaScript fictifs dans un dossier temporaire qui sont utilisés lorsque les modules Replit sont importés.

3. **Variable d'environnement NODE_PATH** : Nous configurons Node.js pour rechercher les modules dans notre dossier de correctifs en premier, avant de chercher dans les node_modules standards.

## Résolution d'autres problèmes courants sous Windows

### Erreur de module non trouvé (@rollup/rollup-win32-x64-msvc)

Cette erreur est résolue par le script `fix-windows-dependencies.bat` qui installe explicitement ce module.

### Erreur de chemin avec des espaces

Les scripts améliorés utilisent des guillemets autour des chemins et des techniques d'échappement appropriées pour gérer les chemins contenant des espaces.

### Problèmes de permissions

Certains scripts peuvent nécessiter des droits d'administrateur pour s'exécuter correctement, surtout ceux qui modifient le système de fichiers. Essayez d'exécuter l'invite de commande en tant qu'administrateur si vous rencontrez des problèmes de permission.