// Minimal server test to isolate the issue
const express = require('express');
const cors = require('cors');

console.log('🔍 Testing minimal server startup...');

const app = express();

// Basic CORS
app.use(cors({
  origin: '*',
  credentials: true
}));

app.use(express.json());

// Simple health check
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    message: 'Minimal server is working'
  });
});

// Simple API test
app.get('/api/test', (req, res) => {
  res.json({
    message: 'API is working',
    timestamp: new Date().toISOString()
  });
});

const port = 5000;
const host = '0.0.0.0';

console.log(`🚀 Starting minimal server on ${host}:${port}...`);

const server = app.listen(port, host, () => {
  console.log(`✅ Minimal server running on http://***********:${port}`);
  console.log(`📊 Health check: http://***********:${port}/health`);
  console.log(`🧪 API test: http://***********:${port}/api/test`);
  console.log('');
  console.log('🌐 Server is accessible from network');
  console.log('Press Ctrl+C to stop');
});

server.on('error', (error) => {
  console.error('❌ Server error:', error);
  if (error.code === 'EADDRINUSE') {
    console.error(`Port ${port} is already in use. Please stop other servers first.`);
  }
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  server.close(() => {
    console.log('✅ Server stopped');
    process.exit(0);
  });
});
