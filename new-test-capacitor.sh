#!/bin/bash

# Script pour tester l'application mobile sans build complet
echo "Préparation de l'environnement de test Capacitor pour Android..."

# Basculer vers la configuration Capacitor
./switch-deployment.sh capacitor

# Installation des dépendances
echo "Installation des dépendances..."
npm install

# Lancer le script de développement Capacitor
echo "Démarrage du mode développement Capacitor..."

# Lancer directement avec npx en fonction du système d'exploitation
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" || "$OSTYPE" == "cygwin" ]]; then
  # Windows - utiliser CMD directement
  echo "Vérification que new-capacitor-dev.js existe..."
  if [ -f "./new-capacitor-dev.js" ]; then
    cmd /c "npx cross-env NODE_ENV=development npx tsx new-capacitor-dev.js" &
  else
    echo "⚠️ Fichier new-capacitor-dev.js non trouvé, utilisation de capacitor-dev.js standard"
    cmd /c "npx cross-env NODE_ENV=development npx tsx scripts/capacitor-dev.js" &
  fi
else 
  # Unix/Linux/Mac
  if [ -f "./new-capacitor-dev.js" ]; then
    npx cross-env NODE_ENV=development npx tsx new-capacitor-dev.js &
  else
    npx cross-env NODE_ENV=development npx tsx scripts/capacitor-dev.js &
  fi
fi

DEV_PID=$!

# Fonction de nettoyage
function cleanup() {
  echo "Arrêt des processus..."
  kill $DEV_PID 2>/dev/null
  exit 0
}

# Attraper les signaux pour un arrêt propre
trap cleanup SIGINT SIGTERM

echo "Application mobile lancée en mode test."
echo "L'application Android sera disponible via Capacitor."
echo "Appuyez sur Ctrl+C pour terminer."

# Attendre indéfiniment
wait $DEV_PID
cleanup