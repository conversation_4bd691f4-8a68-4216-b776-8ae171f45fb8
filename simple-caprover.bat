@echo off
REM Script ultra-simple pour CapRover

echo ==================================
echo CapRover tarball (ultra-simple)
echo ==================================

REM Creer un repertoire temporaire
set TEMP_DIR=caprover_build
mkdir %TEMP_DIR% 2>nul

echo Preparation des fichiers pour l'archive...

REM Copier tous les fichiers necessaires
xcopy client %TEMP_DIR%\client\ /E /I /Y
xcopy server %TEMP_DIR%\server\ /E /I /Y
xcopy shared %TEMP_DIR%\shared\ /E /I /Y
xcopy drizzle %TEMP_DIR%\drizzle\ /E /I /Y
if exist public xcopy public %TEMP_DIR%\public\ /E /I /Y

REM Creer un package.json minimal
echo { > %TEMP_DIR%\package.json
echo   "name": "iqraa-app", >> %TEMP_DIR%\package.json
echo   "version": "1.0.0", >> %TEMP_DIR%\package.json
echo   "scripts": { >> %TEMP_DIR%\package.json
echo     "build": "vite build", >> %TEMP_DIR%\package.json
echo     "start": "node dist/server/index.js" >> %TEMP_DIR%\package.json
echo   }, >> %TEMP_DIR%\package.json
echo   "dependencies": { >> %TEMP_DIR%\package.json
echo     "express": "^4.21.2", >> %TEMP_DIR%\package.json
echo     "drizzle-orm": "^0.39.3", >> %TEMP_DIR%\package.json
echo     "@neondatabase/serverless": "^0.9.0" >> %TEMP_DIR%\package.json
echo   } >> %TEMP_DIR%\package.json
echo } >> %TEMP_DIR%\package.json

REM Creer un Dockerfile simple
echo FROM node:20-slim > %TEMP_DIR%\Dockerfile
echo WORKDIR /app >> %TEMP_DIR%\Dockerfile
echo COPY package.json ./ >> %TEMP_DIR%\Dockerfile
echo RUN npm install >> %TEMP_DIR%\Dockerfile
echo COPY . . >> %TEMP_DIR%\Dockerfile
echo ENV NODE_ENV production >> %TEMP_DIR%\Dockerfile
echo ENV PORT 80 >> %TEMP_DIR%\Dockerfile
echo EXPOSE 80 >> %TEMP_DIR%\Dockerfile
echo CMD ["npm", "start"] >> %TEMP_DIR%\Dockerfile

REM Creer un fichier captain-definition
echo { > %TEMP_DIR%\captain-definition
echo   "schemaVersion": 2, >> %TEMP_DIR%\captain-definition
echo   "dockerfilePath": "./Dockerfile" >> %TEMP_DIR%\captain-definition
echo } >> %TEMP_DIR%\captain-definition

REM Creer un fichier .env.production
echo NODE_ENV=production > %TEMP_DIR%\.env.production
echo DATABASE_URL=postgres://user:password@localhost:5432/iqraa >> %TEMP_DIR%\.env.production
echo PORT=80 >> %TEMP_DIR%\.env.production

REM Creer un fichier tsconfig.json minimal
echo { > %TEMP_DIR%\tsconfig.json
echo   "compilerOptions": { >> %TEMP_DIR%\tsconfig.json
echo     "target": "ES2020", >> %TEMP_DIR%\tsconfig.json
echo     "module": "NodeNext", >> %TEMP_DIR%\tsconfig.json
echo     "moduleResolution": "NodeNext", >> %TEMP_DIR%\tsconfig.json
echo     "esModuleInterop": true >> %TEMP_DIR%\tsconfig.json
echo   } >> %TEMP_DIR%\tsconfig.json
echo } >> %TEMP_DIR%\tsconfig.json

REM Creer un fichier vite.config.ts minimal
echo import { defineConfig } from 'vite'; > %TEMP_DIR%\vite.config.ts
echo export default defineConfig({ >> %TEMP_DIR%\vite.config.ts
echo   build: { >> %TEMP_DIR%\vite.config.ts
echo     outDir: 'dist' >> %TEMP_DIR%\vite.config.ts
echo   } >> %TEMP_DIR%\vite.config.ts
echo }); >> %TEMP_DIR%\vite.config.ts

REM Creer un zip (PowerShell est toujours disponible sur Windows 10+)
echo Creation de l'archive ZIP...
powershell -Command "Compress-Archive -Path '%TEMP_DIR%\*' -DestinationPath 'iqraa-caprover.zip' -Force"

echo.
echo Archive ZIP creee: iqraa-caprover.zip
echo IMPORTANT: Pour CapRover, renommez ce fichier en .tar.gz avant de le telecharger
echo.

REM Supprimer le repertoire temporaire
rmdir /S /Q %TEMP_DIR%