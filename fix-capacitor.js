#!/usr/bin/env node
/**
 * Script pour réparer la configuration Capacitor sous Windows
 * Problème principal: 'apacitor-dev.js"' n'est pas reconnu (erreur de typographie)
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 Réparation de la configuration Capacitor pour Windows...');

// Vérifier si new-capacitor-dev.js existe
if (!fs.existsSync('./new-capacitor-dev.js')) {
  console.log('⚠️ Le fichier new-capacitor-dev.js n\'existe pas, le téléchargement est nécessaire.');
  console.log('Création du fichier à partir du modèle...');
  
  // Contenu du fichier new-capacitor-dev.js
  const newCapacitorDevContent = `#!/usr/bin/env node
// Script pour lancer l'application en mode développement Capacitor
// Version améliorée avec support de chemins contenant des espaces sous Windows

import { spawn } from 'child_process';
import path from 'path';
import os from 'os';
import fs from 'fs';

// Déterminer si l'on est sous Windows
const isWindows = process.platform === 'win32';

// Configuration de l'environnement
const env = {
  ...process.env,
  NODE_ENV: 'development',
  DB_TYPE: 'sqlite'
};

console.log('🚀 Démarrage du développement Capacitor...');

// Démarrer le serveur backend (SQLite)
console.log('📦 Démarrage du serveur backend (SQLite)...');

// Différentes méthodes de lancement selon le système d'exploitation
let serverProcess;
if (isWindows) {
  // Utiliser cmd /c directement pour éviter les problèmes avec les espaces
  serverProcess = spawn('cmd', ['/c', 'npx', 'cross-env', 'NODE_ENV=development', 'DB_TYPE=sqlite',
    'npx', 'tsx', 'server/index-sqlite.ts'], {
    stdio: 'inherit',
    env,
    shell: true
  });
} else {
  // Sous Unix/Linux/Mac
  serverProcess = spawn('npx', ['cross-env', 'NODE_ENV=development', 'DB_TYPE=sqlite', 
    'tsx', 'server/index-sqlite.ts'], {
    stdio: 'inherit',
    env
  });
}

// Démarrer le serveur de développement pour l'interface utilisateur
console.log('🖥️ Démarrage du serveur frontend...');

// Différentes méthodes de lancement selon le système d'exploitation
let frontendProcess;
if (isWindows) {
  // Utiliser cmd /c directement pour éviter les problèmes avec les espaces
  frontendProcess = spawn('cmd', ['/c', 'npx', 'cross-env', 'NODE_ENV=development',
    'npx', 'vite', '--port', '5173'], {
    stdio: 'inherit',
    env,
    shell: true
  });
} else {
  // Sous Unix/Linux/Mac
  frontendProcess = spawn('npx', ['cross-env', 'NODE_ENV=development', 
    'vite', '--port', '5173'], {
    stdio: 'inherit',
    env
  });
}

// Afficher les URLs locales
setTimeout(() => {
  console.log('');
  console.log('📱 Application prête pour les tests mobiles');
  console.log('');
  console.log('🌐 URL pour le navigateur: http://localhost:5173');
  console.log('🌐 URL pour l\'API: http://localhost:3000/api');
  console.log('');
  console.log('👉 Pour arrêter les serveurs, appuyez sur Ctrl+C');
}, 3000);

// Gérer les signaux pour arrêter proprement
const cleanup = () => {
  console.log('\\n🛑 Arrêt des processus...');
  serverProcess.kill();
  frontendProcess.kill();
  process.exit(0);
};

process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);`;

  // Écrire le fichier
  fs.writeFileSync('./new-capacitor-dev.js', newCapacitorDevContent);
  console.log('✅ Fichier new-capacitor-dev.js créé avec succès');
} else {
  console.log('✅ Fichier new-capacitor-dev.js déjà existant');
}

// Corriger le script new-test-capacitor.sh si nécessaire
if (fs.existsSync('./new-test-capacitor.sh')) {
  console.log('🔍 Vérification et correction de new-test-capacitor.sh...');
  
  let content = fs.readFileSync('./new-test-capacitor.sh', 'utf8');
  
  // Corriger l'erreur "apacitor-dev.js"
  if (content.includes('apacitor-dev.js')) {
    content = content.replace('apacitor-dev.js', 'new-capacitor-dev.js');
    fs.writeFileSync('./new-test-capacitor.sh', content);
    console.log('✅ Correction de l\'erreur de typo dans new-test-capacitor.sh');
  }
}

// Installation des dépendances nécessaires
console.log('📦 Vérification et installation des dépendances nécessaires...');

try {
  console.log('Vérification de cross-env...');
  execSync('npx cross-env --version', { stdio: 'ignore' });
  console.log('✅ cross-env est installé');
} catch (error) {
  console.log('⚠️ cross-env n\'est pas installé, installation en cours...');
  execSync('npm install --save-dev cross-env', { stdio: 'inherit' });
}

try {
  console.log('Vérification de tsx...');
  execSync('npx tsx --version', { stdio: 'ignore' });
  console.log('✅ tsx est installé');
} catch (error) {
  console.log('⚠️ tsx n\'est pas installé, installation en cours...');
  execSync('npm install --save-dev tsx', { stdio: 'inherit' });
}

console.log('🔧 Préparation des permissions pour le script...');
try {
  execSync('chmod +x ./new-capacitor-dev.js', { stdio: 'ignore' });
  execSync('chmod +x ./new-test-capacitor.sh', { stdio: 'ignore' });
  console.log('✅ Permissions ajoutées aux scripts');
} catch (error) {
  console.log('⚠️ Note: Sous Windows, l\'ajout de permissions n\'est pas toujours nécessaire');
}

console.log('');
console.log('🎉 Configuration Capacitor réparée avec succès');
console.log('👉 Vous pouvez maintenant exécuter ./new-test-capacitor.sh');