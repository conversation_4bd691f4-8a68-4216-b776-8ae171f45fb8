#!/usr/bin/env node
// Script pour lancer l'application en mode développement Capacitor
// Version améliorée avec support de chemins contenant des espaces sous Windows

import { spawn } from 'child_process';
import path from 'path';
import os from 'os';
import fs from 'fs';

// Déterminer si l'on est sous Windows
const isWindows = process.platform === 'win32';

// Configuration de l'environnement
const env = {
  ...process.env,
  NODE_ENV: 'development',
  DB_TYPE: 'sqlite'
};

console.log('🚀 Démarrage du développement Capacitor...');

// Démarrer le serveur backend (SQLite)
console.log('📦 Démarrage du serveur backend (SQLite)...');

// Différentes méthodes de lancement selon le système d'exploitation
let serverProcess;
if (isWindows) {
  // Utiliser cmd /c directement pour éviter les problèmes avec les espaces
  serverProcess = spawn('cmd', ['/c', 'npx', 'cross-env', 'NODE_ENV=development', 'DB_TYPE=sqlite',
    'npx', 'tsx', 'server/index-sqlite.ts'], {
    stdio: 'inherit',
    env,
    shell: true
  });
} else {
  // Sous Unix/Linux/Mac
  serverProcess = spawn('npx', ['cross-env', 'NODE_ENV=development', 'DB_TYPE=sqlite', 
    'tsx', 'server/index-sqlite.ts'], {
    stdio: 'inherit',
    env
  });
}

// Démarrer le serveur de développement pour l'interface utilisateur
console.log('🖥️ Démarrage du serveur frontend...');

// Différentes méthodes de lancement selon le système d'exploitation
let frontendProcess;
if (isWindows) {
  // Utiliser cmd /c directement pour éviter les problèmes avec les espaces
  frontendProcess = spawn('cmd', ['/c', 'npx', 'cross-env', 'NODE_ENV=development',
    'npx', 'vite', '--port', '5173'], {
    stdio: 'inherit',
    env,
    shell: true
  });
} else {
  // Sous Unix/Linux/Mac
  frontendProcess = spawn('npx', ['cross-env', 'NODE_ENV=development', 
    'vite', '--port', '5173'], {
    stdio: 'inherit',
    env
  });
}

// Obtenir l'adresse IP locale pour l'accès depuis l'émulateur ou un appareil physique
import { getLocalIpAddress } from './scripts/capacitor/network-utils.js';
const localIp = getLocalIpAddress();

// Afficher l'URL pour l'émulateur Android
setTimeout(() => {
  console.log('');
  console.log('📱 Application prête pour les tests mobiles');
  console.log('');
  console.log('🌐 URL pour l\'émulateur Android: http://' + localIp + ':5173');
  console.log('🌐 URL pour le navigateur: http://localhost:5173');
  console.log('');
  console.log('⚠️ Note: Pour tester sur un émulateur ou appareil Android:');
  console.log('1. Assurez-vous que l\'émulateur/appareil et votre ordinateur sont sur le même réseau');
  console.log('2. Utilisez l\'URL avec l\'adresse IP (pas localhost) dans l\'application mobile');
  console.log('');
  console.log('👉 Pour arrêter les serveurs, appuyez sur Ctrl+C');
}, 3000);

// Gérer les signaux pour arrêter proprement
const cleanup = () => {
  console.log('\n🛑 Arrêt des processus...');
  serverProcess.kill();
  frontendProcess.kill();
  process.exit(0);
};

process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);