@echo off
REM Script pour creer une archive tar.gz pour CapRover avec structure correcte

echo ==================================
echo Creation d'un tarball CapRover avec structure correcte
echo ==================================

REM Obtenir la date pour le nom du fichier
set TIMESTAMP=%date:~-4,4%%date:~-7,2%%date:~-10,2%
set TARBALL_NAME=iqraa-caprover-%TIMESTAMP%.tar

REM Creer un repertoire temporaire
set TEMP_DIR=caprover_build
mkdir %TEMP_DIR% 2>nul

echo Preparation des fichiers pour l'archive...

REM Copier les fichiers SANS le prefixe deployment/caprover/
REM Les fichiers doivent etre a la racine de l'archive
xcopy client %TEMP_DIR%\client\ /E /I /Y
xcopy server %TEMP_DIR%\server\ /E /I /Y
xcopy shared %TEMP_DIR%\shared\ /E /I /Y
xcopy drizzle %TEMP_DIR%\drizzle\ /E /I /Y
if exist public xcopy public %TEMP_DIR%\public\ /E /I /Y

REM Copier les fichiers de configuration DIRECTEMENT a la racine
copy deployment\caprover\package.json %TEMP_DIR%\package.json
copy captain-definition %TEMP_DIR%\
copy deployment\caprover\Dockerfile %TEMP_DIR%\Dockerfile
copy tsconfig.json %TEMP_DIR%\
copy drizzle.config.ts %TEMP_DIR%\
copy vite.config.ts %TEMP_DIR%\

REM Creer un fichier .env.production minimal
echo NODE_ENV=production > %TEMP_DIR%\.env.production
echo DATABASE_URL=postgres://user:password@localhost:5432/iqraa >> %TEMP_DIR%\.env.production
echo VITE_API_URL=http://localhost:5000 >> %TEMP_DIR%\.env.production

REM Creer un Dockerfile simplifie qui n'a pas besoin de chercher dans deployment/caprover/
echo FROM node:20-slim AS base > %TEMP_DIR%\Dockerfile
echo. >> %TEMP_DIR%\Dockerfile
echo FROM base AS deps >> %TEMP_DIR%\Dockerfile
echo WORKDIR /app >> %TEMP_DIR%\Dockerfile
echo. >> %TEMP_DIR%\Dockerfile
echo RUN apt-get update ^&^& apt-get install -y --no-install-recommends \>> %TEMP_DIR%\Dockerfile
echo     build-essential \>> %TEMP_DIR%\Dockerfile
echo     python3 \>> %TEMP_DIR%\Dockerfile
echo     gcc \>> %TEMP_DIR%\Dockerfile
echo     g++ \>> %TEMP_DIR%\Dockerfile
echo     make \>> %TEMP_DIR%\Dockerfile
echo     postgresql-client \>> %TEMP_DIR%\Dockerfile
echo     libpq-dev \>> %TEMP_DIR%\Dockerfile
echo     ^&^& rm -rf /var/lib/apt/lists/* >> %TEMP_DIR%\Dockerfile
echo. >> %TEMP_DIR%\Dockerfile
echo COPY package.json ./ >> %TEMP_DIR%\Dockerfile
echo RUN npm install >> %TEMP_DIR%\Dockerfile
echo. >> %TEMP_DIR%\Dockerfile
echo FROM deps AS builder >> %TEMP_DIR%\Dockerfile
echo WORKDIR /app >> %TEMP_DIR%\Dockerfile
echo. >> %TEMP_DIR%\Dockerfile
echo COPY . . >> %TEMP_DIR%\Dockerfile
echo COPY .env.production .env >> %TEMP_DIR%\Dockerfile
echo. >> %TEMP_DIR%\Dockerfile
echo ENV NODE_ENV production >> %TEMP_DIR%\Dockerfile
echo RUN npm run build >> %TEMP_DIR%\Dockerfile
echo. >> %TEMP_DIR%\Dockerfile
echo FROM base AS runner >> %TEMP_DIR%\Dockerfile
echo WORKDIR /app >> %TEMP_DIR%\Dockerfile
echo. >> %TEMP_DIR%\Dockerfile
echo ENV NODE_ENV production >> %TEMP_DIR%\Dockerfile
echo. >> %TEMP_DIR%\Dockerfile
echo COPY --from=builder /app/dist ./dist >> %TEMP_DIR%\Dockerfile
echo COPY --from=builder /app/server ./server >> %TEMP_DIR%\Dockerfile
echo COPY --from=builder /app/drizzle ./drizzle >> %TEMP_DIR%\Dockerfile
echo COPY --from=builder /app/shared ./shared >> %TEMP_DIR%\Dockerfile
echo COPY --from=builder /app/package.json . >> %TEMP_DIR%\Dockerfile
echo COPY --from=builder /app/node_modules ./node_modules >> %TEMP_DIR%\Dockerfile
echo COPY --from=builder /app/.env ./ >> %TEMP_DIR%\Dockerfile
echo. >> %TEMP_DIR%\Dockerfile
echo CMD ["node", "dist/server/index.js"] >> %TEMP_DIR%\Dockerfile

REM S'assurer que captain-definition est correct
echo { > %TEMP_DIR%\captain-definition
echo   "schemaVersion": 2, >> %TEMP_DIR%\captain-definition
echo   "dockerfilePath": "./Dockerfile" >> %TEMP_DIR%\captain-definition
echo } >> %TEMP_DIR%\captain-definition

REM Creer l'archive tar avec tar.exe natif de Windows 10
where tar >nul 2>nul
if %ERRORLEVEL% EQU 0 (
  echo Utilisation de tar.exe pour creer l'archive...
  tar -cf %TARBALL_NAME% -C %TEMP_DIR% .
  
  REM Si gzip est disponible, utiliser gzip pour le compresser
  where gzip >nul 2>nul
  if %ERRORLEVEL% EQU 0 (
    echo Compression avec gzip...
    gzip -f %TARBALL_NAME%
    echo Archive TAR.GZ creee: %TARBALL_NAME%.gz
  ) else (
    echo Gzip non disponible, utilisation de PowerShell pour compresser...
    powershell -Command "Compress-Archive -Path '%TEMP_DIR%\*' -DestinationPath 'iqraa-caprover-%TIMESTAMP%.zip' -Force"
    echo ATTENTION: Archive ZIP creee au lieu de TAR.GZ: iqraa-caprover-%TIMESTAMP%.zip
    echo Pour CapRover, veuillez la renommer en .tar.gz avant de la telecharger
  )
) else (
  echo Tar non disponible, utilisation de PowerShell pour creer un ZIP...
  powershell -Command "Compress-Archive -Path '%TEMP_DIR%\*' -DestinationPath 'iqraa-caprover-%TIMESTAMP%.zip' -Force"
  echo ATTENTION: Archive ZIP creee au lieu de TAR.GZ: iqraa-caprover-%TIMESTAMP%.zip
  echo Pour CapRover, veuillez la renommer en .tar.gz avant de la telecharger
)

echo.
echo Archive creee avec succes!
echo Vous pouvez maintenant telecharger cette archive dans votre panneau CapRover.
echo Structure des fichiers corrigee pour le Dockerfile.
echo.

REM Supprimer le repertoire temporaire
rmdir /S /Q %TEMP_DIR%