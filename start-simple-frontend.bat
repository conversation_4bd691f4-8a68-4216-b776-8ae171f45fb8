@echo off
echo Starting IQRAA Manager Frontend...

REM Set environment variables
set VITE_API_URL=http://***********:5000
set VITE_SERVER_IP=***********
set VITE_FRONTEND_PORT=5173
set VITE_BACKEND_PORT=5000
set VITE_DB_TYPE=sqlite
set VITE_PG_DISABLED=true
set VITE_SYNC_ENABLED=false
set VITE_IS_OFFLINE_FIRST=true
set VITE_DEFAULT_LANGUAGE=fr
set VITE_NETWORK_MODE=true

echo Environment configured:
echo   VITE_API_URL: %VITE_API_URL%
echo   VITE_SERVER_IP: %VITE_SERVER_IP%
echo   VITE_FRONTEND_PORT: %VITE_FRONTEND_PORT%

echo.
echo Starting frontend server...
npx vite --config vite.config.network.ts

pause
