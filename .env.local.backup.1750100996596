# Configuration pour le serveur réseau local
# Ce fichier configure l'application pour fonctionner sur un réseau local

# Environment
NODE_ENV=development

# Database Configuration
DB_TYPE=sqlite
SQLITE_PATH=./sqlite.db
PG_DISABLED=true

# Server Configuration - Network accessible
PORT=5000
HOST=0.0.0.0
# Replace with your actual server IP for production
SERVER_IP=***********

# API Configuration - Use server IP for network access
API_URL=http://***********:5000
VITE_API_URL=http://***********:5000

# Synchronization Configuration (disabled for local network)
SYNC_ENABLED=false
VITE_SYNC_ENABLED=false
SYNC_API_URL=http://***********:5000/api/sync
SYNC_INTERVAL=300000

# Client Configuration
VITE_DB_TYPE=sqlite
VITE_PG_DISABLED=true
VITE_IS_OFFLINE_FIRST=true
VITE_DEFAULT_LANGUAGE=fr

# Network Configuration
VITE_SERVER_IP=***********
VITE_FRONTEND_PORT=5173
VITE_BACKEND_PORT=5000

# Session Configuration
SESSION_SECRET=iqraa-local-network-secret-key

# Development flags
DEBUG=true
VERBOSE_LOGGING=true

# Network Security
CORS_ORIGIN=*
ALLOW_NETWORK_ACCESS=true