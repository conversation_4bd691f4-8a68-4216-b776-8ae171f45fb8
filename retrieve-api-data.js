const fetch = require('node-fetch');

// Fonction pour récupérer les données des genres
async function getGenres() {
  try {
    const response = await fetch('http://localhost:5000/api/genres');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    console.log('Genres:', JSON.stringify(data, null, 2));
    return data;
  } catch (error) {
    console.error('Error fetching genres:', error);
  }
}

// Fonction pour récupérer les données des langues
async function getLanguages() {
  try {
    const response = await fetch('http://localhost:5000/api/languages');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const data = await response.json();
    console.log('Languages:', JSON.stringify(data, null, 2));
    return data;
  } catch (error) {
    console.error('Error fetching languages:', error);
  }
}

// Exécuter les fonctions
async function main() {
  await getGenres();
  await getLanguages();
}

main().catch(console.error);