import fs from 'fs';
import os from 'os';

/**
 * Simple network setup script
 */

// Get local IP address
function getLocalIP() {
  const interfaces = os.networkInterfaces();
  
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      if (iface.family === 'IPv4' && !iface.internal) {
        return iface.address;
      }
    }
  }
  
  return '*************'; // fallback
}

// Create .env.local file
function createEnvFile(serverIP) {
  const envContent = `# Configuration pour le serveur réseau local
# Ce fichier configure l'application pour fonctionner sur un réseau local

# Environment
NODE_ENV=development

# Database Configuration
DB_TYPE=sqlite
SQLITE_PATH=./sqlite.db
PG_DISABLED=true

# Server Configuration - Network accessible
PORT=5000
HOST=0.0.0.0
# Replace with your actual server IP for production
SERVER_IP=${serverIP}

# API Configuration - Use server IP for network access
API_URL=http://${serverIP}:5000
VITE_API_URL=http://${serverIP}:5000

# Synchronization Configuration (disabled for local network)
SYNC_ENABLED=false
VITE_SYNC_ENABLED=false
SYNC_API_URL=http://${serverIP}:5000/api/sync
SYNC_INTERVAL=300000

# Client Configuration
VITE_DB_TYPE=sqlite
VITE_PG_DISABLED=true
VITE_IS_OFFLINE_FIRST=true
VITE_DEFAULT_LANGUAGE=fr

# Network Configuration
VITE_SERVER_IP=${serverIP}
VITE_FRONTEND_PORT=5173
VITE_BACKEND_PORT=5000

# Session Configuration
SESSION_SECRET=iqraa-local-network-secret-key

# Development flags
DEBUG=true
VERBOSE_LOGGING=true

# Network Security
CORS_ORIGIN=*
ALLOW_NETWORK_ACCESS=true
`;

  fs.writeFileSync('.env.local', envContent);
  return serverIP;
}

// Main function
function main() {
  console.log('🌐 Setting up network configuration...');
  
  const serverIP = getLocalIP();
  console.log(`📍 Detected IP: ${serverIP}`);
  
  // Backup existing .env.local if it exists
  if (fs.existsSync('.env.local')) {
    const backup = `.env.local.backup.${Date.now()}`;
    fs.copyFileSync('.env.local', backup);
    console.log(`💾 Backed up existing .env.local to: ${backup}`);
  }
  
  createEnvFile(serverIP);
  console.log('✅ Created .env.local with network configuration');
  
  console.log('');
  console.log('🚀 To start the application:');
  console.log('   npm run dev:network-full');
  console.log('   or use: start-network-fixed.bat');
  console.log('');
  console.log('🌐 Access URLs:');
  console.log(`   Frontend: http://${serverIP}:5173`);
  console.log(`   Backend: http://${serverIP}:5000`);
  console.log('');
  console.log('👤 Default login: admin / admin123');
}

main();
