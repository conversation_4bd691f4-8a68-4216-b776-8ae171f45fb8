# 🔧 Backend Server Startup Fix

## Problem Analysis ❌

The error shows:
- Frontend is running (process `[1]`)
- Backend is NOT running (ECONNREFUSED errors)
- Frontend trying to connect to `::1:5000` and `127.0.0.1:5000`
- This means the backend server failed to start

## Root Cause 🔍

The backend server is not starting properly. This could be due to:
1. TypeScript compilation issues
2. Missing dependencies
3. Database initialization problems
4. Port conflicts
5. Environment variable issues

## Step-by-Step Solution ✅

### Step 1: Stop All Processes
```bash
# Kill any existing processes
taskkill /f /im node.exe
# Or use Ctrl+C in all terminal windows
```

### Step 2: Install Dependencies
```bash
npm install
```

### Step 3: Test Minimal Server First
```bash
node test-minimal-server.js
```
This should show:
```
✅ Minimal server running on http://***********:5000
```

### Step 4: If Minimal Server Works, Test Backend
```bash
# Method A: Direct command
npx cross-env NODE_ENV=development DB_TYPE=sqlite PG_DISABLED=true HOST=0.0.0.0 CORS_ORIGIN=* npx tsx server/index-sqlite.ts

# Method B: NPM script
npm run dev:network
```

### Step 5: Check for Errors
Look for these common errors:
- `Cannot find module` → Run `npm install`
- `Port 5000 already in use` → Kill existing processes
- `Database error` → Check if `sqlite.db` exists
- `TypeScript errors` → Check syntax in server files

## Quick Fix Commands 🚀

### Option 1: Use Batch Script (Recommended)
```bash
test-backend-only.bat
```

### Option 2: Manual Backend Start
```bash
# Set environment variables
set NODE_ENV=development
set DB_TYPE=sqlite
set PG_DISABLED=true
set HOST=0.0.0.0
set PORT=5000
set SERVER_IP=***********
set CORS_ORIGIN=*

# Start server
npx tsx server/index-sqlite.ts
```

### Option 3: Use PowerShell
```powershell
$env:NODE_ENV="development"
$env:DB_TYPE="sqlite"
$env:PG_DISABLED="true"
$env:HOST="0.0.0.0"
$env:PORT="5000"
$env:SERVER_IP="***********"
$env:CORS_ORIGIN="*"

npx tsx server/index-sqlite.ts
```

## Expected Success Output 📊

When backend starts successfully, you should see:
```
Base de données SQLite initialisée avec succès
Métadonnées de synchronisation initialisées avec succès
🚀 iQraa Server running on http://***********:5000
📊 API endpoints available at http://***********:5000/api
🗄️ Database: SQLite (sqlite.db)
🔄 Sync: Disabled
🌐 Network access enabled - Server accessible from:
   - Local: http://localhost:5000
   - Network: http://***********:5000
   - Frontend: http://***********:5173
```

## Verification Steps 🧪

### 1. Test Health Endpoint
```bash
curl http://***********:5000/health
```
Should return:
```json
{
  "status": "ok",
  "timestamp": "...",
  "environment": "development",
  "database": "sqlite",
  "version": "1.0.0"
}
```

### 2. Test API Endpoint
```bash
curl http://***********:5000/api/stats
```

### 3. Check Process
```bash
netstat -an | findstr :5000
```
Should show: `TCP 0.0.0.0:5000 ... LISTENING`

## Troubleshooting Common Issues 🛠️

### Issue: "Cannot find module 'tsx'"
**Solution:**
```bash
npm install -g tsx
# Or use npx: npx tsx server/index-sqlite.ts
```

### Issue: "Port 5000 already in use"
**Solution:**
```bash
netstat -ano | findstr :5000
taskkill /PID <PID_NUMBER> /F
```

### Issue: "Database initialization failed"
**Solution:**
```bash
# Check if sqlite.db exists
dir sqlite.db

# If missing, the server should create it automatically
# If corrupted, delete and restart:
del sqlite.db
npm run dev:network
```

### Issue: "TypeScript compilation errors"
**Solution:**
```bash
# Check for syntax errors
npx tsc --noEmit

# If errors found, fix them or use JavaScript version
node test-minimal-server.js
```

## Alternative: Start Frontend Only 🖥️

If backend won't start, you can test frontend with a mock backend:

1. Start minimal server: `node test-minimal-server.js`
2. Update frontend to use minimal API
3. Test basic functionality

## Next Steps After Backend Starts ➡️

1. ✅ Backend running on http://***********:5000
2. Start frontend: `npm run dev:network-frontend`
3. Access application: http://***********:5173
4. Login with: admin / admin123
5. Test from mobile device

## Files to Use 📁

- `test-backend-only.bat` - Backend startup script
- `test-minimal-server.js` - Minimal server test
- `diagnose-network.js` - Network diagnostics
- `start-both-servers.bat` - Complete solution

The key is to get the backend server running first, then the frontend will be able to connect to it.
