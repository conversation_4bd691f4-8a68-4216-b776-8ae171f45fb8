@echo off
echo Starting IQRAA Manager Server...

REM Set environment variables
set NODE_ENV=development
set DB_TYPE=sqlite
set PG_DISABLED=true
set SQLITE_PATH=./sqlite.db
set PORT=5000
set HOST=0.0.0.0
set SERVER_IP=***********
set CORS_ORIGIN=*
set ALLOW_NETWORK_ACCESS=true

echo Environment configured:
echo   PORT: %PORT%
echo   HOST: %HOST%
echo   SERVER_IP: %SERVER_IP%
echo   DB_TYPE: %DB_TYPE%

echo.
echo Starting backend server...
npx tsx server/index-sqlite.ts

pause
