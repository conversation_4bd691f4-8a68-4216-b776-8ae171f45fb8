# PowerShell script to start backend with debugging

Write-Host "🔧 IQRAA Manager - Backend Debug Startup" -ForegroundColor Blue
Write-Host "=======================================" -ForegroundColor Blue

# Set environment variables
$env:NODE_ENV = "development"
$env:DB_TYPE = "sqlite"
$env:PG_DISABLED = "true"
$env:SQLITE_PATH = "./sqlite.db"
$env:HOST = "0.0.0.0"
$env:PORT = "5000"
$env:SERVER_IP = "***********"
$env:CORS_ORIGIN = "*"
$env:ALLOW_NETWORK_ACCESS = "true"

Write-Host ""
Write-Host "📋 Environment Variables:" -ForegroundColor Yellow
Write-Host "   NODE_ENV: $env:NODE_ENV" -ForegroundColor Cyan
Write-Host "   DB_TYPE: $env:DB_TYPE" -ForegroundColor Cyan
Write-Host "   HOST: $env:HOST" -ForegroundColor Cyan
Write-Host "   PORT: $env:PORT" -ForegroundColor Cyan
Write-Host "   SERVER_IP: $env:SERVER_IP" -ForegroundColor Cyan

# Check prerequisites
Write-Host ""
Write-Host "🔍 Checking prerequisites..." -ForegroundColor Yellow

# Check Node.js
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not found" -ForegroundColor Red
    exit 1
}

# Check if SQLite database exists
if (Test-Path "sqlite.db") {
    $dbSize = (Get-Item "sqlite.db").Length
    Write-Host "✅ SQLite database exists ($dbSize bytes)" -ForegroundColor Green
} else {
    Write-Host "⚠️ SQLite database not found - will be created" -ForegroundColor Yellow
}

# Check if server file exists
if (Test-Path "server/index-sqlite.ts") {
    Write-Host "✅ Server file exists" -ForegroundColor Green
} else {
    Write-Host "❌ Server file not found" -ForegroundColor Red
    exit 1
}

# Check if port 5000 is free
$portCheck = netstat -an | Select-String ":5000"
if ($portCheck) {
    Write-Host "⚠️ Port 5000 is in use:" -ForegroundColor Yellow
    Write-Host $portCheck -ForegroundColor White
    Write-Host "Attempting to kill existing processes..." -ForegroundColor Yellow
    
    # Try to kill processes using port 5000
    $processes = netstat -ano | Select-String ":5000" | ForEach-Object {
        $line = $_.Line
        if ($line -match "\s+(\d+)$") {
            $matches[1]
        }
    }
    
    foreach ($pid in $processes) {
        try {
            Stop-Process -Id $pid -Force
            Write-Host "✅ Killed process $pid" -ForegroundColor Green
        } catch {
            Write-Host "⚠️ Could not kill process $pid" -ForegroundColor Yellow
        }
    }
} else {
    Write-Host "✅ Port 5000 is available" -ForegroundColor Green
}

Write-Host ""
Write-Host "🚀 Starting backend server..." -ForegroundColor Green
Write-Host "Command: npx tsx server/index-sqlite.ts" -ForegroundColor Cyan

# Start the server with error handling
try {
    # Use Start-Process to capture output
    $process = Start-Process -FilePath "npx" -ArgumentList "tsx", "server/index-sqlite.ts" -NoNewWindow -PassThru -RedirectStandardOutput "backend-output.log" -RedirectStandardError "backend-error.log"
    
    Write-Host "✅ Backend process started (PID: $($process.Id))" -ForegroundColor Green
    
    # Wait a bit and check if process is still running
    Start-Sleep -Seconds 3
    
    if ($process.HasExited) {
        Write-Host "❌ Backend process exited unexpectedly" -ForegroundColor Red
        Write-Host "Exit code: $($process.ExitCode)" -ForegroundColor Red
        
        if (Test-Path "backend-error.log") {
            Write-Host "Error log:" -ForegroundColor Red
            Get-Content "backend-error.log" | Write-Host -ForegroundColor Red
        }
    } else {
        Write-Host "✅ Backend process is running" -ForegroundColor Green
        
        # Test the server
        Start-Sleep -Seconds 2
        Write-Host "🧪 Testing server connection..." -ForegroundColor Yellow
        
        try {
            $response = Invoke-WebRequest -Uri "http://***********:5000/health" -TimeoutSec 5
            Write-Host "✅ Server is responding!" -ForegroundColor Green
            Write-Host "Response: $($response.Content)" -ForegroundColor Cyan
        } catch {
            Write-Host "❌ Server not responding: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        Write-Host ""
        Write-Host "🌐 Server should be accessible at:" -ForegroundColor Blue
        Write-Host "   http://***********:5000/health" -ForegroundColor Cyan
        Write-Host "   http://***********:5000/api/stats" -ForegroundColor Cyan
        
        Write-Host ""
        Write-Host "Press any key to stop the server..." -ForegroundColor Yellow
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        
        # Stop the process
        if (-not $process.HasExited) {
            $process.Kill()
            Write-Host "🛑 Server stopped" -ForegroundColor Red
        }
    }
    
} catch {
    Write-Host "❌ Failed to start server: $($_.Exception.Message)" -ForegroundColor Red
}

# Clean up log files
if (Test-Path "backend-output.log") { Remove-Item "backend-output.log" }
if (Test-Path "backend-error.log") { Remove-Item "backend-error.log" }
