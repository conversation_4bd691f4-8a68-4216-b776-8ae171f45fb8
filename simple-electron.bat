@echo off
REM Script ultra-simple pour Electron

echo ==================================
echo Test ELECTRON ultra-simple
echo ==================================

REM Installer electron globalement si necessaire
where electron >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
  echo Installation d'Electron...
  call npm install electron -g
)

REM Creer le dossier electron si necessaire
if not exist electron mkdir electron

REM Creer un fichier preload tres simple
echo const { contextBridge } = require('electron'); > electron\preload.js
echo contextBridge.exposeInMainWorld('api', { >> electron\preload.js
echo   getEnv: () => { return { NODE_ENV: process.env.NODE_ENV || 'development' }; } >> electron\preload.js
echo }); >> electron\preload.js

REM Creer un fichier HTML simple
echo ^<!DOCTYPE html^> > test.html
echo ^<html^> >> test.html
echo ^<head^>^<title^>iQraa Test^</title^>^</head^> >> test.html
echo ^<body^> >> test.html
echo   ^<h1^>iQraa Test^</h1^> >> test.html
echo   ^<p id="info"^>Test Electron^</p^> >> test.html
echo   ^<script^> >> test.html
echo     if (window.api) { >> test.html
echo       document.getElementById('info').innerHTML = 'Mode: ' + window.api.getEnv().NODE_ENV; >> test.html
echo     } >> test.html
echo   ^</script^> >> test.html
echo ^</body^> >> test.html
echo ^</html^> >> test.html

REM Creer un fichier main.js pour Electron
echo const { app, BrowserWindow } = require('electron'); > main.js
echo const path = require('path'); >> main.js
echo function createWindow() { >> main.js
echo   const win = new BrowserWindow({ >> main.js
echo     width: 800, height: 600, >> main.js
echo     webPreferences: { >> main.js
echo       nodeIntegration: false, contextIsolation: true, >> main.js
echo       preload: path.join(__dirname, 'electron', 'preload.js') >> main.js
echo     } >> main.js
echo   }); >> main.js
echo   win.loadFile('test.html'); >> main.js
echo   win.webContents.openDevTools(); >> main.js
echo } >> main.js
echo app.whenReady().then(createWindow); >> main.js
echo app.on('window-all-closed', () => app.quit()); >> main.js

echo Demarrage d'Electron avec un fichier ultra-simple...
electron main.js

echo Test termine!