@echo off
setlocal EnableDelayedExpansion

REM Définir les couleurs pour une meilleure lisibilité
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "CYAN=[96m"
set "RESET=[0m"

title iQraa - Serveur Réseau Local

echo %BLUE%========================================================%RESET%
echo %GREEN%🌐 Démarrage de l'application iQraa sur le réseau local%RESET%
echo %BLUE%========================================================%RESET%

REM Détecter l'adresse IP locale
echo %YELLOW%🔍 Détection de l'adresse IP locale...%RESET%
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    for /f "tokens=1" %%b in ("%%a") do (
        set "LOCAL_IP=%%b"
        goto :ip_found
    )
)
:ip_found

if "%LOCAL_IP%"=="" (
    echo %RED%❌ Impossible de détecter l'adresse IP locale automatiquement.%RESET%
    set /p LOCAL_IP="Veuillez entrer l'adresse IP de ce serveur (ex: *************): "
)

echo %CYAN%📍 Adresse IP détectée: %LOCAL_IP%%RESET%

REM Vérifier les prérequis
echo %YELLOW%🔍 Vérification des prérequis...%RESET%
where node >nul 2>&1 || (
    echo %RED%❌ Node.js n'est pas installé ou n'est pas dans le PATH.%RESET%
    echo Veuillez installer Node.js depuis https://nodejs.org/
    pause
    exit /b 1
)

REM Vérifier si les dépendances sont installées
if not exist "node_modules" (
    echo %YELLOW%📦 Installation des dépendances...%RESET%
    call npm install
    if %ERRORLEVEL% NEQ 0 (
        echo %RED%❌ Erreur lors de l'installation des dépendances.%RESET%
        pause
        exit /b 1
    )
)

REM Nettoyer les processus existants
echo %YELLOW%🔄 Nettoyage des processus existants...%RESET%
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :5000') DO (
    echo  - Arrêt du processus sur le port 5000 (PID: %%P)
    TaskKill /PID %%P /F /T >NUL 2>&1
)
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :5173') DO (
    echo  - Arrêt du processus sur le port 5173 (PID: %%P)
    TaskKill /PID %%P /F /T >NUL 2>&1
)

REM Créer le fichier de base de données SQLite s'il n'existe pas
if not exist "sqlite.db" (
    echo %YELLOW%🗄️ Initialisation de la base de données SQLite...%RESET%
    echo. > sqlite.db
)

REM Configurer les variables d'environnement pour le réseau
set NODE_ENV=production
set DB_TYPE=sqlite
set PG_DISABLED=true
set SQLITE_PATH=./sqlite.db
set PORT=5000
set HOST=0.0.0.0
set SERVER_IP=%LOCAL_IP%
set VITE_API_URL=http://%LOCAL_IP%:5000
set VITE_SERVER_IP=%LOCAL_IP%
set VITE_FRONTEND_PORT=5173
set VITE_BACKEND_PORT=5000
set CORS_ORIGIN=*
set ALLOW_NETWORK_ACCESS=true

REM Démarrer le serveur backend
echo %GREEN%🌐 Démarrage du serveur backend sur http://%LOCAL_IP%:5000%RESET%
start "iQraa Backend" cmd /c "npx cross-env NODE_ENV=%NODE_ENV% DB_TYPE=%DB_TYPE% PG_DISABLED=%PG_DISABLED% SQLITE_PATH=%SQLITE_PATH% PORT=%PORT% HOST=%HOST% SERVER_IP=%SERVER_IP% CORS_ORIGIN=%CORS_ORIGIN% npx tsx server/index-sqlite.ts"

REM Attendre que le backend se lance
echo %YELLOW%⏳ Attente du démarrage du backend (8 secondes)...%RESET%
ping -n 9 127.0.0.1 > nul

REM Démarrer le serveur frontend
echo %GREEN%🖥️ Démarrage du serveur frontend sur http://%LOCAL_IP%:5173%RESET%
start "iQraa Frontend" cmd /c "npx cross-env NODE_ENV=%NODE_ENV% VITE_API_URL=%VITE_API_URL% VITE_SERVER_IP=%VITE_SERVER_IP% VITE_FRONTEND_PORT=%VITE_FRONTEND_PORT% VITE_BACKEND_PORT=%VITE_BACKEND_PORT% npx vite --config vite.config.network.ts"

echo.
echo %GREEN%✅ Application démarrée avec succès sur le réseau local!%RESET%
echo.
echo %CYAN%🌐 Accès depuis ce serveur:%RESET%
echo %CYAN%   👉 Frontend: http://localhost:5173%RESET%
echo %CYAN%   👉 Backend API: http://localhost:5000/api%RESET%
echo.
echo %BLUE%🌐 Accès depuis d'autres appareils du réseau:%RESET%
echo %BLUE%   👉 Frontend: http://%LOCAL_IP%:5173%RESET%
echo %BLUE%   👉 Backend API: http://%LOCAL_IP%:5000/api%RESET%
echo.
echo %YELLOW%📱 Pour accéder depuis un téléphone/tablette:%RESET%
echo %YELLOW%   Connectez-vous au même WiFi et ouvrez: http://%LOCAL_IP%:5173%RESET%
echo.
echo %BLUE%========================================================%RESET%
echo %YELLOW%Comptes par défaut:%RESET%
echo %YELLOW%  Admin: admin / admin123%RESET%
echo %YELLOW%  Bibliothécaire: librarian / librarian123%RESET%
echo %BLUE%========================================================%RESET%
echo %RED%⛔ Fermez cette fenêtre pour terminer tous les processus.%RESET%
echo %BLUE%========================================================%RESET%

REM Attendre que l'utilisateur appuie sur une touche
pause > nul

REM Tuer tous les processus
echo.
echo %YELLOW%🛑 Arrêt des processus...%RESET%
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :5000') DO TaskKill /PID %%P /F /T >NUL 2>&1
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :5173') DO TaskKill /PID %%P /F /T >NUL 2>&1

echo %GREEN%✅ Application arrêtée.%RESET%
timeout /t 2 > nul
