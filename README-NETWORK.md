# 🌐 IQRAA Manager - Configuration Réseau Local

Ce guide vous explique comment configurer et démarrer l'application IQRAA Manager pour un accès depuis tout le réseau local (WiFi/LAN).

## 🎯 Objectif

Permettre l'accès à l'application IQRAA Manager depuis :
- Le serveur local (localhost)
- Tous les ordinateurs du réseau local
- Téléphones et tablettes connectés au même WiFi
- Autres appareils sur le même réseau

## 📋 Prérequis

- **Node.js** (version 16 ou supérieure)
- **NPM** (version 8 ou supérieure)
- **Réseau local** (WiFi ou Ethernet)
- **Pare-feu** configuré pour autoriser les ports 5000 et 5173

## 🚀 Configuration Automatique (Recommandé)

### Étape 1: Configuration automatique
```bash
npm run setup:network
```

Cette commande va :
- Détecter automatiquement l'adresse IP de votre serveur
- Créer le fichier `.env.local` avec la bonne configuration
- Générer un fichier `NETWORK-INFO.md` avec toutes les informations

### Étape 2: Démarrage
**Windows:**
```bash
./start-network-server.bat
```

**Linux/Mac:**
```bash
chmod +x start-network-server.sh
./start-network-server.sh
```

## 🛠️ Configuration Manuelle

### 1. Trouver l'adresse IP de votre serveur

**Windows:**
```cmd
ipconfig
```
Cherchez "Adresse IPv4" (ex: *************)

**Linux/Mac:**
```bash
ip addr show
# ou
ifconfig
# ou
hostname -I
```

### 2. Modifier le fichier .env.local

Remplacez `*************` par l'IP de votre serveur :

```env
# Configuration réseau
SERVER_IP=*************
API_URL=http://*************:5000
VITE_API_URL=http://*************:5000
VITE_SERVER_IP=*************
HOST=0.0.0.0
CORS_ORIGIN=*
```

### 3. Démarrer l'application

**Option A: Scripts automatisés**
```bash
# Windows
./start-network-server.bat

# Linux/Mac
./start-network-server.sh
```

**Option B: NPM Scripts**
```bash
npm run dev:network-full
```

**Option C: Séparément**
```bash
# Terminal 1 - Backend
npm run dev:network

# Terminal 2 - Frontend
npm run dev:network-frontend
```

## 🌐 Accès à l'Application

### Depuis le serveur
- Frontend: http://localhost:5173
- Backend API: http://localhost:5000/api

### Depuis le réseau (remplacez par votre IP)
- Frontend: http://*************:5173
- Backend API: http://*************:5000/api

## 📱 Accès Mobile

1. **Connectez votre téléphone/tablette au même WiFi**
2. **Ouvrez le navigateur mobile**
3. **Allez à:** http://*************:5173
4. **Connectez-vous avec les comptes par défaut**

## 👤 Comptes par Défaut

- **Administrateur:**
  - Identifiant: `admin`
  - Mot de passe: `admin123`

- **Bibliothécaire:**
  - Identifiant: `librarian`
  - Mot de passe: `librarian123`

## 🔧 Configuration du Pare-feu

### Windows
```cmd
# Autoriser les ports entrants
netsh advfirewall firewall add rule name="iQraa Backend" dir=in action=allow protocol=TCP localport=5000
netsh advfirewall firewall add rule name="iQraa Frontend" dir=in action=allow protocol=TCP localport=5173
```

### Linux (UFW)
```bash
sudo ufw allow 5000
sudo ufw allow 5173
```

### macOS
```bash
# Généralement pas nécessaire, mais si besoin :
sudo pfctl -f /etc/pf.conf
```

## 🔍 Dépannage

### Problème: Impossible d'accéder depuis un autre appareil

1. **Vérifiez l'IP du serveur:**
   ```bash
   npm run setup:network
   ```

2. **Vérifiez le pare-feu:**
   - Windows: Panneau de configuration > Pare-feu Windows
   - Linux: `sudo ufw status`
   - macOS: Préférences Système > Sécurité > Pare-feu

3. **Testez la connectivité:**
   ```bash
   # Depuis un autre appareil
   ping *************
   telnet ************* 5000
   ```

### Problème: L'IP change souvent

Si votre routeur attribue des IP dynamiques :

1. **Configurez une IP statique sur votre serveur**
2. **Ou relancez la configuration à chaque changement:**
   ```bash
   npm run setup:network
   ```

### Problème: Erreurs CORS

Si vous voyez des erreurs CORS dans la console :

1. **Vérifiez que `CORS_ORIGIN=*` dans .env.local**
2. **Redémarrez l'application**
3. **Videz le cache du navigateur**

## 🔒 Sécurité

### ⚠️ Important
- Cette configuration est prévue pour un **réseau local de confiance**
- **Ne pas exposer sur Internet** sans configuration de sécurité supplémentaire
- **Changez les mots de passe par défaut** en production

### Recommandations
- Utilisez un réseau WiFi sécurisé (WPA2/WPA3)
- Limitez l'accès aux appareils de confiance
- Surveillez les connexions dans les logs du serveur

## 📊 Monitoring

### Logs du serveur
Les logs affichent toutes les connexions :
```
🚀 iQraa Server running on http://*************:5000
🌐 Network access enabled - Server accessible from:
   - Local: http://localhost:5000
   - Network: http://*************:5000
   - Frontend: http://*************:5173
```

### Test de connectivité
```bash
npm run test:local
```

## 🆘 Support

En cas de problème :

1. **Vérifiez les logs** dans les terminaux
2. **Testez la connectivité** réseau
3. **Vérifiez la configuration** du pare-feu
4. **Relancez la configuration** automatique
5. **Consultez** `NETWORK-INFO.md` généré automatiquement

## 📁 Fichiers de Configuration

- `.env.local` - Configuration principale
- `.env.network` - Template de configuration réseau
- `vite.config.network.ts` - Configuration Vite pour le réseau
- `NETWORK-INFO.md` - Informations générées automatiquement
