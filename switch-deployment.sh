#!/bin/bash

# Script pour basculer entre les différents modes de déploiement
# Usage: ./switch-deployment.sh [caprover|electron|capacitor|dev]

# Vérifier que le paramètre est fourni
if [ "$#" -ne 1 ]; then
    echo "Usage: ./switch-deployment.sh [caprover|electron|capacitor|dev]"
    exit 1
fi

MODE=$1

echo "Passage au mode de déploiement: $MODE"

# Supprimer le fichier .npmrc existant s'il y en a un
if [ -f ".npmrc" ]; then
    rm .npmrc
    echo "Fichier .npmrc existant supprimé"
fi

case $MODE in
    caprover)
        cp .npmrc.caprover .npmrc
        echo "Mode CapRover activé"
        echo "Ce mode est optimisé pour le déploiement en ligne sans Electron ni Capacitor"
        ;;
    electron)
        cp .npmrc.electron .npmrc
        echo "Mode Electron activé"
        echo "Ce mode est optimisé pour le déploiement d'applications de bureau sans Capacitor"
        ;;
    capacitor)
        cp .npmrc.capacitor .npmrc
        echo "Mode Capacitor activé"
        echo "Ce mode est optimisé pour le déploiement d'applications mobiles sans Electron"
        ;;
    dev)
        echo "Mode développement activé"
        echo "Ce mode inclut toutes les dépendances pour le développement"
        ;;
    *)
        echo "Mode non reconnu: $MODE"
        echo "Modes disponibles: caprover, electron, capacitor, dev"
        exit 1
        ;;
esac

echo "Exécutez 'npm install' pour mettre à jour les dépendances"