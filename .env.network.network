# Configuration pour serveur réseau local
# Copiez ce fichier vers .env.local et modifiez l'adresse IP

# Environment
NODE_ENV=production

# Database Configuration
DB_TYPE=sqlite
SQLITE_PATH=./sqlite.db
PG_DISABLED=true

# Server Configuration - IMPORTANT: Remplacez par l'IP de votre serveur
PORT=5000
HOST=0.0.0.0
SERVER_IP=***********

# API Configuration - Utilisez l'IP du serveur
API_URL=http://***********:5000
VITE_API_URL=http://***********:5000

# Synchronization Configuration (disabled for local network)
SYNC_ENABLED=false
VITE_SYNC_ENABLED=false
SYNC_API_URL=http://***********:5000/api/sync
SYNC_INTERVAL=300000

# Client Configuration
VITE_DB_TYPE=sqlite
VITE_PG_DISABLED=true
VITE_IS_OFFLINE_FIRST=true
VITE_DEFAULT_LANGUAGE=fr

# Network Configuration
VITE_SERVER_IP=***********
VITE_FRONTEND_PORT=5173
VITE_BACKEND_PORT=5000

# Session Configuration
SESSION_SECRET=iqraa-network-production-secret-key-change-this

# Security Configuration
CORS_ORIGIN=*
ALLOW_NETWORK_ACCESS=true
TRUST_PROXY=true

# Logging
DEBUG=false
VERBOSE_LOGGING=false
