# PowerShell script to start both IQRAA Manager servers

Write-Host "========================================================" -ForegroundColor Blue
Write-Host "🌐 IQRAA Manager - Starting Network Servers" -ForegroundColor Green
Write-Host "========================================================" -ForegroundColor Blue

Write-Host ""
Write-Host "📋 Configuration:" -ForegroundColor Yellow
Write-Host "   Server IP: ***********" -ForegroundColor Cyan
Write-Host "   Backend Port: 5000" -ForegroundColor Cyan
Write-Host "   Frontend Port: 5173" -ForegroundColor Cyan

Write-Host ""
Write-Host "🚀 Starting servers..." -ForegroundColor Green

# Start backend server
Write-Host "🔧 Starting backend server..." -ForegroundColor Yellow
$backendArgs = @(
    "cross-env"
    "NODE_ENV=development"
    "DB_TYPE=sqlite"
    "PG_DISABLED=true"
    "HOST=0.0.0.0"
    "CORS_ORIGIN=*"
    "SERVER_IP=***********"
    "npx"
    "tsx"
    "server/index-sqlite.ts"
)

Start-Process -FilePath "npx" -ArgumentList $backendArgs -WindowStyle Normal

# Wait for backend to start
Write-Host "⏳ Waiting 8 seconds for backend to initialize..." -ForegroundColor Yellow
Start-Sleep -Seconds 8

# Start frontend server
Write-Host "🖥️ Starting frontend server..." -ForegroundColor Yellow
$frontendArgs = @(
    "cross-env"
    "VITE_API_URL=http://***********:5000"
    "VITE_SERVER_IP=***********"
    "VITE_FRONTEND_PORT=5173"
    "VITE_BACKEND_PORT=5000"
    "npx"
    "vite"
    "--config"
    "vite.config.network.ts"
)

Start-Process -FilePath "npx" -ArgumentList $frontendArgs -WindowStyle Normal

Write-Host ""
Write-Host "✅ Both servers are starting!" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 Access URLs:" -ForegroundColor Blue
Write-Host "   👉 Frontend: http://***********:5173" -ForegroundColor Cyan
Write-Host "   👉 Backend API: http://***********:5000/api" -ForegroundColor Cyan
Write-Host "   👉 Health Check: http://***********:5000/health" -ForegroundColor Cyan
Write-Host ""
Write-Host "👤 Default Login:" -ForegroundColor Yellow
Write-Host "   Admin: admin / admin123" -ForegroundColor White
Write-Host "   Librarian: librarian / librarian123" -ForegroundColor White
Write-Host ""
Write-Host "📱 Mobile Access:" -ForegroundColor Magenta
Write-Host "   Connect to same WiFi and open: http://***********:5173" -ForegroundColor White

Write-Host ""
Write-Host "========================================================" -ForegroundColor Blue
Write-Host "ℹ️ Two separate processes are now running:" -ForegroundColor Yellow
Write-Host "   - Backend server (port 5000)" -ForegroundColor White
Write-Host "   - Frontend server (port 5173)" -ForegroundColor White
Write-Host ""
Write-Host "🛑 To stop servers, close the PowerShell windows or use Ctrl+C" -ForegroundColor Red
Write-Host "========================================================" -ForegroundColor Blue

# Keep the script running
Write-Host ""
Write-Host "Press any key to exit this script (servers will continue running)..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
