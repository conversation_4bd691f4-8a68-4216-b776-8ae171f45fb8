@echo off
echo ===================================
echo = Demarrage de l'application iQraa =
echo ===================================
echo.

echo Verification et installation des dependances...
echo.

:: Verification si npm est installe
where npm >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [ERREUR] npm n'est pas installe ou n'est pas dans le PATH.
    echo Veuillez installer Node.js depuis https://nodejs.org/
    pause
    exit /b 1
)

:: Installation des dependances manquantes
echo Installation des modules necessaires...
call npm install --no-save cross-env vite typescript tsx @vitejs/plugin-react >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [ATTENTION] Probleme durant l'installation des dependances.
    :: Continuer quand meme
)

:: Creation du dossier de correction temporaire
echo Creation d'un correctif pour eviter l'erreur "Cannot find package 'vite'"...
set "TEMP_FIX_DIR=%TEMP%\iqraa-fix"
mkdir "%TEMP_FIX_DIR%" 2>nul

:: Creation de scripts de correction
echo export default {} > "%TEMP_FIX_DIR%\runtime-error-modal.js"
echo export const cartographer = () => {}; > "%TEMP_FIX_DIR%\cartographer.js"

:: Demarrage des serveurs dans des fenetres separees
echo.
echo Demarrage du serveur backend...
start cmd /k "set NODE_PATH=%TEMP_FIX_DIR%&npx cross-env NODE_ENV=development tsx server/index.ts"

echo Demarrage du serveur frontend avec configuration locale...
start cmd /k "set NODE_PATH=%TEMP_FIX_DIR%&npx vite --config vite-local.config.ts --port 5173"

echo.
echo =====================================================================
echo Application demarree! Acces a l'interface via http://localhost:5173
echo Utilisez les identifiants: admin / admin123
echo =====================================================================

:: Ne pas fermer cette fenetre
echo.
echo Cette fenetre peut rester ouverte. Fermez-la pour arreter l'application.
pause