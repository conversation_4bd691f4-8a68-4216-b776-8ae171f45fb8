/*
 * WARNING: do not edit!
 * Generated by util/mkbuildinf.pl
 *
 * Copyright 2014-2017 The OpenSSL Project Authors. All Rights Reserved.
 *
 * Licensed under the OpenSSL license (the "License").  You may not use
 * this file except in compliance with the License.  You can obtain a copy
 * in the file LICENSE in the source distribution or at
 * https://www.openssl.org/source/license.html
 */

#define PLATFORM "platform: linux32-s390x"
#define DATE "built on: Sun Aug  6 12:29:54 2023 UTC"

/*
 * Generate compiler_flags as an array of individual characters. This is a
 * workaround for the situation where CFLAGS gets too long for a C90 string
 * literal
 */
static const char compiler_flags[] = {
    'c','o','m','p','i','l','e','r',':',' ','g','c','c',' ','-','f',
    'P','I','C',' ','-','p','t','h','r','e','a','d',' ','-','m','3',
    '1',' ','-','W','a',',','-','m','z','a','r','c','h',' ','-','W',
    'a',',','-','-','n','o','e','x','e','c','s','t','a','c','k',' ',
    '-','W','a','l','l',' ','-','O','3',' ','-','D','O','P','E','N',
    'S','S','L','_','U','S','E','_','N','O','D','E','L','E','T','E',
    ' ','-','D','B','_','E','N','D','I','A','N',' ','-','D','O','P',
    'E','N','S','S','L','_','P','I','C',' ','-','D','O','P','E','N',
    'S','S','L','_','C','P','U','I','D','_','O','B','J',' ','-','D',
    'O','P','E','N','S','S','L','_','B','N','_','A','S','M','_','M',
    'O','N','T',' ','-','D','O','P','E','N','S','S','L','_','B','N',
    '_','A','S','M','_','G','F','2','m',' ','-','D','S','H','A','1',
    '_','A','S','M',' ','-','D','S','H','A','2','5','6','_','A','S',
    'M',' ','-','D','S','H','A','5','1','2','_','A','S','M',' ','-',
    'D','K','E','C','C','A','K','1','6','0','0','_','A','S','M',' ',
    '-','D','R','C','4','_','A','S','M',' ','-','D','A','E','S','_',
    'A','S','M',' ','-','D','A','E','S','_','C','T','R','_','A','S',
    'M',' ','-','D','A','E','S','_','X','T','S','_','A','S','M',' ',
    '-','D','G','H','A','S','H','_','A','S','M',' ','-','D','P','O',
    'L','Y','1','3','0','5','_','A','S','M',' ','-','D','N','D','E',
    'B','U','G','\0'
};
