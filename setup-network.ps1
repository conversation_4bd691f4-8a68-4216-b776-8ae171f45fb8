# PowerShell script to setup network configuration for IQRAA Manager

Write-Host "🌐 Setting up IQRAA Manager for network access..." -ForegroundColor Blue

# Get local IP address
$localIP = (Get-NetIPAddress -AddressFamily IPv4 -InterfaceAlias "Wi-Fi*" | Where-Object {$_.IPAddress -notlike "169.254.*"})[0].IPAddress

if (-not $localIP) {
    $localIP = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object {$_.IPAddress -notlike "127.*" -and $_.IPAddress -notlike "169.254.*"})[0].IPAddress
}

if (-not $localIP) {
    $localIP = "*************"
    Write-Host "⚠️ Could not detect IP automatically, using fallback: $localIP" -ForegroundColor Yellow
} else {
    Write-Host "📍 Detected IP address: $localIP" -ForegroundColor Green
}

# Backup existing .env.local if it exists
if (Test-Path ".env.local") {
    $backup = ".env.local.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    Copy-Item ".env.local" $backup
    Write-Host "💾 Backed up existing .env.local to: $backup" -ForegroundColor Yellow
}

# Create .env.local content
$envContent = @"
# Configuration pour le serveur réseau local
# Ce fichier configure l'application pour fonctionner sur un réseau local

# Environment
NODE_ENV=development

# Database Configuration
DB_TYPE=sqlite
SQLITE_PATH=./sqlite.db
PG_DISABLED=true

# Server Configuration - Network accessible
PORT=5000
HOST=0.0.0.0
# Replace with your actual server IP for production
SERVER_IP=$localIP

# API Configuration - Use server IP for network access
API_URL=http://$localIP:5000
VITE_API_URL=http://$localIP:5000

# Synchronization Configuration (disabled for local network)
SYNC_ENABLED=false
VITE_SYNC_ENABLED=false
SYNC_API_URL=http://$localIP:5000/api/sync
SYNC_INTERVAL=300000

# Client Configuration
VITE_DB_TYPE=sqlite
VITE_PG_DISABLED=true
VITE_IS_OFFLINE_FIRST=true
VITE_DEFAULT_LANGUAGE=fr

# Network Configuration
VITE_SERVER_IP=$localIP
VITE_FRONTEND_PORT=5173
VITE_BACKEND_PORT=5000

# Session Configuration
SESSION_SECRET=iqraa-local-network-secret-key

# Development flags
DEBUG=true
VERBOSE_LOGGING=true

# Network Security
CORS_ORIGIN=*
ALLOW_NETWORK_ACCESS=true
"@

# Write the .env.local file
$envContent | Out-File -FilePath ".env.local" -Encoding UTF8

Write-Host "✅ Created .env.local with network configuration" -ForegroundColor Green

Write-Host ""
Write-Host "🚀 To start the application:" -ForegroundColor Blue
Write-Host "   npm run dev:network-full" -ForegroundColor Cyan
Write-Host "   or use: start-network-fixed.bat" -ForegroundColor Cyan

Write-Host ""
Write-Host "🌐 Access URLs:" -ForegroundColor Blue
Write-Host "   Frontend: http://$localIP:5173" -ForegroundColor Cyan
Write-Host "   Backend: http://$localIP:5000" -ForegroundColor Cyan

Write-Host ""
Write-Host "👤 Default login: admin / admin123" -ForegroundColor Yellow

Write-Host ""
Write-Host "📱 Mobile access: Connect to the same WiFi and open http://$localIP:5173" -ForegroundColor Magenta
