#!/bin/bash

# Script amélioré pour construire l'application mobile avec Capacitor
echo "=========================================================="
echo "Préparation du build Capacitor pour Android avec API config"
echo "=========================================================="

# Variables de configuration
API_URL=${API_URL:-"https://api.iqraa-library.com"}
DEV_SERVER_URL=${DEV_SERVER_URL:-""}
ENVIRONMENT=${NODE_ENV:-"production"}
DEFAULT_LANGUAGE=${DEFAULT_LANGUAGE:-"fr"}

echo "Environnement: $ENVIRONMENT"
echo "URL API: $API_URL"
if [ "$ENVIRONMENT" = "development" ] && [ ! -z "$DEV_SERVER_URL" ]; then
  echo "URL Serveur Dev: $DEV_SERVER_URL"
fi

# Basculer vers la configuration Capacitor
echo "Passage en mode Capacitor..."
./switch-deployment.sh capacitor

# Installation des dépendances
echo "Installation des dépendances..."
npm install

# Créer un fichier .env.capacitor pour la configuration
echo "Configuration des variables d'environnement..."
cat > .env.capacitor << EOF
NODE_ENV=$ENVIRONMENT
PG_DISABLED=true
SYNC_ENABLED=true
SYNC_API_ENDPOINT=$API_URL
DEFAULT_LANGUAGE=$DEFAULT_LANGUAGE
APP_MODE=mobile
DEV_SERVER_URL=$DEV_SERVER_URL
EOF

# Exporter les variables pour le build
export NODE_ENV=$ENVIRONMENT
export PG_DISABLED=true
export SYNC_ENABLED=true
export SYNC_API_ENDPOINT=$API_URL
export DEFAULT_LANGUAGE=$DEFAULT_LANGUAGE
export APP_MODE=mobile
export DEV_SERVER_URL=$DEV_SERVER_URL

# Construction du projet avec configuration spécifique
echo "Construction du projet pour Capacitor..."
npx cross-env NODE_ENV=$ENVIRONMENT PG_DISABLED=true SYNC_ENABLED=true SYNC_API_ENDPOINT=$API_URL npm run build

# Corrige les problèmes de chemins dans les fichiers CSS et JS
echo "Application des corrections pour Capacitor..."
node fix-capacitor.js

# Synchronisation avec Capacitor
echo "Synchronisation avec Capacitor..."
npx cap sync android

# Construction de l'application Android
echo "=========================================================="
echo "Construction de l'application mobile terminée!"
echo "Pour finaliser le build Android, ouvrez le projet dans Android Studio:"
echo "npx cap open android"
echo ""
echo "Ensuite, dans Android Studio:"
echo "1. Vérifiez la configuration dans Build > Generate Signed Bundle/APK"
echo "2. Utilisez Build > Build Bundle(s) / APK(s) > Build APK(s)"
echo "=========================================================="