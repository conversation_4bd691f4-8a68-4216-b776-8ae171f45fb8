#!/bin/bash

# Script pour construire l'application Electron
echo "Préparation du build Electron..."

# Basculer vers la configuration Electron
./switch-deployment.sh electron

# Installation des dépendances avec support explicite pour les modules natifs
echo "Installation des dépendances avec support pour les modules natifs..."
npm install --include=optional

# Installation explicite des dépendances natives de Rollup pour éviter les erreurs
echo "Installation des dépendances natives de Rollup..."
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" || "$OSTYPE" == "cygwin" ]]; then
  # Windows
  npm install @rollup/rollup-win32-x64-msvc --no-save
elif [[ "$OSTYPE" == "darwin"* ]]; then
  # macOS
  npm install @rollup/rollup-darwin-x64 --no-save
else
  # Linux
  npm install @rollup/rollup-linux-x64-gnu --no-save
  # Pour les systèmes Linux alternatifs
  npm install @rollup/rollup-linux-x64-musl --no-save
fi

# Construction du projet avec variable d'environnement pour éviter les erreurs Rollup
echo "Construction du projet..."
VITE_SKIP_ROLLUP_NATIVE=1 npx cross-env NODE_ENV=production npm run build

# Construction de l'application Electron
echo "Construction de l'application Electron..."
npx electron-builder

echo "Build Electron terminé avec succès !"
echo "Les fichiers d'installation se trouvent dans le dossier dist_electron"