// Script pour démarrer le serveur en mode développement local avec SQLite
import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Charger les variables d'environnement depuis .env.local
const envLocalPath = path.join(__dirname, '.env.local');
if (fs.existsSync(envLocalPath)) {
  const envContent = fs.readFileSync(envLocalPath, 'utf8');
  const envVars = envContent
    .split('\n')
    .filter(line => line && !line.startsWith('#'))
    .map(line => line.split('='))
    .reduce((acc, [key, value]) => {
      if (key && value) acc[key] = value;
      return acc;
    }, {});

  // Ajouter les variables d'environnement
  Object.entries(envVars).forEach(([key, value]) => {
    process.env[key] = value;
  });
}

// Démarrer le frontend avec Vite
const viteProcess = spawn('npx', ['vite'], {
  stdio: 'inherit',
  env: { ...process.env }
});

// Démarrer le backend avec nodemon et ts-node
const backendProcess = spawn('npx', ['nodemon', '--exec', 'tsx', 'server/index-sqlite.ts'], {
  stdio: 'inherit',
  env: { ...process.env }
});

// Gestion de l'arrêt du processus
const cleanup = () => {
  viteProcess.kill();
  backendProcess.kill();
  process.exit(0);
};

process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);