@echo off
setlocal EnableDelayedExpansion

title IQRAA Manager - Network Server (Fixed)

echo ========================================================
echo 🌐 IQRAA Manager - Network Mode (Fixed Version)
echo ========================================================

echo 🔧 Applied fixes:
echo   - Fixed NODE_ENV to development mode
echo   - Verified database exists and has data
echo   - Confirmed network configuration
echo   - Updated package.json scripts

echo.
echo 📋 Current Configuration:
echo   Server IP: ***********
echo   Backend Port: 5000
echo   Frontend Port: 5173
echo   Database: SQLite (./sqlite.db)
echo   Environment: Development

echo.
echo 🔍 Checking prerequisites...

REM Check if Node.js is available
where node >nul 2>&1 || (
    echo ❌ Node.js not found. Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if dependencies are installed
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Clean up any existing processes on ports 5000 and 5173
echo 🔄 Cleaning up existing processes...
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :5000') DO (
    echo   - Stopping process on port 5000 (PID: %%P)
    TaskKill /PID %%P /F /T >NUL 2>&1
)
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :5173') DO (
    echo   - Stopping process on port 5173 (PID: %%P)
    TaskKill /PID %%P /F /T >NUL 2>&1
)

echo.
echo 🚀 Starting IQRAA Manager servers...

REM Start backend server
echo 🔧 Starting backend server...
start "IQRAA Backend" cmd /c "npx cross-env NODE_ENV=development DB_TYPE=sqlite PG_DISABLED=true HOST=0.0.0.0 CORS_ORIGIN=* npx tsx server/index-sqlite.ts"

REM Wait for backend to start
echo ⏳ Waiting for backend to initialize (10 seconds)...
ping -n 11 127.0.0.1 > nul

REM Start frontend server
echo 🖥️ Starting frontend server...
start "IQRAA Frontend" cmd /c "npx cross-env VITE_API_URL=http://***********:5000 VITE_SERVER_IP=*********** npx vite --config vite.config.network.ts"

echo.
echo ✅ Servers started successfully!
echo.
echo 🌐 Access URLs:
echo   📱 Frontend: http://***********:5173
echo   🔧 Backend API: http://***********:5000/api
echo   ❤️ Health Check: http://***********:5000/health
echo.
echo 👤 Default Login:
echo   Admin: admin / admin123
echo   Librarian: librarian / librarian123
echo.
echo 📱 Mobile Access:
echo   Connect to the same WiFi and open: http://***********:5173
echo.
echo ========================================================
echo ⚠️ Keep this window open to monitor the servers
echo 🛑 Close this window to stop all servers
echo ========================================================

pause

REM Cleanup when user closes
echo.
echo 🛑 Stopping servers...
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :5000') DO TaskKill /PID %%P /F /T >NUL 2>&1
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :5173') DO TaskKill /PID %%P /F /T >NUL 2>&1
echo ✅ Servers stopped.
timeout /t 2 > nul
