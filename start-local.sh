#!/bin/bash
# Script pour démarrer l'application en mode développement local

echo "🚀 Démarrage de l'application iQraa en mode développement local"

# Vérifier si les scripts JS existent
if [ -f "scripts/local-dev.js" ]; then
  echo "🔧 Utilisation du script Node.js..."
  node scripts/local-dev.js
else
  echo "⚠️ Script JS non trouvé, utilisation du mode shell..."
  
  # Vérifier si nous sommes dans un environnement Windows
  if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    echo "💻 Environnement Windows détecté"
    CMD_PREFIX="npx cross-env"
  else
    echo "💻 Environnement Unix/Linux/Mac détecté"
    CMD_PREFIX=""
  fi

  # Arrêter les processus existants sur les ports 3000 et 5173 si nécessaire
  echo "🔄 Nettoyage des ports..."
  if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    # Windows - on utilise une commande echo pour indiquer ce qu'il faudrait faire manuellement
    echo "Sur Windows, veuillez exécuter dans CMD:"
    echo "FOR /F \"tokens=5\" %P IN ('netstat -a -n -o ^| findstr :3000') DO TaskKill /PID %P /F /T >NUL 2>&1"
    echo "FOR /F \"tokens=5\" %P IN ('netstat -a -n -o ^| findstr :5173') DO TaskKill /PID %P /F /T >NUL 2>&1"
  else
    # Unix/Linux/Mac
    lsof -ti:3000 | xargs kill -9 2>/dev/null
    lsof -ti:5173 | xargs kill -9 2>/dev/null
  fi

  echo "🌐 Démarrage du serveur backend sur http://localhost:3000"
  $CMD_PREFIX NODE_ENV=development DB_TYPE=sqlite node ./node_modules/.bin/tsx server/index-sqlite.ts &
  BACKEND_PID=$!

  echo "⏳ Attente du démarrage du backend..."
  sleep 3

  echo "🖥️ Démarrage du serveur frontend sur http://localhost:5173"
  $CMD_PREFIX NODE_ENV=development node ./node_modules/.bin/vite &
  FRONTEND_PID=$!

  echo ""
  echo "✅ Serveurs démarrés avec succès"
  echo "👉 Accédez à l'application via: http://localhost:5173"
  echo "📊 API disponible sur: http://localhost:3000/api"
  echo ""
  echo "⛔ Appuyez sur Ctrl+C pour arrêter les serveurs"

  # Fonction de nettoyage
  cleanup() {
    echo ""
    echo "🛑 Arrêt des serveurs..."
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    exit 0
  }

  # Capture les signaux pour nettoyer
  trap cleanup SIGINT SIGTERM

  # Attend indéfiniment
  wait $BACKEND_PID $FRONTEND_PID
fi