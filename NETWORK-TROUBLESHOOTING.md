# 🔧 IQRAA Manager - Network Mode Troubleshooting

## Issue Analysis

The frontend is trying to connect to `***********:5000` but getting `ECONNREFUSED` errors, indicating the backend server is not running or not accessible on that address.

## Root Cause

The main issue was in `package.json` where the `dev:network` script was set to `NODE_ENV=production` instead of `development`, causing the server to try to serve static files instead of running in development mode with proper API routes.

## ✅ Fixes Applied

### 1. Fixed package.json script
```json
"dev:network": "NODE_ENV=development DB_TYPE=sqlite PG_DISABLED=true HOST=0.0.0.0 CORS_ORIGIN=* tsx server/index-sqlite.ts"
```

### 2. Environment Configuration
The `.env.local` file is correctly configured with:
- `SERVER_IP=***********`
- `API_URL=http://***********:5000`
- `VITE_API_URL=http://***********:5000`
- `HOST=0.0.0.0` (allows network access)
- `CORS_ORIGIN=*` (allows all origins)

### 3. Database Status
✅ SQLite database exists and contains data:
- Users table with admin accounts
- Books, readers, loans, locations tables
- All tables properly initialized

## 🚀 How to Start the Server

### Option 1: Using NPM Scripts (Recommended)
```bash
# Terminal 1 - Start backend
npm run dev:network

# Terminal 2 - Start frontend  
npm run dev:network-frontend

# Or start both together
npm run dev:network-full
```

### Option 2: Using Simplified Scripts
```bash
# Start backend
start-simple-server.bat

# Start frontend (in another terminal)
start-simple-frontend.bat
```

### Option 3: Manual Commands
```bash
# Backend
npx cross-env NODE_ENV=development DB_TYPE=sqlite PG_DISABLED=true HOST=0.0.0.0 CORS_ORIGIN=* npx tsx server/index-sqlite.ts

# Frontend
npx cross-env VITE_API_URL=http://***********:5000 VITE_SERVER_IP=*********** npx vite --config vite.config.network.ts
```

## 🔍 Verification Steps

### 1. Check if backend is running
```bash
curl http://***********:5000/health
```
Should return:
```json
{
  "status": "ok",
  "timestamp": "...",
  "environment": "development",
  "database": "sqlite",
  "version": "1.0.0"
}
```

### 2. Check API endpoints
```bash
curl http://***********:5000/api/stats
```

### 3. Check frontend proxy
Open browser to `http://***********:5173` and check network tab for API calls.

## 🌐 Network Access

### From the server machine:
- Frontend: http://localhost:5173
- Backend: http://localhost:5000

### From other devices on the network:
- Frontend: http://***********:5173
- Backend: http://***********:5000

## 🔒 Default Login Credentials

- **Admin**: username: `admin`, password: `admin123`
- **Librarian**: username: `librarian`, password: `librarian123`

## 🛠️ Common Issues & Solutions

### Issue: "ECONNREFUSED ***********:5000"
**Solution**: Backend server is not running. Start it with `npm run dev:network`

### Issue: "CORS errors"
**Solution**: Ensure `CORS_ORIGIN=*` is set in environment

### Issue: "Cannot find module"
**Solution**: Run `npm install` to install dependencies

### Issue: "Port already in use"
**Solution**: Kill existing processes:
```bash
# Windows
netstat -ano | findstr :5000
taskkill /PID <PID> /F

# Or use the cleanup in start-network-server.bat
```

### Issue: "Database errors"
**Solution**: Database exists and is properly initialized. If issues persist, delete `sqlite.db` and restart server to recreate.

## 📊 Server Configuration Summary

- **Backend Port**: 5000
- **Frontend Port**: 5173
- **Database**: SQLite (./sqlite.db)
- **Network Host**: 0.0.0.0 (allows network access)
- **Server IP**: ***********
- **CORS**: Enabled for all origins
- **Environment**: Development mode
- **Sync**: Disabled (local network mode)

## 🔄 Next Steps

1. Start the backend server using one of the methods above
2. Verify it's accessible at http://***********:5000/health
3. Start the frontend server
4. Access the application at http://***********:5173
5. Login with default credentials
6. Test functionality from other devices on the network
