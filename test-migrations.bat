@echo off
REM Script de test des migrations PostgreSQL pour Windows
REM Ce script exécute les migrations sur la base PostgreSQL locale
REM pour vérifier qu'elles fonctionnent bien avant le déploiement

echo ==================================
echo Test des migrations PostgreSQL
echo ==================================

REM Vérifier si la variable d'environnement DATABASE_URL est définie
if "%DATABASE_URL%"=="" (
  echo La variable DATABASE_URL n'est pas définie.
  echo Pour continuer, définissez DATABASE_URL avec votre connexion PostgreSQL.
  exit /b 1
)

REM Exécuter les migrations via le script
echo Exécution des migrations...
set NODE_ENV=production
set DB_TYPE=postgresql
npx tsx server/migrate-pg.ts

REM Vérifier le code de sortie
if %ERRORLEVEL% equ 0 (
  echo Test des migrations réussi!
) else (
  echo Erreur lors du test des migrations.
  exit /b 1
)