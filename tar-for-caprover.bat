@echo off
REM Script pour installer et utiliser tar.exe sur Windows

echo ==================================
echo Creation d'un fichier TAR.GZ pour CapRover
echo ==================================

REM Verifier si tar.exe est deja present
where tar.exe >nul 2>nul
if %ERRORLEVEL% EQU 0 (
  echo Tar.exe est deja installe sur votre systeme.
) else (
  echo Tar.exe n'est pas trouve. Installation temporaire...
  REM Telecharger busybox qui contient tar.exe
  echo Telechargement de busybox qui inclut tar...
  powershell -Command "Invoke-WebRequest -Uri 'https://frippery.org/files/busybox/busybox.exe' -OutFile 'busybox.exe'"
  
  echo Busybox telecharge, on peut l'utiliser pour creer des archives tar
)

REM Obtenir la date pour le nom du fichier
set TIMESTAMP=%date:~-4,4%%date:~-7,2%%date:~-10,2%
set TARBALL_NAME=iqraa-caprover-%TIMESTAMP%.tar

REM Creer un repertoire temporaire
set TEMP_DIR=caprover_build
mkdir %TEMP_DIR% 2>nul

echo Preparation des fichiers pour l'archive...

REM Copier tous les fichiers necessaires
xcopy client %TEMP_DIR%\client\ /E /I /Y
xcopy server %TEMP_DIR%\server\ /E /I /Y
xcopy shared %TEMP_DIR%\shared\ /E /I /Y
xcopy drizzle %TEMP_DIR%\drizzle\ /E /I /Y
if exist public xcopy public %TEMP_DIR%\public\ /E /I /Y

REM Copier les fichiers de configuration
copy deployment\caprover\package.json %TEMP_DIR%\
copy captain-definition %TEMP_DIR%\
copy deployment\caprover\Dockerfile %TEMP_DIR%\
copy tsconfig.json %TEMP_DIR%\
copy drizzle.config.ts %TEMP_DIR%\
copy vite.config.ts %TEMP_DIR%\

REM Creer un fichier .env.production minimal
echo NODE_ENV=production > %TEMP_DIR%\.env.production
echo DATABASE_URL=postgres://user:password@localhost:5432/iqraa >> %TEMP_DIR%\.env.production
echo VITE_API_URL=http://localhost:5000 >> %TEMP_DIR%\.env.production

REM Si busybox est disponible, l'utiliser; sinon utiliser tar.exe natif
echo Creation de l'archive TAR...

if exist busybox.exe (
  REM Utiliser busybox pour creer le tar
  cd %TEMP_DIR%
  ..\busybox.exe tar -cf ..\%TARBALL_NAME% *
  cd ..
) else (
  REM Utiliser le tar natif de Windows
  tar -cf %TARBALL_NAME% -C %TEMP_DIR% .
)

REM Comprimer avec gzip si disponible
where gzip >nul 2>nul
if %ERRORLEVEL% EQU 0 (
  echo Compression de l'archive avec gzip...
  gzip -f %TARBALL_NAME%
  echo Archive TAR.GZ creee: %TARBALL_NAME%.gz
) else (
  echo Gzip non disponible, utilisation de l'archive TAR non compresse: %TARBALL_NAME%
  echo POUR CAPROVER: Vous devrez peut-etre renommer l'archive en .tar.gz
)

echo.
echo Archive creee avec succes!
echo Vous pouvez maintenant telecharger cette archive dans votre panneau CapRover.
echo.

REM Supprimer le repertoire temporaire
rmdir /S /Q %TEMP_DIR%

REM Supprimer busybox s'il a ete telecharge
if exist busybox.exe (
  echo Suppression de l'utilitaire busybox temporaire...
  del busybox.exe
)