@echo off
REM Script final et ultra-simplifie pour tester Electron avec un chemin de preload correct

echo ==================================
echo Test FINAL de l'application iQraa avec Electron
echo ==================================

REM Verifier si Electron est installe globalement
where electron >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
  echo Installation d'Electron (peut prendre quelques minutes)...
  call npm install electron -g
)

REM Creer le repertoire preload si necessaire
if not exist electron mkdir electron

REM S'assurer que les fichiers essentiels sont presents
echo Preparation des fichiers...

REM Generer un fichier HTML minimal pour le test
echo ^<!DOCTYPE html^> > test-electron.html
echo ^<html^> >> test-electron.html
echo ^<head^> >> test-electron.html
echo   ^<meta charset="utf-8"^> >> test-electron.html
echo   ^<title^>iQraa Test^</title^> >> test-electron.html
echo   ^<style^>body{font-family:Arial;text-align:center;padding:50px;}^</style^> >> test-electron.html
echo ^</head^> >> test-electron.html
echo ^<body^> >> test-electron.html
echo   ^<h1^>iQraa - Test Electron^</h1^> >> test-electron.html
echo   ^<p^>Cette page confirme que l'application fonctionne correctement avec Electron.^</p^> >> test-electron.html
echo   ^<script^> >> test-electron.html
echo     document.write('^<p^>Mode: ' + (window.api ? window.api.getEnv().NODE_ENV : 'inconnu') + '^</p^>'); >> test-electron.html
echo   ^</script^> >> test-electron.html
echo ^</body^> >> test-electron.html
echo ^</html^> >> test-electron.html

REM Creer un fichier preload simplifie
echo // Preload script simple pour Electron > electron\preload.js
echo const { contextBridge } = require('electron'); >> electron\preload.js
echo. >> electron\preload.js
echo contextBridge.exposeInMainWorld('api', { >> electron\preload.js
echo   getEnv: () => { >> electron\preload.js
echo     return { >> electron\preload.js
echo       NODE_ENV: process.env.NODE_ENV || 'development', >> electron\preload.js
echo       DB_TYPE: process.env.DB_TYPE || 'sqlite' >> electron\preload.js
echo     }; >> electron\preload.js
echo   } >> electron\preload.js
echo }); >> electron\preload.js

REM Creer un fichier main simplifie pour Electron
echo // Main process pour Electron > electron-main-minimal.js
echo const { app, BrowserWindow } = require('electron'); >> electron-main-minimal.js
echo const path = require('path'); >> electron-main-minimal.js
echo. >> electron-main-minimal.js
echo function createWindow() { >> electron-main-minimal.js
echo   // Configuration simplifiee >> electron-main-minimal.js
echo   const mainWindow = new BrowserWindow({ >> electron-main-minimal.js
echo     width: 800, >> electron-main-minimal.js
echo     height: 600, >> electron-main-minimal.js
echo     webPreferences: { >> electron-main-minimal.js
echo       nodeIntegration: false, >> electron-main-minimal.js
echo       contextIsolation: true, >> electron-main-minimal.js
echo       preload: path.join(__dirname, 'electron', 'preload.js') >> electron-main-minimal.js
echo     } >> electron-main-minimal.js
echo   }); >> electron-main-minimal.js
echo. >> electron-main-minimal.js
echo   // Ouvrir DevTools en mode dev >> electron-main-minimal.js
echo   if (process.env.NODE_ENV === 'development') { >> electron-main-minimal.js
echo     mainWindow.webContents.openDevTools(); >> electron-main-minimal.js
echo   } >> electron-main-minimal.js
echo. >> electron-main-minimal.js 
echo   // Charger le fichier HTML de test >> electron-main-minimal.js
echo   mainWindow.loadFile('test-electron.html'); >> electron-main-minimal.js
echo   console.log('Application iQraa demarree avec succes en mode TEST'); >> electron-main-minimal.js
echo } >> electron-main-minimal.js
echo. >> electron-main-minimal.js
echo app.whenReady().then(createWindow); >> electron-main-minimal.js
echo. >> electron-main-minimal.js
echo app.on('window-all-closed', () => { >> electron-main-minimal.js
echo   app.quit(); >> electron-main-minimal.js
echo }); >> electron-main-minimal.js

echo Demarrage de l'application en mode Electron minimal...
set NODE_ENV=development

REM Lancer electron avec notre fichier minimal
electron electron-main-minimal.js

echo Test Electron termine!