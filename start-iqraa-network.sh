#!/bin/bash

# IqraaManager Network Server Startup Script
# Compatible with macOS 10.15

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================================${NC}"
echo -e "${GREEN}🌐 Starting IqraaManager Network Server${NC}"
echo -e "${BLUE}========================================================${NC}"

# Set up Node.js path
NODE_PATH="./node-v16.20.2-darwin-x64/bin"
export PATH="$NODE_PATH:$PATH"

# Verify Node.js is working
echo -e "${YELLOW}🔍 Checking Node.js installation...${NC}"
if ! $NODE_PATH/node --version; then
    echo -e "${RED}❌ Node.js not working properly${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Node.js $(${NODE_PATH}/node --version) is ready${NC}"

# Detect local IP address
echo -e "${YELLOW}🔍 Detecting local IP address...${NC}"
if command -v ifconfig &> /dev/null; then
    LOCAL_IP=$(ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -1)
elif command -v hostname &> /dev/null; then
    LOCAL_IP=$(hostname -I | awk '{print $1}' 2>/dev/null)
fi

# If no IP detected, use default
if [ -z "$LOCAL_IP" ] || [ "$LOCAL_IP" = "127.0.0.1" ]; then
    LOCAL_IP="***********"
    echo -e "${YELLOW}⚠️ Using default IP: $LOCAL_IP${NC}"
else
    echo -e "${CYAN}📍 Detected IP: $LOCAL_IP${NC}"
fi

# Clean up existing processes
echo -e "${YELLOW}🔄 Cleaning up existing processes...${NC}"
pkill -f "tsx server/index-sqlite.ts" 2>/dev/null || true
pkill -f "vite --config vite.config.network.ts" 2>/dev/null || true
sleep 2

# Create SQLite database if it doesn't exist
if [ ! -f "sqlite.db" ]; then
    echo -e "${YELLOW}🗄️ Initializing SQLite database...${NC}"
    touch sqlite.db
fi

# Set environment variables
export NODE_ENV=development
export DB_TYPE=sqlite
export PG_DISABLED=true
export SQLITE_PATH=./sqlite.db
export PORT=5000
export HOST=0.0.0.0
export SERVER_IP=$LOCAL_IP
export VITE_API_URL=http://$LOCAL_IP:5000
export VITE_SERVER_IP=$LOCAL_IP
export VITE_FRONTEND_PORT=5173
export VITE_BACKEND_PORT=5000
export CORS_ORIGIN=*
export ALLOW_NETWORK_ACCESS=true

# Function to cleanup processes on exit
cleanup() {
    echo -e "\n${YELLOW}🛑 Stopping servers...${NC}"
    pkill -f "tsx server/index-sqlite.ts" 2>/dev/null || true
    pkill -f "vite --config vite.config.network.ts" 2>/dev/null || true
    exit 0
}

# Trap signals for clean shutdown
trap cleanup SIGINT SIGTERM

# Start backend server
echo -e "${GREEN}🌐 Starting backend server on http://$LOCAL_IP:5000${NC}"
$NODE_PATH/npx tsx server/index-sqlite.ts &
BACKEND_PID=$!

# Wait for backend to start
echo -e "${YELLOW}⏳ Waiting for backend to start (8 seconds)...${NC}"
sleep 8

# Check if backend is running
if ! lsof -i:5000 &> /dev/null; then
    echo -e "${RED}⚠️ Backend server may not have started on port 5000${NC}"
    echo -e "${YELLOW}⏳ Waiting additional 5 seconds...${NC}"
    sleep 5
fi

# Start frontend server
echo -e "${GREEN}🖥️ Starting frontend server on http://$LOCAL_IP:5173${NC}"
$NODE_PATH/npx vite --config vite.config.network.ts &
FRONTEND_PID=$!

echo ""
echo -e "${GREEN}✅ Application started successfully on the local network!${NC}"
echo ""
echo -e "${CYAN}🌐 Access from this server:${NC}"
echo -e "${CYAN}   👉 Frontend: http://localhost:5173${NC}"
echo -e "${CYAN}   👉 Backend API: http://localhost:5000/api${NC}"
echo ""
echo -e "${BLUE}🌐 Access from other network devices:${NC}"
echo -e "${BLUE}   👉 Frontend: http://$LOCAL_IP:5173${NC}"
echo -e "${BLUE}   👉 Backend API: http://$LOCAL_IP:5000/api${NC}"
echo ""
echo -e "${YELLOW}📱 To access from phone/tablet:${NC}"
echo -e "${YELLOW}   Connect to same WiFi and open: http://$LOCAL_IP:5173${NC}"
echo ""
echo -e "${BLUE}========================================================${NC}"
echo -e "${YELLOW}Default accounts:${NC}"
echo -e "${YELLOW}  Admin: admin / admin123${NC}"
echo -e "${YELLOW}  Librarian: librarian / librarian123${NC}"
echo -e "${BLUE}========================================================${NC}"
echo -e "${RED}⛔ Press Ctrl+C to stop the application.${NC}"
echo -e "${BLUE}========================================================${NC}"

# Wait for processes to finish
wait $BACKEND_PID $FRONTEND_PID
