{"compilerOptions": {"target": "ES2018", "module": "CommonJS", "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "declaration": false, "downlevelIteration": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "outDir": "./dist/server-compiled", "paths": {"@/*": ["./client/src/*"], "@shared/*": ["./shared/*"], "@server/*": ["./server/*"]}, "baseUrl": "."}, "include": ["server/**/*.ts", "shared/**/*.ts"], "exclude": ["node_modules", "**/*.test.ts"]}