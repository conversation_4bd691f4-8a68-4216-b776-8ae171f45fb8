@echo off
setlocal EnableDelayedExpansion

REM Script pour démarrer l'application iQraa en mode développement local sous Windows
title iQraa - Mode Développement Local

echo ========================================================
echo 🚀 Démarrage de l'application iQraa en mode développement local
echo ========================================================

REM Vérification de l'environnement Node.js
echo 🔍 Vérification de l'environnement Node.js...
where node >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
  echo ❌ Node.js n'est pas installé ou n'est pas dans le PATH.
  echo Veuillez installer Node.js depuis https://nodejs.org/
  pause
  exit /b 1
)

REM Vérification des dépendances
if not exist "node_modules" (
  echo 📦 Installation des dépendances...
  call npm install
  if %ERRORLEVEL% NEQ 0 (
    echo ❌ Erreur lors de l'installation des dépendances.
    pause
    exit /b 1
  )
)

REM Création du fichier .env.local s'il n'existe pas
if not exist ".env.local" (
  echo 📄 Création du fichier .env.local...
  echo # Configuration de développement local pour iQraa> .env.local
  echo NODE_ENV=development>> .env.local
  
  echo # Forcer l'utilisation de SQLite uniquement>> .env.local
  echo DB_TYPE=sqlite>> .env.local
  echo SQLITE_PATH=sqlite.db>> .env.local
  echo PG_DISABLED=true>> .env.local
  
  echo # PostgreSQL désactivé en mode développement local>> .env.local
  
  echo # Identifiant client unique>> .env.local
  echo CLIENT_ID=iqraa-local-dev-%RANDOM%>> .env.local
  
  echo # Configuration de synchronisation>> .env.local
  echo SYNC_ENABLED=false>> .env.local
)

REM Arrêter les processus existants sur les ports 3000, 5000 et 5173 si nécessaire
echo 🔄 Nettoyage des ports...
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :3000') DO (
  echo  - Arrêt du processus sur le port 3000 (PID: %%P)
  TaskKill /PID %%P /F /T >NUL 2>&1
)
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :5000') DO (
  echo  - Arrêt du processus sur le port 5000 (PID: %%P)
  TaskKill /PID %%P /F /T >NUL 2>&1
)
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :5173') DO (
  echo  - Arrêt du processus sur le port 5173 (PID: %%P)
  TaskKill /PID %%P /F /T >NUL 2>&1
)

REM Vérifier si SQLite est disponible
if not exist "sqlite.db" (
  echo 🗃️ Initialisation de la base de données SQLite...
  call npx tsx server/init-sqlite-db.ts
  if %ERRORLEVEL% NEQ 0 (
    echo ❌ Erreur lors de l'initialisation de la base de données.
    pause
    exit /b 1
  )
)

REM Vérifier les erreurs de compilation TypeScript
echo 🔧 Vérification des erreurs TypeScript...
call npx tsc --noEmit
if %ERRORLEVEL% NEQ 0 (
  echo ⚠️ Des erreurs TypeScript ont été détectées.
  echo Continuez quand même? (O/N)
  choice /C ON /N
  if errorlevel 2 (
    echo ✅ Continuer malgré les erreurs.
  ) else (
    exit /b 1
  )
)

echo ✨ Démarrage des serveurs...

REM Utiliser local-dev.js si disponible, sinon script shell
if exist "scripts\local-dev.js" (
  echo 🔧 Utilisation du script Node.js unifié...
  start "iQraa Backend+Frontend" cmd /c "npx cross-env NODE_ENV=development tsx scripts/local-dev.js"
) else (
  echo 🔧 Utilisation du mode shell direct...
  
  echo 🌐 Démarrage du serveur backend sur http://localhost:5000
  start "iQraa Backend" cmd /c "npx cross-env NODE_ENV=development DB_TYPE=sqlite PG_DISABLED=true SQLITE_PATH=./sqlite.db npx tsx server/index-sqlite.ts"
  
  echo ⏳ Attente du démarrage du backend...
  timeout /t 3 /nobreak > nul
  
  echo 🖥️ Démarrage du serveur frontend sur http://localhost:5173
  start "iQraa Frontend" cmd /c "npx cross-env NODE_ENV=development DB_TYPE=sqlite PG_DISABLED=true npx vite"
)

echo.
echo ✅ Serveurs démarrés avec succès
echo 👉 Accédez à l'application via: http://localhost:5173
echo 📊 API disponible sur: http://localhost:5000/api
echo.
echo 💾 Mode SQLite uniquement (PostgreSQL désactivé en local)
echo 🔄 Synchronisation disponible mais désactivée par défaut
echo.
echo ⛔ Pour arrêter les serveurs, fermez cette fenêtre ou appuyez sur Ctrl+C
echo.
echo 📝 Logs disponibles dans les fenêtres de commande ouvertes.
echo ========================================================

REM Garder la fenêtre ouverte pour la visibilité du processus
pause > nul

echo.
echo 🛑 Arrêt des serveurs...
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :3000') DO TaskKill /PID %%P /F /T >NUL 2>&1
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :5000') DO TaskKill /PID %%P /F /T >NUL 2>&1
FOR /F "tokens=5" %%P IN ('netstat -a -n -o ^| findstr :5173') DO TaskKill /PID %%P /F /T >NUL 2>&1
echo ✅ Serveurs arrêtés
timeout /t 2 /nobreak > nul