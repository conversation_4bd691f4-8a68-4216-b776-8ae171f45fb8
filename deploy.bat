@echo off
REM Script principal de déploiement pour iQraa (Windows)

:menu
cls
echo ==================================
echo Application iQraa - Gestionnaire de déploiement
echo ==================================
echo 1. Démarrer en mode développement local (Windows)
echo 2. Déployer vers CapRover (version en ligne)
echo 3. Construire pour Electron (version bureau)
echo 4. Tester avec Electron (développement)
echo 5. Construire pour Capacitor (version mobile)
echo 6. Tester avec Capacitor (développement)
echo 7. Afficher la documentation
echo 8. Installer les dépendances (résoudre erreurs d'installation)
echo 9. Nettoyer les anciens fichiers de déploiement
echo 10. Configurer l'URL de l'API de synchronisation
echo 0. Quitter
echo ==================================
echo Documentation : README-Deployment.md
echo ==================================

set /p choice=Choisissez une option (0-10): 

if "%choice%"=="1" (
  echo Démarrage en mode développement local (Windows)...
  call deployment\local\start.bat
  goto end
)

if "%choice%"=="2" (
  echo Déploiement vers CapRover...
  call deployment\caprover\deploy.bat  
  goto end
)

if "%choice%"=="3" (
  echo Construction pour Electron...
  call deployment\electron\build.bat
  goto end
)

if "%choice%"=="4" (
  echo Test avec Electron...
  call deployment\electron\test.bat
  goto end
)

if "%choice%"=="5" (
  echo Construction pour Capacitor...
  call deployment\capacitor\build.bat
  goto end
)

if "%choice%"=="6" (
  echo Test avec Capacitor...
  call deployment\capacitor\test.bat
  goto end
)

if "%choice%"=="7" (
  echo Affichage de la documentation...
  type README-Deployment.md | more
  echo.
  echo Pour plus de détails, consultez également :
  echo - deployment\DOCUMENTATION.md
  echo - deployment\README.md
  echo - deployment\caprover\README.md
  echo - deployment\electron\README.md
  echo - deployment\capacitor\README.md
  echo - deployment\local\README.md
  goto end
)

if "%choice%"=="8" (
  echo Installation des dépendances...
  call deployment\local\install-dependencies.bat
  goto end
)

if "%choice%"=="9" (
  echo Nettoyage des anciens fichiers de déploiement...
  call cleanup-deployment.bat
  goto end
)

if "%choice%"=="10" (
  echo Configuration de l'URL de l'API de synchronisation...
  call deployment\configure-api.bat
  goto end
)

if "%choice%"=="0" (
  echo Au revoir!
  exit /b 0
)

echo Option invalide. Veuillez choisir une option entre 0 et 10.

:end
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
goto menu