{"name": "iqraa-capacitor", "version": "1.0.0", "description": "Application de gestion de bibliothèque iQraa (Version mobile avec Capacitor)", "type": "module", "license": "MIT", "scripts": {"cap:sync": "npx cap sync", "cap:add:android": "npx cap add android", "cap:build:android": "npx cap copy android && cd android && ./gradlew build", "cap:open:android": "npx cap open android", "cap:run:android": "npx cap run android", "cap:add:ios": "npx cap add ios", "cap:open:ios": "npx cap open ios", "cap:run:ios": "npx cap run ios", "build": "vite build"}, "dependencies": {"@capacitor/android": "^7.2.0", "@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/ios": "^7.2.0", "@capacitor/preferences": "^7.2.0", "@capacitor/splash-screen": "^7.2.0", "@capacitor/status-bar": "^7.2.0", "better-sqlite3": "^11.9.1", "connect-sqlite3": "^0.9.15", "express": "^4.21.2", "express-session": "^1.18.1", "dotenv": "^16.5.0", "dotenv-expand": "^12.0.2", "drizzle-orm": "^0.39.3", "node-fetch": "^3.3.2", "passport": "^0.7.0", "passport-local": "^1.0.0"}, "devDependencies": {"@capacitor/assets": "^3.0.4", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "vite": "^5.4.14"}}