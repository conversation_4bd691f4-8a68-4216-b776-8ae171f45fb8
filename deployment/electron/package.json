{"name": "iqraa-electron", "version": "1.0.0", "description": "Application de gestion de bibliothèque iQraa (Version bureau avec Electron)", "main": "electron-wrapper.js", "type": "commonjs", "license": "MIT", "scripts": {"start": "electron .", "build": "electron-builder", "dev": "node electron-dev-commonjs.js"}, "build": {"appId": "com.iqraa.app", "productName": "iQraa", "directories": {"output": "electron-dist"}, "files": ["dist/**/*", "electron-wrapper.js", "public/**/*", "node_modules/**/*", "package.json"], "win": {"target": "nsis", "icon": "public/icon.ico"}, "linux": {"target": "AppImage", "icon": "public/icon.png"}, "mac": {"target": "dmg", "icon": "public/icon.icns"}}, "dependencies": {"better-sqlite3": "^11.9.1", "connect-sqlite3": "^0.9.15", "cors": "^2.8.5", "express": "^4.21.2", "express-session": "^1.18.1", "dotenv": "^16.5.0", "dotenv-expand": "^12.0.2", "drizzle-orm": "^0.39.3", "passport": "^0.7.0", "passport-local": "^1.0.0", "electron-is-dev": "^2.0.0", "electron-serve": "^1.2.0"}, "devDependencies": {"electron": "^36.2.0", "electron-builder": "^26.0.12", "wait-on": "^7.2.0"}}