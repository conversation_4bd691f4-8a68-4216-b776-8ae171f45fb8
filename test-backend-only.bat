@echo off
echo ========================================================
echo 🔧 Testing Backend Server Only
echo ========================================================

echo 📋 Environment Variables:
set NODE_ENV=development
set DB_TYPE=sqlite
set PG_DISABLED=true
set SQLITE_PATH=./sqlite.db
set HOST=0.0.0.0
set PORT=5000
set SERVER_IP=***********
set CORS_ORIGIN=*
set ALLOW_NETWORK_ACCESS=true

echo   NODE_ENV: %NODE_ENV%
echo   DB_TYPE: %DB_TYPE%
echo   HOST: %HOST%
echo   PORT: %PORT%
echo   SERVER_IP: %SERVER_IP%

echo.
echo 🔍 Checking if SQLite database exists...
if exist "sqlite.db" (
    echo ✅ SQLite database found
) else (
    echo ❌ SQLite database not found - this might be the issue!
)

echo.
echo 🔍 Checking if server files exist...
if exist "server\index-sqlite.ts" (
    echo ✅ Server file found
) else (
    echo ❌ Server file not found!
)

echo.
echo 🚀 Starting backend server...
echo Command: npx tsx server/index-sqlite.ts

npx tsx server/index-sqlite.ts

echo.
echo 🛑 Server stopped. Check the output above for errors.
pause
