@echo off
REM Script principal de deploiement pour iQraa (Windows)

:menu
cls
echo ==================================
echo Application iQraa - Gestionnaire de deploiement
echo ==================================
echo 1. Demarrer en mode developpement local (Windows)
echo 2. Deployer vers CapRover (version en ligne)
echo 3. Construire pour Electron (version bureau)
echo 4. Tester avec Electron (developpement)
echo 5. Construire pour Capacitor (version mobile)
echo 6. Tester avec Capacitor (developpement)
echo 7. Afficher la documentation
echo 8. Installer les dependances (resoudre erreurs d'installation)
echo 9. Nettoyer les anciens fichiers de deploiement
echo 10. Configurer l'URL de l'API de synchronisation
echo 0. Quitter
echo ==================================
echo Documentation : README-Deployment.md
echo ==================================

set /p choice=Choisissez une option (0-10): 

if "%choice%"=="1" (
  echo Demarrage en mode developpement local (Windows)...
  call deployment\local\start.bat
  goto end
)

if "%choice%"=="2" (
  echo Deploiement vers CapRover...
  call deployment\caprover\deploy-fixed.bat  
  goto end
)

if "%choice%"=="3" (
  echo Construction pour Electron...
  call deployment\electron\build.bat
  goto end
)

if "%choice%"=="4" (
  echo Test avec Electron...
  call deployment\electron\test-fixed.bat
  goto end
)

if "%choice%"=="5" (
  echo Construction pour Capacitor...
  call deployment\capacitor\build.bat
  goto end
)

if "%choice%"=="6" (
  echo Test avec Capacitor...
  call deployment\capacitor\test.bat
  goto end
)

if "%choice%"=="7" (
  echo Affichage de la documentation...
  type README-Deployment.md | more
  echo.
  echo Pour plus de details, consultez egalement :
  echo - deployment\DOCUMENTATION.md
  echo - deployment\README.md
  echo - deployment\caprover\README.md
  echo - deployment\electron\README.md
  echo - deployment\capacitor\README.md
  echo - deployment\local\README.md
  goto end
)

if "%choice%"=="8" (
  echo Installation des dependances...
  call deployment\local\install-dependencies.bat
  goto end
)

if "%choice%"=="9" (
  echo Nettoyage des anciens fichiers de deploiement...
  call cleanup-deployment.bat
  goto end
)

if "%choice%"=="10" (
  echo Configuration de l'URL de l'API de synchronisation...
  call deployment\configure-api-fixed.bat
  goto end
)

if "%choice%"=="0" (
  echo Au revoir!
  exit /b 0
)

echo Option invalide. Veuillez choisir une option entre 0 et 10.

:end
echo.
echo Appuyez sur une touche pour continuer...
pause > nul
goto menu