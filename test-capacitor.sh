#!/bin/bash

# Script pour tester l'application mobile sans build complet
echo "Préparation de l'environnement de test Capacitor pour Android..."

# Basculer vers la configuration Capacitor
./switch-deployment.sh capacitor

# Installation des dépendances
echo "Installation des dépendances..."
npm install

# Utiliser le script de développement Capacitor existant
echo "Utilisation du script de développement Capacitor existant..."

# Lancer le script de développement Capacitor
echo "Démarrage du mode développement Capacitor..."
node scripts/capacitor-dev.js &
DEV_PID=$!

# Fonction de nettoyage
function cleanup() {
  echo "Arrêt des processus..."
  kill $DEV_PID 2>/dev/null
  exit 0
}

# Attraper les signaux pour un arrêt propre
trap cleanup SIGINT SIGTERM

echo "Application mobile lancée en mode test."
echo "Appuyez sur Ctrl+C pour terminer."

# Attendre indéfiniment
wait $DEV_PID
cleanup