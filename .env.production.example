# Exemple de configuration .env.production pour CapRover
# Copiez ce fichier en .env.production et modifiez les valeurs selon votre environnement

# Configuration d'environnement
NODE_ENV=production
DB_TYPE=postgresql
PORT=3000

# Configuration PostgreSQL - Remplacez les valeurs par vos informations
DATABASE_URL=postgres://iqraa_user:mot_de_passe_sécurisé@srv-captain--iqraa-postgres:5432/iqraa_db

# Configuration de sécurité - Générez une chaîne aléatoire pour la sécurité des sessions
SESSION_SECRET=chaîne_aléatoire_longue_et_sécurisée

# Configuration de l'application
APP_NAME=iQraa
APP_URL=https://iqraa.example.com
APP_ADMIN_EMAIL=<EMAIL>

# Autres paramètres
TZ=UTC
DEFAULT_LANGUAGE=fr