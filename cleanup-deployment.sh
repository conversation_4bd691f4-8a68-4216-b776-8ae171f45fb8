#!/bin/bash
# Script pour nettoyer les anciens fichiers de déploiement

# Couleurs pour la sortie
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}==================================${NC}"
echo -e "${GREEN}Nettoyage des anciens fichiers de déploiement${NC}"
echo -e "${YELLOW}==================================${NC}"

# Liste des anciens scripts de déploiement à supprimer
OLD_SCRIPTS=(
  "switch-deployment.sh"
  "build-electron.sh"
  "test-capacitor.sh"
  "start-sqlite.sh"
  "start-local.sh"
  "start-local.bat"
  "test-electron.sh"
  "new-test-capacitor.sh"
  "fix-dependencies.sh"
  "start-windows.bat"
  "fix-windows-dependencies.bat"
  "new-test-electron.sh"
  "deploy-caprover.sh"
  "new-test-electron.bat"
  "new-start-local.sh"
  "new-start-local.bat"
  "build-capacitor.sh"
  "test-migrations.sh"
  "test-migrations.bat"
)

# Liste des anciennes configurations à supprimer
OLD_CONFIGS=(
  ".npmrc.capacitor"
  ".npmrc.electron"
  ".npmrc.caprover"
)

# Création d'un répertoire d'archive pour sauvegarder les anciens fichiers
mkdir -p .deployment_archive

# Déplacement des anciens scripts vers l'archive
echo -e "${YELLOW}Déplacement des anciens scripts de déploiement...${NC}"
for script in "${OLD_SCRIPTS[@]}"; do
  if [ -f "$script" ]; then
    echo "Archivage de $script"
    mv "$script" .deployment_archive/
  fi
done

# Déplacement des anciennes configurations vers l'archive
echo -e "${YELLOW}Déplacement des anciennes configurations...${NC}"
for config in "${OLD_CONFIGS[@]}"; do
  if [ -f "$config" ]; then
    echo "Archivage de $config"
    mv "$config" .deployment_archive/
  fi
done

echo -e "${GREEN}Nettoyage terminé avec succès!${NC}"
echo -e "${YELLOW}Les anciens fichiers ont été déplacés dans le répertoire .deployment_archive${NC}"
echo -e "${YELLOW}Si vous rencontrez des problèmes avec la nouvelle structure, vous pouvez les restaurer depuis ce répertoire.${NC}"