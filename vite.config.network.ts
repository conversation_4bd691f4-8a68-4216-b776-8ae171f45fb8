import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

// Configuration Vite optimisée pour l'accès réseau local
export default defineConfig({
  plugins: [
    react()
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "client", "src"),
      "@shared": path.resolve(__dirname, "shared"),
      "@assets": path.resolve(__dirname, "attached_assets"),
    },
  },
  root: path.resolve(__dirname, "client"),
  build: {
    outDir: path.resolve(__dirname, "dist/public"),
    emptyOutDir: true,
  },
  server: {
    port: parseInt(process.env.VITE_FRONTEND_PORT || '5173'),
    host: '0.0.0.0', // Allow connections from any IP on the network
    strictPort: true, // Fail if port is already in use
    proxy: {
      // Proxy API calls to the backend server
      '/api': {
        target: process.env.VITE_API_URL || `http://${process.env.VITE_SERVER_IP || 'localhost'}:${process.env.VITE_BACKEND_PORT || '5000'}`,
        changeOrigin: true,
        secure: false,
        ws: true, // Enable WebSocket proxying
        configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        },
      }
    }
  },
  define: {
    // Define environment variables for the client
    'import.meta.env.VITE_API_URL': JSON.stringify(
      process.env.VITE_API_URL || 
      `http://${process.env.VITE_SERVER_IP || 'localhost'}:${process.env.VITE_BACKEND_PORT || '5000'}`
    ),
    'import.meta.env.VITE_DB_TYPE': JSON.stringify('sqlite'),
    'import.meta.env.VITE_PG_DISABLED': JSON.stringify('true'),
    'import.meta.env.VITE_SYNC_ENABLED': JSON.stringify('false'),
    'import.meta.env.VITE_IS_OFFLINE_FIRST': JSON.stringify('true'),
    'import.meta.env.VITE_DEFAULT_LANGUAGE': JSON.stringify('fr'),
    'import.meta.env.VITE_SERVER_IP': JSON.stringify(process.env.VITE_SERVER_IP || 'localhost'),
    'import.meta.env.VITE_FRONTEND_PORT': JSON.stringify(process.env.VITE_FRONTEND_PORT || '5173'),
    'import.meta.env.VITE_BACKEND_PORT': JSON.stringify(process.env.VITE_BACKEND_PORT || '5000'),
    'import.meta.env.VITE_NETWORK_MODE': JSON.stringify('true'),
  },
  preview: {
    port: parseInt(process.env.VITE_FRONTEND_PORT || '5173'),
    host: '0.0.0.0',
    strictPort: true,
  }
});
