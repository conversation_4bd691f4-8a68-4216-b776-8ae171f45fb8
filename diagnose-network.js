#!/usr/bin/env node

/**
 * Diagnostic script to check network configuration and server status
 */

import fs from 'fs';
import http from 'http';
import { spawn } from 'child_process';

console.log('🔍 IQRAA Manager Network Diagnostics');
console.log('=====================================');

// Check .env.local file
console.log('\n📋 Checking .env.local configuration...');
if (fs.existsSync('.env.local')) {
  const envContent = fs.readFileSync('.env.local', 'utf8');
  const lines = envContent.split('\n').filter(line => 
    line.includes('SERVER_IP') || 
    line.includes('API_URL') || 
    line.includes('VITE_API_URL') ||
    line.includes('HOST') ||
    line.includes('PORT')
  );
  
  console.log('✅ .env.local exists');
  lines.forEach(line => {
    if (line.trim() && !line.startsWith('#')) {
      console.log(`   ${line.trim()}`);
    }
  });
} else {
  console.log('❌ .env.local not found');
}

// Check if backend server is running
console.log('\n🔍 Checking backend server status...');

function checkServer(host, port) {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: host,
      port: port,
      path: '/health',
      method: 'GET',
      timeout: 3000
    }, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({ success: true, status: res.statusCode, data });
      });
    });

    req.on('error', (err) => {
      resolve({ success: false, error: err.message });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({ success: false, error: 'Timeout' });
    });

    req.end();
  });
}

// Test different server addresses
const testAddresses = [
  { name: 'Localhost IPv4', host: '127.0.0.1', port: 5000 },
  { name: 'Localhost IPv6', host: '::1', port: 5000 },
  { name: 'Network IP', host: '***********', port: 5000 },
  { name: 'All interfaces', host: '0.0.0.0', port: 5000 }
];

async function runDiagnostics() {
  for (const addr of testAddresses) {
    console.log(`\n🧪 Testing ${addr.name} (${addr.host}:${addr.port})...`);
    const result = await checkServer(addr.host, addr.port);
    
    if (result.success) {
      console.log(`✅ Server responding on ${addr.host}:${addr.port}`);
      console.log(`   Status: ${result.status}`);
      if (result.data) {
        try {
          const parsed = JSON.parse(result.data);
          console.log(`   Response: ${JSON.stringify(parsed, null, 2)}`);
        } catch {
          console.log(`   Response: ${result.data.substring(0, 100)}...`);
        }
      }
    } else {
      console.log(`❌ Cannot connect to ${addr.host}:${addr.port}`);
      console.log(`   Error: ${result.error}`);
    }
  }

  // Check if any Node.js processes are running
  console.log('\n🔍 Checking for running Node.js processes...');
  
  const { exec } = await import('child_process');
  exec('tasklist | findstr node', (error, stdout, stderr) => {
    if (stdout) {
      console.log('✅ Found Node.js processes:');
      console.log(stdout);
    } else {
      console.log('❌ No Node.js processes found');
    }
  });

  // Check ports in use
  console.log('\n🔍 Checking ports 5000 and 5173...');
  exec('netstat -an | findstr ":5000\\|:5173"', (error, stdout, stderr) => {
    if (stdout) {
      console.log('✅ Ports in use:');
      console.log(stdout);
    } else {
      console.log('❌ Ports 5000 and 5173 are not in use');
    }
  });

  console.log('\n📋 Diagnostic Summary:');
  console.log('1. Check if backend server is actually running');
  console.log('2. Verify environment variables are set correctly');
  console.log('3. Ensure no firewall is blocking the connections');
  console.log('4. Try starting backend manually with: npm run dev:network');
}

runDiagnostics().catch(console.error);
