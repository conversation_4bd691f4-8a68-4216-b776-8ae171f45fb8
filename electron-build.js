const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Script de build Electron pour iQraa');
console.log('------------------------------------');

// Vérifier si le dossier dist existe déjà et le supprimer si c'est le cas
if (fs.existsSync(path.join(__dirname, 'dist'))) {
  console.log('Suppression du dossier dist existant...');
  fs.rmSync(path.join(__dirname, 'dist'), { recursive: true, force: true });
}

// Créer le dossier dist
console.log('Création du dossier dist...');
fs.mkdirSync(path.join(__dirname, 'dist'), { recursive: true });

// Copier les fichiers HTML, CSS et JS depuis client
console.log('Copie des fichiers client...');
fs.cpSync(path.join(__dirname, 'client'), path.join(__dirname, 'dist'), { 
  recursive: true,
  filter: (src) => !src.includes('node_modules')
});

// Compiler le serveur
console.log('Compilation du serveur...');
try {
  execSync('npx esbuild server/index-sqlite.ts --platform=node --packages=external --bundle --format=cjs --outfile=dist/server.js', { 
    stdio: 'inherit' 
  });
  console.log('✓ Le serveur a été compilé avec succès');
} catch (error) {
  console.error('❌ Erreur lors de la compilation du serveur:', error.message);
  process.exit(1);
}

// Créer un fichier index.html basique dans dist
console.log('Création du fichier index.html...');
fs.writeFileSync(path.join(__dirname, 'dist', 'index.html'), `
<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>iQraa - Gestion de bibliothèque</title>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
      color: #333;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      padding: 20px;
    }
    h1 {
      color: #166534;
      margin-top: 0;
    }
    .logo {
      max-width: 150px;
      margin-bottom: 20px;
    }
    .loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 0;
    }
    .spinner {
      border: 4px solid rgba(0, 0, 0, 0.1);
      width: 36px;
      height: 36px;
      border-radius: 50%;
      border-left-color: #166534;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="container">
    <img src="./public/assets/iqraa.png" alt="iQraa Logo" class="logo">
    <h1>iQraa - Gestion de bibliothèque</h1>
    <div class="loading">
      <div class="spinner"></div>
      <p>Chargement de l'application...</p>
      <p><small>L'interface complète est en cours de chargement, veuillez patienter.</small></p>
    </div>
  </div>
  <script>
    // Redirection vers l'application après quelques secondes
    setTimeout(() => {
      window.location.href = 'http://localhost:5000';
    }, 2000);
  </script>
</body>
</html>
`);

// Copier les assets (iqraa.png et favicon)
console.log('Copie des assets...');
try {
  if (!fs.existsSync(path.join(__dirname, 'dist', 'public'))) {
    fs.mkdirSync(path.join(__dirname, 'dist', 'public'), { recursive: true });
  }
  if (!fs.existsSync(path.join(__dirname, 'dist', 'public', 'assets'))) {
    fs.mkdirSync(path.join(__dirname, 'dist', 'public', 'assets'), { recursive: true });
  }
  
  // Copier les assets depuis attached_assets si disponibles
  if (fs.existsSync(path.join(__dirname, 'attached_assets', 'iqraa.png'))) {
    fs.copyFileSync(
      path.join(__dirname, 'attached_assets', 'iqraa.png'),
      path.join(__dirname, 'dist', 'public', 'assets', 'iqraa.png')
    );
  }
  
  if (fs.existsSync(path.join(__dirname, 'attached_assets', 'favicon.png'))) {
    fs.copyFileSync(
      path.join(__dirname, 'attached_assets', 'favicon.png'),
      path.join(__dirname, 'dist', 'public', 'assets', 'favicon.png')
    );
  }
  
  console.log('✓ Assets copiés avec succès');
} catch (error) {
  console.error('❌ Erreur lors de la copie des assets:', error.message);
  // Ne pas quitter, ce n'est pas critique
}

// Créer un fichier de configuration pour Electron
console.log('Création du fichier de configuration Electron...');
fs.writeFileSync(path.join(__dirname, 'dist', 'electron-config.json'), JSON.stringify({
  mainWindow: {
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    title: "iQraa - Gestion de bibliothèque",
    icon: "./public/assets/favicon.png",
    backgroundColor: "#ffffff"
  },
  server: {
    port: 5000,
    host: "localhost"
  },
  api: {
    // URL par défaut du serveur central en production
    syncEndpoint: "https://api.iqraa-library.com", 
    // Cette valeur peut être modifiée par l'utilisateur dans les paramètres
    enableSync: true
  },
  database: {
    // Toujours utiliser SQLite en mode desktop
    mode: "offline",
    syncInterval: 30 * 60 * 1000 // 30 minutes par défaut
  },
  settings: {
    checkForUpdates: true,
    language: "fr",
    theme: "light"
  }
}, null, 2));

console.log('✓ Build Electron terminé avec succès!');
console.log('------------------------------------');
console.log('Vous pouvez maintenant lancer l\'application avec:');
console.log('NODE_ENV=production node dist/server.js');
console.log('NODE_ENV=production npx electron electron-wrapper.js');