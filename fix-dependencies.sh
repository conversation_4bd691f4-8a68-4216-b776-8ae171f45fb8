#!/bin/bash
# Script pour résoudre les problèmes de dépendances dans l'application iQraa
# À utiliser quand vous rencontrez des erreurs du type "Cannot find package 'vite'" ou "Le binaire TSX n'a pas été trouvé"

echo "🔧 Résolution des problèmes de dépendances pour iQraa..."

# Vérifier si nous sommes sous Windows
IS_WINDOWS=false
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" || "$OSTYPE" == "cygwin" ]]; then
  IS_WINDOWS=true
  echo "📌 Système Windows détecté"
else
  echo "📌 Système Unix/Linux/Mac détecté"
fi

# Sauvegarder le répertoire actuel
CURRENT_DIR=$(pwd)

# Fonction pour vérifier si une commande existe
command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# Fonction pour installer une dépendance
install_dependency() {
  local pkg=$1
  echo "📦 Installation de $pkg..."
  npm install $pkg --save-dev
  
  # Vérifier si l'installation a réussi
  if [ $? -ne 0 ]; then
    echo "❌ Échec de l'installation de $pkg. Tentative d'installation globale..."
    npm install -g $pkg
  else
    echo "✅ $pkg installé avec succès"
  fi
}

echo "🔍 Vérification des dépendances essentielles..."

# Vérifier et installer les dépendances manquantes
DEPS_TO_CHECK=("vite" "tsx" "cross-env" "esbuild")

for dep in "${DEPS_TO_CHECK[@]}"; do
  if ! command_exists $dep && ! [ -f "./node_modules/.bin/$dep" ]; then
    echo "⚠️ Dépendance manquante: $dep"
    install_dependency $dep
  else
    echo "✅ $dep est déjà installé"
  fi
done

# Réinitialiser le cache npm si demandé
read -p "Voulez-vous réinitialiser le cache npm? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
  echo "🧹 Réinitialisation du cache npm..."
  npm cache clean --force
  echo "✅ Cache npm réinitialisé"
fi

# Vérifier les chemins problématiques sous Windows
if [ "$IS_WINDOWS" = true ]; then
  echo "🔍 Vérification des chemins Windows problématiques..."
  
  # Vérifier si Node.js est installé dans un chemin contenant des espaces
  NODE_PATH=$(which node)
  if [[ $NODE_PATH == *" "* ]]; then
    echo "⚠️ ATTENTION: Node.js est installé dans un chemin contenant des espaces: $NODE_PATH"
    echo "👉 Conseil: Pensez à réinstaller Node.js dans un chemin sans espaces (ex: C:\\nodejs)"
  else
    echo "✅ Le chemin d'installation de Node.js ne contient pas d'espaces"
  fi
fi

# Vérifier l'existence des scripts d'exécution
echo "🔍 Vérification des permissions des scripts..."
if [ -f "./new-start-local.sh" ]; then
  chmod +x ./new-start-local.sh
  echo "✅ Permissions ajoutées à new-start-local.sh"
fi

if [ -f "./new-test-electron.sh" ]; then
  chmod +x ./new-test-electron.sh
  echo "✅ Permissions ajoutées à new-test-electron.sh"
fi

if [ -f "./new-test-capacitor.sh" ]; then
  chmod +x ./new-test-capacitor.sh
  echo "✅ Permissions ajoutées à new-test-capacitor.sh"
fi

echo "✨ Vérification terminée!"
echo ""
echo "👉 Si vous avez toujours des problèmes:"
echo "  1. Essayez d'exécuter 'npm ci' pour réinstaller toutes les dépendances"
echo "  2. Référez-vous au guide fix-windows-paths.md pour plus d'informations"
echo "  3. Utilisez les scripts améliorés avec préfixe 'new-' (ils sont plus robustes)"
echo ""
echo "🎉 Bonne utilisation d'iQraa!"