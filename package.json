{"name": "iqraa-local", "version": "1.0.0", "type": "module", "license": "MIT", "description": "Application de gestion de bibliothèque iQraa (Développement local)", "scripts": {"dev": "NODE_ENV=development tsx server/index.ts", "dev:local": "cross-env NODE_ENV=development DB_TYPE=sqlite PG_DISABLED=true tsx server/index-sqlite.ts", "dev:frontend": "cross-env NODE_ENV=development vite --config vite-local.config.ts", "dev:full": "concurrently \"npm run dev:local\" \"npm run dev:frontend\"", "dev:network": "cross-env NODE_ENV=development DB_TYPE=sqlite PG_DISABLED=true HOST=0.0.0.0 CORS_ORIGIN=* tsx server/index-sqlite.ts", "dev:network-frontend": "cross-env VITE_API_URL=http://***********:5000 VITE_SERVER_IP=*********** vite --config vite.config.network.ts", "dev:network-full": "concurrently \"npm run dev:network\" \"npm run dev:network-frontend\"", "dev:network-backend": "npm run dev:network", "dev:network-frontend-only": "npm run dev:network-frontend", "build": "vite build && esbuild server/index.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "build:local": "vite --config vite-local.config.ts build && esbuild server/index-sqlite.ts --platform=node --packages=external --bundle --format=esm --outdir=dist", "start": "NODE_ENV=development node dist/index.js", "start:local": "NODE_ENV=development DB_TYPE=sqlite PG_DISABLED=true PORT=5000 tsx server/index-sqlite.ts", "setup:network": "node scripts/setup-network.js", "test:local": "node test-local-server.js", "check": "tsc", "db:push": "drizzle-kit push"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@neondatabase/serverless": "^0.10.4", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@tailwindcss/vite": "^4.1.3", "@tanstack/react-query": "^5.60.5", "@types/better-sqlite3": "^7.6.13", "@types/connect-sqlite3": "^0.9.5", "@types/cors": "^2.8.17", "better-sqlite3": "^11.9.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "connect-pg-simple": "^10.0.0", "connect-sqlite3": "^0.9.15", "cors": "^2.8.5", "cross-env": "^7.0.3", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "dotenv-expand": "^12.0.2", "drizzle-orm": "^0.39.3", "drizzle-zod": "^0.7.0", "embla-carousel-react": "^8.6.0", "express": "^4.21.2", "express-session": "^1.18.1", "framer-motion": "^11.13.1", "i18next": "^25.0.2", "input-otp": "^1.4.2", "lucide-react": "^0.453.0", "memorystore": "^1.6.7", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "nodemon": "^3.1.10", "passport": "^0.7.0", "passport-local": "^1.0.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "react-i18next": "^15.5.1", "react-icons": "^5.4.0", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "vaul": "^1.1.2", "wouter": "^3.3.5", "ws": "^8.18.2", "xlsx": "^0.18.5", "zod": "^3.24.2", "zod-validation-error": "^3.4.0"}, "devDependencies": {"@replit/vite-plugin-cartographer": "^0.1.2", "@replit/vite-plugin-runtime-error-modal": "^0.0.3", "@tailwindcss/typography": "^0.5.15", "@types/connect-pg-simple": "^7.0.3", "@types/express": "4.17.21", "@types/express-session": "^1.18.0", "@types/node": "20.16.11", "@types/passport": "^1.0.16", "@types/passport-local": "^1.0.38", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@types/ws": "^8.5.13", "@vitejs/plugin-react": "^4.3.2", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "drizzle-kit": "^0.30.4", "esbuild": "^0.25.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.17", "tsx": "^4.19.4", "typescript": "5.6.3", "vite": "^5.4.14"}}