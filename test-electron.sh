#!/bin/bash

# Script pour tester l'application Electron sans build complet
echo "Préparation de l'environnement de test Electron..."

# Basculer vers la configuration Electron
./switch-deployment.sh electron

# Installation des dépendances avec support explicite pour les modules natifs
echo "Installation des dépendances avec support pour les modules natifs..."
npm install --include=optional

# Installation explicite des dépendances natives de Rollup pour éviter les erreurs
echo "Installation des dépendances natives de Rollup..."
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" || "$OSTYPE" == "cygwin" ]]; then
  # Windows
  npm install @rollup/rollup-win32-x64-msvc --no-save
elif [[ "$OSTYPE" == "darwin"* ]]; then
  # macOS
  npm install @rollup/rollup-darwin-x64 --no-save
else
  # Linux
  npm install @rollup/rollup-linux-x64-gnu --no-save
  # Pour les systèmes Linux alternatifs
  npm install @rollup/rollup-linux-x64-musl --no-save
fi

# Lance<PERSON> le script de développement Electron
echo "Démarrage du mode développement Electron (sans build complet)..."
node -e "
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const isWindows = process.platform === 'win32';
const tsxBin = isWindows ? '\\\"node_modules\\\\.bin\\\\tsx.cmd\\\"' : '\\\"./node_modules/.bin/tsx\\\"';

// Vérifier l'existence du binaire tsx
const tsxPath = isWindows ? 'node_modules\\\\.bin\\\\tsx.cmd' : './node_modules/.bin/tsx';
const binPath = path.resolve(process.cwd(), tsxPath.replace(/\\\\/g, '\\\\'));
if (!fs.existsSync(binPath)) {
  console.error('\\x1b[31m❌ Erreur: Le binaire TSX n\\'a pas été trouvé\\x1b[0m');
  console.log('👉 Solution: Exécutez \"npm install tsx\" et réessayez');
  process.exit(1);
}

console.log('Utilisation du binaire TSX:', tsxBin);

// Utiliser différentes méthodes selon l'OS
let proc;
if (isWindows) {
  // Sous Windows, utiliser shell:true et cmd /c pour assurer la compatibilité
  proc = spawn('cmd', ['/c', 'node', tsxBin, 'electron-dev.js'], {
    stdio: 'inherit',
    shell: true
  });
} else {
  proc = spawn('node', [tsxBin.replace(/\\\"/g, ''), 'electron-dev.js'], {
    stdio: 'inherit'
  });
}

proc.on('error', (err) => {
  console.error('\\x1b[31mErreur:', err, '\\x1b[0m');
  process.exit(1);
});
" &
DEV_PID=$!

# Fonction de nettoyage
function cleanup() {
  echo "Arrêt des processus..."
  kill $DEV_PID 2>/dev/null
  exit 0
}

# Attraper les signaux pour un arrêt propre
trap cleanup SIGINT SIGTERM

echo "Application Electron lancée en mode test."
echo "Appuyez sur Ctrl+C pour terminer."

# Attendre indéfiniment
wait $DEV_PID
cleanup