// Fichier JavaScript CommonJS minimal pour Electron
const { app, BrowserWindow } = require('electron');
const path = require('path');

// Stocker la référence globale à l'objet window
let mainWindow;

function createWindow() {
  // Créer une fenêtre de navigateur
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: false
    }
  });

  // Charger un simple fichier HTML
  mainWindow.loadFile('index.html');

  // Ouvrir les outils de développement
  mainWindow.webContents.openDevTools();

  // G<PERSON>rer quand la fenêtre est fermée
  mainWindow.on('closed', function() {
    mainWindow = null;
  });
}

// Créer la fenêtre quand Electron est prêt
app.whenReady().then(createWindow);

// Quitter quand toutes les fenêtres sont fermées
app.on('window-all-closed', function() {
  app.quit();
});